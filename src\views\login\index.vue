<template>
  <div class="login" @keyup.enter="handleLogin">
    <div class="login-title"></div>
    <div class="login-bg">
      <div class="login-main">
        <div class="login-tip">合肥市轨道交通</div>
        <h2 class="login-tip" bold>工程造价数据管理平台</h2>
        <div class="login-error">{{ errorMsg }}</div>
        <tiny-form
          ref="formRef"
          size="medium"
          :model="formData"
          :rules="rules"
          validate-type="text"
          label-width="0"
        >
          <tiny-form-item prop="username">
            <tiny-input v-model="formData.username" placeholder="请输入用户名" :prefix-icon="TinyIconUser" @blur="handleUsernameBlur"></tiny-input>
          </tiny-form-item>
          <tiny-form-item prop="password">
            <tiny-input v-model="formData.password" type="password" placeholder="请输入密码" show-password :prefix-icon="TinyIconLock" @blur="handlePasswordBlur"></tiny-input>
          </tiny-form-item>
          <tiny-form-item>
            <tiny-button class="login-btn" type="primary" @click="handleLogin">登录</tiny-button>
          </tiny-form-item>
        </tiny-form >
        <div style="display:flex;justify-content:space-between;">
          <span class="login-register">立即注册</span>
          <span class="login-forget">忘记密码?</span>
        </div>
        <!-- <tiny-divider class="login-divder"  content-position="center"  content-background-color="#fff">选择其他登录方式</tiny-divider> -->


      </div>
    </div>
    <div class="login-info">
      <span>版权所有 ©2022. 合肥市轨道交通集团有限公司 | 皖ICP备09026444号-1 | 皖公网安备34010302000589</span>
    </div>
    <div class="base-brand" @click="handleGoPage">皖ICP备18011150号-7</div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user.js'
import { iconUser, iconLock } from '@opentiny/vue-icon'
import {handleDynamicRoutes} from '@/router/index.js'
import { dictDatas } from '@/api/methods/dict.ts'
const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

const TinyIconUser = iconUser()
const TinyIconLock = iconLock()

const formRef = ref()
const formData = reactive({
	username: '',
	password: ''
})
const rules = reactive({})

const errorMsg = ref('')

const handleUsernameBlur = () => {
  if (!formData.username) {
    errorMsg.value = '请输入用户名'
  } else {
    errorMsg.value = ''
  }
}

const handlePasswordBlur = () => {
  if (!formData.password) {
    errorMsg.value = '请输入密码'
  } else {
    errorMsg.value = ''
  }
}

const handleLogin = async () => {
  handleUsernameBlur()
  handlePasswordBlur()
  if (errorMsg.value) return
  await userStore.login(formData.username, formData.password)

  // 请求菜单数据并注册路由,此时可获取首页路径
  await handleDynamicRoutes()
  const redirect = route.query.redirect
  router.push({ path: redirect || userStore.homePath })

  //获取字典缓存
  const dictQuery = await dictDatas()
  mainStore.setValue('dictData', dictQuery)
}

const handleGoPage = () => {
  window.open('https://beian.miit.gov.cn/#/Integrated/index', '_blank')
}
</script>

<style lang="less" scoped>
	.login{
		height: 100vh;
    overflow: hidden;
		width: 100%;
    background: #ffffff;
    position: relative;
    &-title{
      background: url("@/assets/images/image.png") no-repeat center center / cover;
      width: 214px;
      height: 50px;
      margin-left: 20%;
      margin-top: 30px;
      margin-bottom: 20px;
      top: 10%;
    }
    &-bg{
      height: 70%;
      min-height: 650px;
      background: url("@/assets/images/login_bg.png") no-repeat center center / cover;
      position: relative;
    }
    &-main{
      background-color: #fff;
      padding: 50px 70px;
      border-radius: 10px;
      width: 460px;
      height: 500px;
      position: absolute;
      left: 65%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
    &-tip{
      font-size: 24px;
      margin-bottom: 15px;
    }
    &-error{
      font-size: 16px;
      margin-bottom: 10px;
      color: var(--ti-base-color-error-4, #de504e);
      height: 20px;
      line-height: 20px;
    }
    &-btn{
      width: 100%;
      background: #0B31A7;
    }
    &-forget{
      font-size: 14px;
      text-align: right;
      cursor: pointer;
    }
    &-register{
      font-size: 14px;
      text-align: left;
      cursor: pointer;
    }
    &-info{
      margin-top: 20px;
      color: #bec6d4;
      text-align: center;
      font-size: 14px;
      span{
        margin: 0 10px;
      }
    }
    &-divder{
      font-size: 14px;
      text-align: center;
      
    }
    .base-brand{
      margin-top: 10px;
      font-size: 14px;
      text-align: center;
    }
	}
</style>
