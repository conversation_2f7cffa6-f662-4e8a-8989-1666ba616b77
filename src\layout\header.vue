<!--
* Description: 布局-header
* Created by huqingchao
* Date: 2024/10/29 18:17
* Update: 2024/10/29 18:17
-->
<template>
	<div class="header">
    <div class="sys-info">
      <div class="logo">
      </div>
      <span>基础模板框架</span>
    </div>
    <div class="header-right">
      <div class="change-font">
        <div class="change-font-item" :class="skinStore.size === 'large' ? 'is--active' : ''" @click="setSize('large')">大版</div>
        <div class="change-font-item" :class="skinStore.size === 'medium' ? 'is--active' : ''" @click="setSize('medium')">中版</div>
        <div class="change-font-item" :class="skinStore.size === 'small' ? 'is--active' : ''" @click="setSize('small')">小版</div>
      </div>
      <vxe-button trigger="hover" mode="text">
        <template #default>
          <vxe-icon name="user-fill"></vxe-icon>
          {{ userStore.userInfo.sname }}
        </template>
        <template #dropdowns>
          <vxe-button mode="text" content="修改密码" @click="handleOpenDialog"></vxe-button>
          <vxe-button mode="text" content="退出登录" @click="handleQuit"></vxe-button>
        </template>
      </vxe-button>
    </div>
    <vxe-modal
      title="修改密码"
      v-model="dialogVisible"
      :width="800">
      <vxe-form
        ref="formRef"
        title-align="right"
        title-width="120"
        :data="formData"
        :rules="formRules"
        @submit="submitEvent"
        @reset="resetEvent">
        <vxe-form-item title="原密码" field="oldPassword" span="24">
          <template #default>
            <vxe-input v-model="formData.oldPassword" placeholder="请输入当前密码" type="password"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="新密码" field="password" span="24">
          <template #title>
            <span style="margin-right: 5px">新密码</span>
            <vxe-tooltip content="密码为6-20字符；只能包含字母、数字以及标点符号（除空格）；包含字母、数字和标点符号至少包含2种。">
              <vxe-icon name="question-circle-fill" status="error"></vxe-icon>
            </vxe-tooltip>
          </template>
          <template #default>
            <vxe-input v-model="formData.password" placeholder="请输入新密码" type="password"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="确认新密码" field="passwordAgain" span="24">
          <template #default>
            <vxe-input v-model="formData.passwordAgain" placeholder="请再次输入新密码" type="password" @blur="handlePasswordBlur"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item align="right" span="24">
          <template #default>
            <vxe-button type="reset" content="取消"></vxe-button>
            <vxe-button type="submit" status="primary" content="确定"></vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user.js'
import { useSkinStore } from '@/stores/skin.ts'
import { editPassword } from '@/api/methods/user.js'
const userStore = useUserStore();
const skinStore = useSkinStore();
const router = useRouter()

const handleQuit = () => {
  userStore.quit()
  router.push({name: 'login'})
}

const dialogVisible = ref(false)

const formRef = ref(null)

const formData = reactive({
  oldPassword: '',
  password: '',
  passwordAgain: '',
})

const formRules = reactive({
  oldPassword: [
    { required: true, content: '必须填写' },
  ],
  password: [
    { required: true, content: '必须填写' },
    {
      validator ({ itemValue }) {
        if (itemValue && (itemValue.length < 6 || itemValue.length > 20)) {
          return new Error('密码为6-20字符')
        }
      }
    },
    { validator: validatePassword1 },
    { validator: validatePassword2 },
  ],
  passwordAgain: [
    { required: true, content: '必须填写' },
    { validator: validatePasswordAgain },
  ],
})

function validatePassword1({ itemValue }) {
  const reg = /^[A-Za-z0-9!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~]*$/
  if (itemValue && !reg.test(itemValue)) {
    return new Error('只能包含字母、数字以及标点符号（除空格）！')
  }
}

function validatePassword2({ itemValue }) {
  const reg = /^(?=.*[A-Za-z])(?=.*[0-9])|(?=.*[A-Za-z])(?=.*[!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~])|(?=.*[0-9])(?=.*[!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~]).*/
  if (itemValue && !reg.test(itemValue)) {
    return new Error('包含字母、数字和标点符号至少包含2种！')
  }
}

function validatePasswordAgain({ itemValue }) {
  if (formData.password && formData.passwordAgain && formData.password !== formData.passwordAgain) {
    return new Error('两次输入密码不一致！')
  }
}
// 打开修改弹框
const handleOpenDialog = () => {
  dialogVisible.value = true
  // 重置表单数据
  formRef.value?.reset()
}
// 校验两次密码是否一致
const handlePasswordBlur = () => {
  formRef.value.validateField('passwordAgain')
}

// 表单校验通过后
const submitEvent = () => {
  editPassword({
    oldPassword: formData.oldPassword,
    password: formData.password,
  }).then(res => {
    dialogVisible.value = false
    handleQuit()
  })
}
// 取消
const resetEvent = () => {
  dialogVisible.value = false
}
// 设置尺寸
const setSize = type => {
  document.documentElement.setAttribute('data-dx-ui-size', type)
  skinStore.setSize(type)
}
</script>

<style scoped lang="less">
  .header{
    height: var(--dx-ui-head-height, 60px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--dx-ui-border-color, #dcdfe6);
    .sys-info {
      display: flex;
      align-items: center;
      justify-content: center;
      background: #ffffff;
      color: var(--dx-ui-color-primary);
      font-size: var(--dx-ui-font-size-xl, 16px);
      font-weight: bold;
      padding-left: 20px;
      img {
        width: 30px;
        height: 30px;
        margin-right: 5px;
        vertical-align: middle;
      }
    }
    &-right{
      display: flex;
      justify-content: flex-end;
      align-items: center;
      font-size: var(--dx-ui-font-size-lg, 14px);
      margin-right: 20px;
      height: 100%;
      .change-font{
        display: flex;
        align-items: center;
        height: 90%;
        border-right: 1px solid var(--dx-ui-border-color, #dcdfe6);
        padding: 0 10px;
        &-item{
          margin-right: 10px;
          color: var(--dx-ui-color-primary);
          cursor: pointer;
          &.is--active{
            color: var(--ti-base-color-brand-6);
          }
        }
      }
      .vxe-button--dropdown{
        padding-left: 10px;
      }
      :deep(.vxe-button){
        font-size: var(--dx-ui-font-size-lg, 14px);
      }
    }
  }
</style>
