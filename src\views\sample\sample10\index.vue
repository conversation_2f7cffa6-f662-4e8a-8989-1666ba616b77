<template>
  <div class="examples-container">
    <vxe-button type="primary" @click="showUserInfoDialog">查看用户个人资料</vxe-button>
    <vxe-button type="primary" @click="showformTypeDialog">formType</vxe-button>
    <div class="button-group">
      <vxe-button @click="setDialogWidth('600px')">设置宽度为600px</vxe-button>
      <vxe-button @click="setDialogWidth('80%')">设置宽度为80%</vxe-button>
      <vxe-button @click="setDialogHeight('400px')">设置高度为400px</vxe-button>
      <vxe-button @click="setDialogHeight('70%')">设置高度为70%</vxe-button>
      <vxe-button @click="setDialogSize('900px', '500px')">设置尺寸为900px×500px</vxe-button>
    </div>
    <div class="button-group">
      <vxe-button @click="setPaneHeight(0, '150px', '100px')">设置第一个面板高度</vxe-button>
      <vxe-button @click="setPaneHeight(0, '300px', '100px')">增加第一个面板高度</vxe-button>
      <vxe-button @click="setPaneHeight(1, '250px', '100px')">设置第二个面板高度</vxe-button>
      <vxe-button @click="setPaneHeight(2, '350px', '100px')">设置第三个面板高度</vxe-button>
    </div>
    <div class="button-group">
      <vxe-button @click="setPaneExpanded(0, true)">展开第一个面板</vxe-button>
      <vxe-button @click="setPaneExpanded(0, false)">折叠第一个面板</vxe-button>
      <vxe-button @click="setPaneExpanded(1, true)">展开第二个面板</vxe-button>
      <vxe-button @click="setPaneExpanded(1, false)">折叠第二个面板</vxe-button>
      <vxe-button @click="setPaneExpanded(2, true)">展开第三个面板</vxe-button>
      <vxe-button @click="setPaneExpanded(2, false)">折叠第三个面板</vxe-button>
    </div>
    <div class="button-group">
      <vxe-tree ref="treeRef" v-bind="treeOptions"></vxe-tree>
    </div>

    <group-form-dialog v-model:group-options="groupOptions" ref="groupRef" @confirm="handleConfirm"/>
    <group-form-dialog v-model:group-options="formTypeOptions" ref="formTypeRef" @confirm="formTypeHandleConfirm"/>
  </div>
</template>

<script lang="tsx" setup>
import { ref, reactive } from 'vue'
import { VxeButton, VxeTreeInstance, VxeTreeProps } from 'vxe-pc-ui'
import GroupFormDialog from '@/components/GroupFormDialog.vue'

interface NodeVO {
  title: string
  id: string
  parentId?: string | null
}

const handleConfirm = (utils: any) => {
  console.log('utils', utils)

  utils.getFormById('1').setFormData({
    name: '赵六',
    username: 'zhaoliu',
    mobile: '13800138000',
    gender: '男',
    birthday: '1990-01-01',
    age: 33,
    nation: '汉族',
    idCard: '110101199001010000'
  })

  console.log('data', utils.getFormById('1'))
  // console.log('data', utils.getGridById('4'))
}

const formTypeHandleConfirm = (utils: any) => {
  console.log('utils', utils.getFormById('3').getFormData())
}



const treeRef = ref<VxeTreeInstance<NodeVO>>()

const treeOptions = reactive<VxeTreeProps>({
  transform: true,
  data: [
    { title: '节点2', id: '2', parentId: null },
    { title: '节点3', id: '3', parentId: null },
    { title: '节点3-1', id: '31', parentId: '3' },
    { title: '节点3-2', id: '32', parentId: '3' },
    { title: '节点3-2-1', id: '321', parentId: '32' },
    { title: '节点3-2-2', id: '322', parentId: '32' },
    { title: '节点3-3', id: '33', parentId: '3' },
    { title: '节点3-3-1', id: '331', parentId: '33' },
    { title: '节点3-3-2', id: '332', parentId: '33' },
    { title: '节点3-3-3', id: '333', parentId: '33' },
    { title: '节点3-4', id: '34', parentId: '3' },
    { title: '节点4', id: '4', parentId: null },
    { title: '节点4-1', id: '41', parentId: '4' },
    { title: '节点4-1-1', id: '411', parentId: '42' },
    { title: '节点4-1-2', id: '412', parentId: '42' },
    { title: '节点4-2', id: '42', parentId: '4' },
    { title: '节点4-3', id: '43', parentId: '4' },
    { title: '节点4-3-1', id: '431', parentId: '43' },
    { title: '节点4-3-2', id: '432', parentId: '43' },
    { title: '节点5', id: '5', parentId: null }
  ]
})

let data = 0;
const groupRef = ref()

const groupOptions = reactive({
  visible: false,
  title: '用户个人资料',
  width: '70%',  // 初始宽度，支持像素值或百分比
  height: '80%', // 初始高度，支持像素值或百分比
  items: [
    {
      // data的触发顺序是：data -> getData
      id: '1',
      type: 'form' as const,
      title: '#1基本信息',
      expanded: true,
      formType: 'view',
      maxHeight: '600px',
      minHeight: '500px',
      formOptions: {
        titleAlign: 'right',
        border: true,
        titleBackground: true,
        verticalAlign: 'center' as 'center',
        titleWidth: 100,
        items: [
          {
            field: 'name',
            title: '姓名',
            span: 24,
            itemRender: {
              name: 'VxeInput',
              props: {
                placeholder: '请输入姓名'
              },
              events: {
                click: (a, b) => {
                  console.log('a', a)
                  console.log('b', b)
                }
              }

            }
          },
          {
            field: 'username',
            title: '用户名',
            span: 24,
            itemRender: {
              name: 'VxeInput',
              props: {
                placeholder: '请输入用户名'
              }
            }
          },
          {
            field: 'mobile',
            title: '手机号',
            span: 24,
            itemRender: {
              name: 'VxeInput',
              props: {
                placeholder: '请输入手机号'
              }
            }
          },
          {
            field: 'gender',
            title: '性别',
            span: 24,
            itemRender: {
              name: 'VxeRadioGroup',
              options: [
                { label: '男', value: '男' },
                { label: '女', value: '女' }
              ]
            }
          },
          {
            field: 'birthday',
            title: '出生日期',
            span: 24,
            itemRender: {
              name: 'VxeDatePicker',
              props: {
                type: 'date',
                placeholder: '请选择出生日期'
              }
            }
          },
          {
            field: 'age',
            title: '年龄',
            span: 24,
            itemRender: {
              name: 'VxeInput',
              props: {
                type: 'number',
                min: 0,
                max: 150
              }
            }
          },
          {
            field: 'nation',
            title: '民族',
            span: 24,
            itemRender: {
              name: 'VxeTreeSelect',
              selectApi: {
                url: '/sysUser/queryUserByName',
                body: { name: '' },
                config: {
                  dataFieldName: 'data.name',
                  keyFieldName: 'title',
                  valueFieldName: 'sid',
                  childrenFieldName: 'children'
                }
              },
              props: {
                optionProps: {
                  label: 'label',
                  value: 'value'
                },
                options: {}
              }
            }
          },
          {
            field: 'idCard',
            title: '身份证号码',
            span: 24,
            itemRender: {
              name: 'VxeInput',
              props: {
                placeholder: '请输入身份证号码'
              }
            }
          }
        ],
        getData: () => {
          // alert('1')
          data = data + 1;
          console.log("data 5555===", data);
          return {
            name: '张三',
            username: 'zhangsan',
            mobile: '13800138000',
            gender: '男',
            birthday: '1990-01-01',
            age: 33,
            nation: '汉族',
            idCard: '110101199001010000'
          }
        },
        // data: {
        //   name: '11',
        //   username: '11',
        //   mobile: '11',
        //   gender: '',
        //   nation: ''
        // },
        rules: {
          name: [
            { required: true, validator: 'ValidName' }
          ],
          nickname: [
            { required: true, validator: 'ValidNikeName' }
          ]
        },
        formType: 'edit' as const,
        applyData: (data) => {
          console.log('保存基本信息数据', data)
          return Promise.resolve()
        }
      }
    },
    {
      id: '2',
      type: 'form' as const,
      title: '#2基本信息',
      expanded: true,
      maxHeight: '600px',
      minHeight: '500px',
      formOptions: {
        titleAlign: 'right',
        border: true,
        titleBackground: true,
        verticalAlign: 'center' as 'center',
        titleWidth: 100,
        items: [
          {
            field: 'name1',
            title: '姓名',
            span: 24,
            itemRender: {
              name: 'VxeInput',
              props: {
                placeholder: '请输入姓名'
              },
              events: {
                click: (a, b) => {
                  console.log('a', a)
                  console.log('b', b)
                  console.log('groupRef1', groupRef.value.getFormById('2').getFormData())
                  groupRef.value.getFormById('2').setFormData({
                    username1 : 55555
                  })
                  console.log('groupRef1  555', groupRef.value.getFormById('2').getFormData())

                }
              }
            }
          },
          {
            field: 'username1',
            title: '用户名',
            span: 24,
            itemRender: {
              name: 'VxeInput',
              props: {
                placeholder: '请输入用户名'
              }
            }
          },
          {
            field: 'mobile1',
            title: '手机号',
            span: 24,
            itemRender: {
              name: 'VxeInput',
              props: {
                placeholder: '请输入手机号'
              }
            }
          },
          {
            field: 'gender1',
            title: '性别',
            span: 24,
            itemRender: {
              name: 'VxeRadioGroup',
              options: [
                { label: '男', value: '男' },
                { label: '女', value: '女' }
              ]
            }
          },
          {
            field: 'birthday1',
            title: '出生日期',
            span: 24,
            itemRender: {
              name: 'VxeDatePicker',
              props: {
                type: 'date',
                placeholder: '请选择出生日期'
              }
            }
          },
          {
            field: 'age1',
            title: '年龄',
            span: 24,
            itemRender: {
              name: 'VxeInput',
              props: {
                type: 'number',
                min: 0,
                max: 150
              }
            }
          },
          {
            field: 'nation1',
            title: '民族',
            span: 24,
            itemRender: {
              name: 'VxeTreeSelect',
              selectApi: {
                url: '/sysUser/queryUserByName',
                body: { name: '' },
                config: {
                  keyFieldName: 'title',
                  valueFieldName: 'sid',
                  childrenFieldName: 'children'
                }
              },
              props: {
                optionProps: {
                  label: 'label',
                  value: 'value'
                },
                options: {}
              }
            }
          },
          {
            field: 'idCard1',
            title: '身份证号码',
            span: 24,
            itemRender: {
              name: 'VxeInput',
              props: {
                placeholder: '请输入身份证号码'
              }
            }
          }
        ],
        getData: () => {
          return {
            name1: '张三',
            username1: 'zhangsan',
            mobile1: '13800138000',
            gender1: '男',
            birthday1: '1990-01-01',
            age1: 33,
            nation1: '汉族',
            idCard1: '110101199001010000'
          }
        },
        data: {
          name1: '11',
          // username1: '11',
          mobile1: '11',
          gender1: '',
          nation1: '',
          idCard1: '110101199001010000',
          age1: 33,
        },
        rules: {
          name1: [
            { required: true, validator: 'ValidName' }
          ],
          username1: [
            { required: true, validator: 'ValidNikeName' }
          ]
        },
        formType: 'edit' as const,
        applyData: (data) => {
          console.log('保存基本信息数据', data)
          return Promise.resolve()
        }
      }
    },
    {
      id: '3',
      type: 'form' as const,
      title: '#2工作经验',
      expanded: true,
      maxHeight: '200px',
      minHeight: '100px',
      formOptions: {
        titleAlign: 'right',
        border: true,
        titleBackground: true,
        verticalAlign: 'center' as 'center',
        titleWidth: 100,
        items: [
          {
            field:'test',
            span: 24,
            itemRender: {
              titleWidth: "100px",
              name:'VxeForm',
              props:{
                width: "100%",
                items:[
                { field: 'name', title: '名称', span: 24, itemRender: { name: 'input' } },
                { field: 'nickname', title: '文本框', span: 24, itemRender: { name: 'input' } },
                { field: 'age', title: '数字', span: 24, itemRender: { name: 'input', attrs: { type: 'number' } } },
              ],
              }
            }
          },
          // {
          //   field: 'test1',
          //   span: 24,
          //   itemRender: {
          //     name: 'VxeForm',
          //     props: {
          //       width: "100%",
          //       titleWidth: "100px",
          //       titleBackground: true,
          //       items: [
          //         { field: 'nameGZJY', title: '名称', span: 24, itemRender: { name: 'input' } },
          //         { field: 'nicknameGZJY', title: '文本框', span: 24, itemRender: { name: 'input' } },
          //         { field: 'ageGZJY', title: '数字', span: 24, itemRender: { name: 'input', attrs: { type: 'number' } } },
          //       ],
          //     }
          //   }
          // },

          {
            field: 'companyGZJY',
            title: '公司名称',
            span: 12,
            itemRender: {
              name: 'VxeTreeSelect',
              selectApi: {
                url: '/sysDept/getDeptTreeList',
                body: { },
                config: {
                  keyFieldName: 'deptName',
                  valueFieldName: 'deptId',
                  childrenFieldName: 'children'
                }
              },
            }
          },
          {
            field: 'positionGZJY',
            title: '职位',
            span: 12,
            itemRender: {
              name: 'VxeInput',
              props: {
                placeholder: '请输入职位'
              }
            }
          },
          {
            field: 'startDateGZJY',
            title: '开始日期',
            span: 12,
            itemRender: {
              name: 'VxeDatePicker',
              props: {
                type: 'date',
                placeholder: '请选择开始日期'
              }
            }
          },
          {
            field: 'endDateGZJY',
            title: '结束日期',
            span: 12,
            itemRender: {
              name: 'VxeDatePicker',
              props: {
                type: 'date',
                placeholder: '请选择结束日期'
              }
            }
          },
          {
            field: 'descriptionGZJY',
            title: '工作描述',
            span: 24,
            itemRender: {
              name: 'VxeTextarea',
              props: {
                placeholder: '请输入工作描述',
                rows: 3
              }
            }
          }
        ],
        formType: 'view' as const,
        getData: () => {
          return {
            // code: 200,
            // data: {
              companyGZJY: '1879063691215900672',
              positionGZJY: '前端开发工程师',
              startDateGZJY: '2019-01-01',
              endDateGZJY: '2020-12-31',
              descriptionGZJY: '负责公司前端项目的开发与维护，使用Vue.js技术栈开发企业级应用。'
            // }
          }
        },
        applyData: (data) => {
          console.log('保存工作经验数据', data)
          return Promise.resolve()
        }
      },
    },
    {
      id: '4',
      type: 'grid' as const,
      title: '测试表格',
      maxHeight: '500px',
      minHeight: '300px',
      expanded: true,
      gridOptions: {
        border: true,
        height: 'auto',
        columns: [
          { type: 'seq', width: 70, fixed: 'left', align: 'center' },
          { field: 'name', title: '姓名', minWidth: 120 },
          { field: 'age', title: '年龄', minWidth: 120 },
          { field: 'sex', title: '性别', minWidth: 120 },
        ],
        data: [
          { name: '张三', age: 18, sex: '男' },
          { name: '李四', age: 19, sex: '女' },
          { name: '王五', age: 20, sex: '男' },
        ]
      }
    },
    {
      id: '5',
      type: 'grid' as const,
      title: '测试表格formInGrid',
      maxHeight: '500px',
      minHeight: '300px',
      expanded: true,
      gridOptions: {
        border: true,
        height: 'auto',
        columns: [
          { type: 'seq', width: 70, fixed: 'left', align: 'center' },
          { field: 'name', title: '姓名', minWidth: 120, cellRender:{
            name: 'VxeForm',
              props: {
                items: [
                  { field: 'nameForm', title: '名称', span: 8, itemRender: { name: 'VxeInput' } },
                  { field: 'nicknameForm', title: '文本框', span: 8, itemRender: { name: 'VxeInput' } },
                  { field: 'ageForm', title: '数字', span: 8, itemRender: { name: 'VxeInput', attrs: { type: 'number' } } },
                ],
              }
            }
          },
          // { field: 'age', title: '年龄', minWidth: 120 },
          // { field: 'sex', title: '性别', minWidth: 120 },
        ],
        data: [
          { name: '张三', age: 18, sex: '男' },
          { name: '李四', age: 19, sex: '女' },
          { name: '王五', age: 20, sex: '男' },
        ]
      }
    }
  ],
  tools: [
    {
      position: 'left',
      marign: {
        left: '10px',
        right: '10px',
        top: '10px',
        bottom: '10px'
      },
      width: '8%',
      name: 'VxeButton',
      props: {
        type: 'primary',
        content: '搜索',
        icon: 'vxe-icon-search'
      },
      events: {
        // 参数1:表单数据,参数2:表单实例
        click(formData, formRef) {
          console.log('搜索', formRef.formRefs.get(0).getFormData())
          console.log('搜索', formRef.gridRefs.get(0).getGridData())
        }
      }
    },
    {
      position: 'left',
      marign: {
        left: '10px',
        right: '10px',
        top: '10px',
        bottom: '10px'
      },
      width: '8%',
      name: 'VxeButton',
      props: {
        type: 'primary',
        content: '折叠'
      },
      events: {
        // 参数1:表单数据,参数2:表单实例
        click(formData, formRef) {
          formRef.collapseForm()
        }
      }
    }
  ]
})

// 显示对话框
const showUserInfoDialog = () => {
  // 方法一：通过组件实例方法设置可见性
  // groupRef.value.setVisiable(true);

  // 方法二：直接修改 reactive 对象的属性（两种方法都可以）
  groupOptions.visible = true;
}

const showformTypeDialog = () => {
  // 方法一：通过组件实例方法设置可见性
  // groupRef.value.setVisiable(true);

  // 方法二：直接修改 reactive 对象的属性（两种方法都可以）
  formTypeOptions.visible = true;
}



// 设置对话框宽度
const setDialogWidth = (width) => {
  // 方法一：通过组件实例方法设置宽度
  groupRef.value.setWidth(width);

  // 方法二：直接修改 reactive 对象的属性
  // groupOptions.width = width;
}

// 设置对话框高度
const setDialogHeight = (height) => {
  // 方法一：通过组件实例方法设置高度
  groupRef.value.setHeight(height);

  // 方法二：直接修改 reactive 对象的属性
  // groupOptions.height = height;
}

// 同时设置对话框宽度和高度
const setDialogSize = (width, height) => {
  // 方法一：通过组件实例方法同时设置宽度和高度
  groupRef.value.setSize(width, height);

  // 方法二：直接修改 reactive 对象的属性
  // groupOptions.width = width;
  // groupOptions.height = height;
}

// 设置面板的最大高度和最小高度
const setPaneHeight = (index, maxHeight, minHeight) => {
  // 方法一：通过组件实例方法设置面板高度
  groupRef.value.setPaneHeight(index, maxHeight, minHeight);

  // 方法二：直接修改 reactive 对象的属性
  // if (index >= 0 && index < groupOptions.items.length) {
  //   // 创建新的 items 数组，以保持响应性
  //   const newItems = [...groupOptions.items];
  //
  //   // 更新指定索引的面板高度
  //   newItems[index] = {
  //     ...newItems[index],
  //     maxHeight,
  //     minHeight
  //   };
  //
  //   // 更新 groupOptions
  //   groupOptions.items = newItems;
  // }
}

// 设置面板的展开状态
const setPaneExpanded = (index, expanded) => {
  // 方法一：通过组件实例方法设置面板展开状态
  groupRef.value.setPaneExpanded(index, expanded);

  // 方法二：直接修改 reactive 对象的属性
  // if (index >= 0 && index < groupOptions.items.length) {
  //   // 创建新的 items 数组，以保持响应性
  //   const newItems = [...groupOptions.items];
  //
  //   // 更新指定索引的面板展开状态
  //   newItems[index] = {
  //     ...newItems[index],
  //     expanded
  //   };
  //
  //   // 更新 groupOptions
  //   groupOptions.items = newItems;
  // }
}

const dialogOptions = reactive({
  visible: false,
  title: '用户信息',
  width: '60%',
  height: '50%',
  layout: [
    { x: 0, y: 0, w: 4, h: 1, i: '0' },
    { x: 4, y: 0, w: 4, h: 1, i: '1' },
  ],
  dialogOptions: new Map([
    [
      '0',
      {
        // 表单项配置数组
        items: [
          {
            field: 'cardType',
            // title: '证件类型',
            span: 10,
            itemRender: {
              name: 'VxeRadioGroup',
              // dict: '0',
              // 如果使用字典,则 option 默认失效
              // selectApi: {
              //     url: '/sys/temp/list',
              //     body: {},
              //     config: {
              //         keyFieldName: 'id',
              //         valueFieldName: 'name'
              //     }
              // },
              options: [
                { label: '身份证', value: '1' },
                { label: '护照', value: '2' }
              ],
              props: {
                placeholder: '请选择证件类型',
              },
              events: {
                change(value, formData, formRef) {
                  console.log(value, formData)
                  console.log(formRef)
                  formRef.submitForm()
                  currentType.value = value;
                  formRef.setFieldVisible('workCode', value == '1');

                }
              },
            },
            // visible: false
          },
          {
            field: 'username',
            // title: '证件类型',
            span: 8,
            // itemRender: {
            //     name: 'VxeInput',
            //     props: {
            //         placeholder: '请选择证件类型',
            //     },
            //     events: {
            //         change(value, formData, formRef) {
            //             // console.log(value, formData)
            //         }
            //     }
            // },
            itemRenders: [
              {
                name: 'VxeInput',
                props: {
                  placeholder: '请选择证件类型',
                },
                events: {
                  change(value, formData, formRef) {
                    // console.log(value, formData)
                  }
                }
              },
              {
                name: 'VxeSelect',
                // dict: '0',
                selectApi: {
                  url: '/sys/temp/list',
                  body: {},
                  config: {
                    keyFieldName: 'id',
                    valueFieldName: 'name'
                  }
                },
                events: {
                  // 参数1:表单数据,参数2:表单实例,参数3:组件实例
                  onchange(value, formData, formRef) {
                    console.log(value)
                  }
                }
              }
            ]
          },
          //这里为表单得字段按钮.
          {
            field: 'workCode',
            span: 6,
            itemRender: {
              name: 'VxeButton',
              props: {
                type: 'primary',
                content: '测试按钮'
              },
              events: {
                // 参数1:表单数据,参数2:表单实例,参数3:组件实例
                click(value, formData, formRef) {
                  console.log(value)
                  alert('点击了')

                }
              }
            },
            // 这个函数仅在第一次加载的时候 生效,后面无论怎么修改都不生效
            visibleEvent: (data) => {
              // 获取表单内的数据
              console.log("visibleEvent", data)
              return currentType.value == '1';
            }
          },
        ],
        // 表单类型 这里决定了表格上面得工具栏是否能自动获取值
        formType: 'edit',
        // 获取数据的配置
        getData: () => {
          //这里支持 返回接口数据
          return {
            cardType: '1',
            workCode: '2',
            username: '张三',
          }
        },
        // 提交数据的函数
        applyData: async (data) => {
          // 这里可以实现提交数据的逻辑
          console.log('提交的数据:', data)
        },
        // 其他vxe-form的配置项
        titleWidth: 'auto',
        titleAlign: 'right',
        titleColon: true,
        titleOverflow: true,
        // 表单布局
        layout: 'inline',
        // 表单项间距
        itemGap: 10,
        // 是否显示冒号
        colon: true,
        // 是否显示必填星号
        showRequiredMark: true,
        // 校验规则
        rules: {
          sname: [
            { required: true, message: '请输入用户名' }
          ],
          phone: [
            { required: true, message: '请输入手机号' }
          ]
        },
        // 是否只读
        readonly: false,
      }
    ],
    [
      '1',
      {
        headerAlign: 'center',//表头对齐方式
        border: true,//是否显示边框
        scrollY: { enabled: false },//是否开启滚动
        treeConfig: {
          rowField: 'menuId',//行唯一标识
          childrenField: 'children'//子节点字段名
        },
        columns: [
          { type: 'seq', width: "14%", fixed: 'left', align: 'center' },
          {
            field: 'action',//字段名
            title: '操作',//标题
            width: '20%',//宽度
            cellRenders: [//单元格渲染
              {
                name: 'VxeIcon',
                props: {
                  size: 'medium',//组件属性
                  name: 'setting',//组件属性
                  width: '33%',//组件属性
                  status: 'primary'//组件属性
                },
                events: {
                  click(cellParams, params) {
                    VxeUI.modal.message({
                      content: '点击了',
                      status: 'success'
                    })
                  }
                }
              },
              {
                name: 'VxeIcon',
                props: {
                  size: 'medium',
                  name: 'home',
                  width: '33%',
                  status: 'success'
                },
                events: {
                  click(cellParams, params) {
                    VxeUI.modal.message({
                      content: '点击了',
                      status: 'success'
                    })
                  }
                }
              },
              {
                name: 'VxeIcon',
                props: {
                  size: 'medium',
                  name: 'home',
                  width: '33%',
                  status: 'warning'
                },
                events: {
                  click(cellParams, params) {
                    VxeUI.modal.message({
                      content: '点击了',
                      status: 'success'
                    })
                  }
                }
              }
            ]
          },
          {
            field: 'sname', title: '菜单名称', width: '30%', treeNode: true,//字段名,标题,宽度,是否树节点
            cellRender: {
              name: 'VxeText',//组件名称
              events: {
                async click(cellParams, params) {
                  console.log(cellParams.row)
                  //根据deptId查询人员信息
                  let res = await getDeptUserList({ deptId: cellParams.row.deptId, queryChild: "false" })
                  console.log(res.data.records)
                  // gridOptions.data = res.data.records
                }
              }
            }
          },
          { field: 'icon', title: '菜单类型', width: '18%' },
          {
            field: 'url', title: '菜单分类', width: '18%',
            // visible:false,
            visibleEvent: (data) => {
              return true
            }
          },
        ],
        height: "auto",
        proxyConfig: {
          sort: true,//是否开启排序
          form: true,//是否开启表单
          response: {
            result: 'data',//返回数据
          },
          ajax: {
            async query({ page, form, sorts }) {
              const params = {
                ...page,
                ...form
              }
              let res = await getList(params)
              return res.data.list
            }
          }
        }
      }
    ],
  ])
})

const formTypeOptions = reactive({
  visible: false,
  title: '用户个人资料',
  width: '70%',  // 初始宽度，支持像素值或百分比
  height: '80%', // 初始高度，支持像素值或百分比
  items: [
    {
      // data的触发顺序是：data -> getData
      id: '1',
      type: 'form' as const,
      title: '#1基本信息',
      expanded: true,
      formType: 'view',
      maxHeight: '300px',
      minHeight: '100px',
      formOptions: {
        titleAlign: 'right',
        border: true,
        titleBackground: true,
        verticalAlign: 'center' as 'center',
        titleWidth: 100,
        items: [
          {
            field: 'ceshi1',
            title: '测试一',
            span: 24,
            itemRender: {
              name: 'VxeInput',
              props: {
                placeholder: '请输入姓名'
              },
            }
          },
          {
            field: 'ceshi2',
            title: '测试2',
            span: 24,
            itemRender: {
              name: 'VxeInput',
              props: {
                placeholder: '请输入用户名'
              }
            }
          },
        ],
        data: {
          ceshi1: 'ceshi1',
          ceshi2: 'ceshi2'
        },
        formType: 'add' as const,
        applyData: (data) => {
          console.log('保存基本信息数据', data)
          return Promise.resolve()
        }
      }
    },
    {
      // data的触发顺序是：data -> getData
      id: '2',
      type: 'form' as const,
      title: '#2基本信息',
      expanded: true,
      formType: 'view',
      maxHeight: '300px',
      minHeight: '100px',
      formOptions: {
        titleAlign: 'right',
        border: true,
        titleBackground: true,
        verticalAlign: 'center' as 'center',
        titleWidth: 100,
        items: [
          {
            field: 'ceshi3',
            title: '测试三',
            span: 24,
            itemRender: {
              name: 'VxeInput',
              props: {
                placeholder: '请输入姓名'
              },
            }
          },
          {
            field: 'ceshi4',
            title: '测试四',
            span: 24,
            itemRender: {
              name: 'VxeInput',
              props: {
                placeholder: '请输入用户名'
              }
            }
          },
        ],
        data: {
          ceshi3: 'ceshi3',
          ceshi4: 'ceshi4'
        },
        formType: 'view' as const,
        applyData: (data) => {
          console.log('保存基本信息数据', data)
          return Promise.resolve()
        }
      }
    },
    {
      // data的触发顺序是：data -> getData
      id: '3',
      type: 'form' as const,
      title: '#3基本信息',
      expanded: true,
      formType: 'view',
      maxHeight: '300px',
      minHeight: '100px',
      formOptions: {
        titleAlign: 'right',
        border: true,
        titleBackground: true,
        verticalAlign: 'center' as 'center',
        titleWidth: 100,
        items: [
          {
            field: 'ceshi5',
            title: '测试五',
            span: 24,
            itemRender: {
              name: 'VxeInput',
              props: {
                placeholder: '请输入姓名'
              },
            }
          },
          {
            field: 'ceshi6',
            title: '测试六',
            span: 24,
            itemRender: {
              name: 'VxeInput',
              props: {
                placeholder: '请输入用户名'
              }
            },
            visible: false
          },
        ],
        data: {
          ceshi5: 'ceshi5',
          ceshi6: 'ceshi6'
        },
        rules: {
          ceshi5:[
            { required: true, message: '必须填写' }
          ]
        },
        formType: 'edit' as const,
        applyData: (data) => {
          console.log('保存基本信息数据', data)
          return Promise.resolve()
        }
      }
    },
  ]
})


</script>

<style scoped>
.examples-container {
  padding: 20px;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
  margin-bottom: 15px;
}

.button-group .vxe-button {
  margin-right: 0;
}
</style>
