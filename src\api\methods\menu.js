/**
 * Description: menu.js
 * Created by huqingchao
 * Date: 2024/10/25 09:33
 * Update: 2024/10/25 09:33
 */
import request from '../index.js'

export const getList = function (data) {
  return request({
    url: '/sysMenu/getList',
    method: 'post',
    data
  })
}


export const deleteById = function (data) {
  return request({
    url: '/sysMenu/deleteById',
    method: 'post',
    data
  })
}

export const getInfoById = function (data) {
  return request({
    url: '/sysMenu/getInfoById',
    method: 'post',
    data
  })
}

export const getListBtn = function (data) {
  return request({
    url: '/sysMenuBtn/getList',
    method: 'post',
    data
  })
}

export const saveOrUpdateBtn = function (data) {
  return request({
    url: '/sysMenuBtn/saveOrUpdate',
    method: 'post',
    data
  })
}

export const deleteByIdBtn = function (data) {
  return request({
    url: '/sysMenuBtn/deleteById',
    method: 'post',
    data
  })
}

export const getInfoByIdBtn = function (data) {
  return request({
    url: '/sysMenuBtn/getInfoById',
    method: 'post',
    data
  })
}

export const sysMenuTabGetList = function (data) {
  return request({
    url: '/sysMenuTab/getList',
    method: 'post',
    data
  })
}

export const sysMenuTabSaveOrUpdate = function (data) {
  return request({
    url: '/sysMenuTab/saveOrUpdate',
    method: 'post',
    data
  })
}

export const sysMenuTabDeleteById = function (data) {
  return request({
    url: '/sysMenuTab/deleteById',
    method: 'post',
    data
  })
}

export const sysMenuTabGetInfoById = function (data) {
  return request({
    url: '/sysMenuTab/getInfoById',
    method: 'post',
    data
  })
}

export const sysMenuTabBtnGetList = function (data) {
  return request({
    url: '/sysMenuTabBtn/getList',
    method: 'post',
    data
  })
}

export const sysMenuTabBtnSaveOrUpdate = function (data) {
  return request({
    url: '/sysMenuTabBtn/saveOrUpdate',
    method: 'post',
    data
  })
}

export const sysMenuTabBtnDeleteById = function (data) {
  return request({
    url: '/sysMenuTabBtn/deleteById',
    method: 'post',
    data
  })
}

export const sysMenuTabBtnGetInfoById = function (data) {
  return request({
    url: '/sysMenuTabBtn/getInfoById',
    method: 'post',
    data
  })
}

//新增,删除,修改菜单
export const saveOrUpdateEntity = function (data) {
  return request({
    url: '/sysMenu/saveOrUpdateEntity',
    method: 'post',
    data
  })
}
//新增删除修改菜单按钮
export const saveOrUpdate = function (data) {
  return request({
    url: '/sysMenuBtn/saveOrUpdate',
    method: 'post',
    data
  })
}
//获取菜单按钮
export const getMenuBtnList = function (data) {
  return request({
    url: '/sysMenuBtn/getList',
    method: 'post',
    data
  })
}
//获取菜单卡片列表
export const getMenuTabList = function (data) {
  return request({
    url: '/sysMenuTab/getList',
    method: 'post',
    data
  })
}
//新增删除修改tab接口
export const saveOrUpdateMenuTabList = function (data) {
  return request({
    url: '/sysMenuTab/saveOrUpdate',
    method: 'post',
    data
  })
}

//获取菜单卡片tab按钮列表接口
export const getMenuTabBtnList = function (data) {
  return request({
    url: '/sysMenuTabBtn/getList',
    method: 'post',
    data
  })
}
//新增删除修改tab按钮接口
export const saveOrUpdateMenuTabBtnList = function (data) {
  return request({
    url: '/sysMenuTabBtn/saveOrUpdate',
    method: 'post',
    data
  })
}

