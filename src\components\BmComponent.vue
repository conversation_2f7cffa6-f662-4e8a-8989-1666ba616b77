<template>
  <div class="bm-component">
    <vxe-form :data="formData" @submit="submitForm" @reset="resetForm">
      <vxe-form-item field="name" title="名称" :item-render="{}">
        <template #default="{ data }">
          <vxe-input v-model="data.name" placeholder="请输入名称"></vxe-input>
        </template>
      </vxe-form-item>
      
      <vxe-form-item field="type" title="类型" :item-render="{}">
        <template #default="{ data }">
          <vxe-select v-model="data.type" placeholder="请选择类型">
            <vxe-option value="1" label="类型一"></vxe-option>
            <vxe-option value="2" label="类型二"></vxe-option>
            <vxe-option value="3" label="类型三"></vxe-option>
          </vxe-select>
        </template>
      </vxe-form-item>
      
      <vxe-form-item field="description" title="描述" :item-render="{}">
        <template #default="{ data }">
          <vxe-textarea v-model="data.description" placeholder="请输入描述" :rows="3"></vxe-textarea>
        </template>
      </vxe-form-item>

      <vxe-form-item align="center" :item-render="{name: 'EditSubmitBtn'}"></vxe-form-item>
    </vxe-form>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { VxeFormProps, VxeFormItemProps } from 'vxe-pc-ui'

export default defineComponent({
  name: 'BmComponent',
  setup() {
    const state = reactive({
      formData: {
        name: '',
        type: '',
        description: ''
      }
    })

    const submitForm = () => {
      console.log('表单提交', state.formData)
      // 实际提交处理逻辑
    }

    const resetForm = () => {
      console.log('表单重置')
    }

    return {
      ...toRefs(state),
      submitForm,
      resetForm
    }
  }
})
</script>

<style scoped>
.bm-component {
  padding: 20px;
}
</style>
