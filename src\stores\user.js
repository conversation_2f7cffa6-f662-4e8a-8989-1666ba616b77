/**
 * Description: 用户信息store.js
 * Created by huqingchao
 * Date: 2024/10/17 15:15
 * Update: 2024/10/17 15:15
 */
import { defineStore } from 'pinia'
import request from '@/api/index.js'
import { resetRouter } from '@/router/index.js'
import { login as loginApi } from '@/api/methods/user.js'
export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '', // token
    userInfoStr: localStorage.getItem('userInfo') || '', // 用户信息str
    userMenus: [], // 用户menus
  }),
  getters: {
    userInfo(state) {
      return state.userInfoStr ? JSON.parse(state.userInfoStr) : {}
    },
    homePath(state) {
      return state.userMenus[0] ? state.userMenus[0].path :　'/model'
    }
  },
  actions: {
    // 登录
    async login(username, password) {
      const { data } = await loginApi({
        username,
        password,
      })
      this.setToken(data.token)
      this.setUserInfo(data.userInfo)
    },
    // 退出
    quit(){
      this.token = ''
      this.userInfoStr = ''
      this.userMenus = []
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      request.defaults.headers.common['Token'] = ''
      // 重置路由
      resetRouter()
    },
    // 设置token
    setToken(token) {
      this.token = token
      request.defaults.headers.common['Token'] = token
      localStorage.setItem('token', token)
    },
    // 设置用户信息
    setUserInfo(userInfo) {
      const info = JSON.stringify(userInfo)
      this.userInfoStr = info
      localStorage.setItem('userInfo', info)
    },
    // 设置用户菜单
    setUserMenus(menus) {
      this.userMenus = menus
    }
  },
})
