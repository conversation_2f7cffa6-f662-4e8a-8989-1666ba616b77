<!-- 布局组件演示项目 -->
<template>
  <BmGridLayout :layout="layout" :col-num="12" :is-draggable="false" :is-resizable="false" :vertical-compact="false"
                :margin="[0, 0]" :use-css-transforms="true" :auto-size="true" fullscreen @size-change="handleSizeChange">
    <BmGridItem v-bind="layout[0]">
      <bm-grid :grid-options="gridOptions1"></bm-grid>
    </BmGridItem>
    <BmGridItem v-bind="layout[1]">
      <bm-grid :grid-options="gridOptions"></bm-grid>
    </BmGridItem>
  </BmGridLayout>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import BmGridLayout from '@/components/gridlayout/BmGridLayout.vue'
import BmGridItem from '@/components/gridlayout/BmGridItem.vue'
import BmGrid from '@/components/BmGrid.vue'
import { ExtendedGridProps } from '@/types/grid'
import { VxeUI, VxeFormItemPropTypes, VxeSelectProps, VxeFormProps, VxeUploadPropTypes, VxeButton } from 'vxe-pc-ui'

const layout = reactive([
  // 菜单区域
  { x: 0, y: 0, w: 1.99, h: 1, i: '0' },
  // 表格区域
  { x: 2, y: 0, w: 10, h: 1, i: '1' },
])

// 监听尺寸变化
const handleSizeChange = (size) => {
  console.log(size)
}

const gridOptions = reactive<ExtendedGridProps>({
  border: true,//是否显示边框
  headerAlign: 'center',//表头对齐方式
  showOverflow: true,//是否显示溢出
  height: 'auto',//高度
  scrollY: { enabled: false },//是否开启滚动
  id: 'UserManageList',//表格唯一标识
  keepSource: true,//是否保持原始数据
  customConfig: {
    storage: true//是否开启本地存储
  },
  rowConfig: {
    isHover: true,//是否开启行高亮
    keyField: '_id'//行唯一标识
  },
  sortConfig: {
    remote: true,
    multiple: true
  },
  editConfig: {
    mode: 'row',//编辑模式
    showStatus: true,//是否显示编辑状态
    enabled: VxeUI.permission.checkVisible('userManageActionInsert|userManageActionUpdate'),//是否开启编辑
    beforeEditMethod({ row, column }) {

      return true
    }
  },
  checkboxConfig: {
    checkMethod({ row }) {
      return true
    }
  },
  toolbarConfig: {
    refresh: false,
    zoom: true,
    buttons: [
      // { name: '新增', code: 'insert_edit', status: 'primary', icon: 'vxe-icon-add', permissionCode: 'userManageActionInsert' },
      // { name: '标记/删除', code: 'mark_cancel', status: 'error', icon: 'vxe-icon-delete', permissionCode: 'userManageActionDelete' },
      // { name: '保存', code: 'save', status: 'success', icon: 'vxe-icon-save', permissionCode: 'userManageActionInsert|userManageActionDelete|userManageActionUpdate' }
    ]
  },
  editRules: {
    name: [
      { required: true, message: '请输入用户名' }
    ],
    roleCodes: [
      { required: true, type: 'array', message: '至少需要授权一个角色' }
    ]
  },
  formConfig: {
    titleWidth: 80,//标题宽度
    titleAlign: 'right',//标题对齐方式
    items: [
      // 隐藏用户名
      { field: 'name', title: '用户名', span: 6, itemRender: { name: 'VxeInput', props: { clearable: true } }, visible: true },
      // 其他字段显示
      { field: 'nickname', title: '昵称', span: 6, itemRender: { name: 'VxeInput', props: { clearable: true } } },
      { field: 'email', title: '邮箱', span: 6, itemRender: { name: 'VxeInput', props: { clearable: true } } },
      { field: 'startDate', title: '开始时间', span: 6, folding: true, itemRender: { name: 'VxeDatePicker', props: { clearable: true } } },
      { field: 'endDate', title: '结束时间', span: 6, folding: true, itemRender: { name: 'VxeDatePicker', props: { clearable: true } } },
      {
        field: 'testSelect', title: '测试下拉', span: 6, folding: true,
        itemRender: {
          name: 'VxeSelect', props: { clearable: true },
          options: [
            { label: '女', value: 'Women' },
            { label: '男', value: 'Men' }
          ],
          // selectApi: 'http://172.16.0.172:8083/sys/temp/list',//下拉选项API
          // dict: 'test_select',//字典配置
        }
      },
      { span: 24, align: 'right', collapseNode: true, itemRender: { name: 'ListSearchBtn' } }
    ]
  },
  pagerConfig: {},
  columns: [
    { type: 'checkbox', width: 60 },
    { type: 'seq', width: 70 },
    { field: 'code', title: '用户编码', width: 300, visible: false },
    { field: 'name', title: '用户名', minWidth: 160, sortable: true },
    { field: 'nickname', title: '昵称', minWidth: 220 },
    { field: 'pictureUrl', title: '头像', width: 120 },
    { field: 'email', title: '邮箱', minWidth: 220 },
    { field: 'createTime', title: '注册时间', width: 160, sortable: true },
    { field: 'updateTime', title: '最后更新时间', width: 160, sortable: true },
    { field: 'action', title: '操作', fixed: 'right', width: 80 }
  ],
  data: [
    {
      _id: '1',
      code: 'EMP001',
      name: '张三',
      nickname: '小张',
      email: '<EMAIL>',
      pictureUrl: '1',
      createTime: '2024-01-15 09:00:00',
      updateTime: '2024-01-15 09:00:00',
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      testSelect: 'Men'
    },
    {
      _id: '2',
      code: 'EMP002',
      name: '李四',
      nickname: '小李',
      email: '<EMAIL>',
      pictureUrl: '1',
      createTime: '2024-01-15 10:00:00',
      updateTime: '2024-01-15 10:00:00',
      startDate: '2024-02-01',
      endDate: '2024-12-31',
      testSelect: 'Women'
    },
    {
      _id: '3',
      code: 'EMP003',
      name: '王五',
      nickname: '小王',
      email: '<EMAIL>',
      pictureUrl: '1',
      createTime: '2024-01-15 11:00:00',
      updateTime: '2024-01-15 11:00:00',
      startDate: '2024-03-01',
      endDate: '2024-12-31',
      testSelect: 'Men'
    },
    {
      _id: '4',
      code: 'EMP004',
      name: '赵六',
      nickname: '小赵',
      email: '<EMAIL>',
      pictureUrl: '4',
      createTime: '2024-01-15 12:00:00',
      updateTime: '2024-01-15 12:00:00',
      startDate: '2024-04-01',
      endDate: '2024-12-31',
      testSelect: 'Women'
    }
  ],
  // proxyConfig: {
  //   sort: true,
  //   form: true,
  //   ajax: {
  //     query({ page, form, sorts }) {
  //         const params = {
  //             ...page,
  //             ...form,
  //             roleCodes: form.roleCodes ? form.roleCodes.join(',') : '',
  //             orderBy: sorts.map(item => `${item.field}|${item.order}`).join(',')
  //         }
  //         return getPubAdminUserListPage(params)
  //     },
  //     save({ body }) {
  //         return postPubAdminUserSaveBatch(body)
  //     }
  //   }
  // }
})

const gridOptions1 = reactive<ExtendedGridProps>({
  headerAlign: 'center',
  border: false,
  scrollY: { enabled: false },
  treeConfig: {
    rowField: 'id',
    childrenField: 'children'
  },
  columns: [
    { field: 'name', title: '部门', width: '30%', treeNode: true },
    {
      field: 'description',
      title: '描述',
      width: '40%',
      component: { name: 'VxeInput', props: { rows: 3 } },
    },
    {
      field: 'status',
      title: '状态',
      width: '30%',
      // component: {name:'VxeSelect',options:[{label:'启用',value:1},{label:'禁用',value:0}],props:{}},
      cellRender: {
        name: 'VxeSelect',
        props: {
          size: 'small'
        },
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 }
        ],
        events: {
          change(cellParams, params) {
            VxeUI.modal.message({
              content: `点击了 ${params.value}`,
              status: 'success'
            })
          }
        }
      }
    },
    {
      field: 'switch',
      title: '状态',
      width: '30%',
      component: { name: 'VxeSwitch' },
    },
    {
      field: 'action',
      title: '操作',
      width: '60%',
      // component: { name: 'TableActionBtn' },
      // cellRender: {
      //   name: 'VxeButtonGroup',
      //   props: {
      //     mode: 'text'
      //   },
      //   options: [
      //     { content: '查看', name: 'view' },
      //     { content: '删除', status: 'error', name: 'del' }
      //   ],
      //   events: {
      //     click(cellParams, params) {
      //       VxeUI.modal.message({
      //         content: `点击了 ${params.name}`,
      //         status: 'success'
      //       })
      //     }
      //   }
      // },
      cellRenders: [
        {
          name: 'VxeSelect',
          // selectApi: 'http://172.16.0.172:8083/sys/temp/list',
          // options: [
          //   { label: '启用', value: '1' },
          //   { label: '禁用', value: '0' }
          // ],
          props: {
            size: 'small',
            width: '50%'
          },
          events: {
            change(cellParams, params) {
              console.log('cellParams', cellParams.value.value)
              // console.log('params', params)
              VxeUI.modal.message({
                content: `点击了 ${params.name}`,
                status: 'success'
              })
            }
          }
        },
        {
          name: 'VxeSelect',
          options: [
            { label: '启用', value: 1 },
            { label: '禁用', value: 0 },
            { label: '删除', value: 3 },
          ],
          props: {
            size: 'small',
            width: '40%'
          },
          events: {
            change(cellParams, params) {
              console.log('cellParams', cellParams.value.value)
              VxeUI.modal.message({
                content: `点击了 ${params.name}`,
                status: 'success'
              })
            }
          }
        },
        {
          name: 'VxeInput',
          props: {
            size: 'small',
            width: '40%'
          },
          events: {
            change(cellParams, params) {
              console.log('cellParams', cellParams.value.value)
              VxeUI.modal.message({
                content: `点击了 ${params.name}`,
                status: 'success'
              })
            }
          }
        }
      ]
    }
  ],
  data: [
    {
      id: '1',
      name: '平台架构',
      description: '这是一个测试描述',
      status: 0,
      switch: 1,
      children: [
        {
          id: '1-1',
          name: '主站',
          description: '主站描述',
          status: 1,
          switch: 0
        },
        {
          id: '1-2',
          name: '测试',
          description: '测试描述',
          status: 0,
          switch: 1
        }
      ]
    }
  ],
  height: "auto"
})

</script>

<style scoped>
</style>
