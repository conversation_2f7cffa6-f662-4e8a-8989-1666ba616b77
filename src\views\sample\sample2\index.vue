<!-- 布局组件演示项目 -->
<template>
  <BmGridLayout :layout="layout" :col-num="12" :is-draggable="false"
    :is-resizable="false" :vertical-compact="false" :margin="[0, 0]" :use-css-transforms="true" :auto-size="true" fullscreen @size-change="handleSizeChange">
    <BmGridItem v-bind="layout[0]" class="grid-content">
        菜单区域
    </BmGridItem>
    <BmGridItem v-bind="layout[1]" class="grid-content">
        查询区域
    </BmGridItem>
    <BmGridItem v-bind="layout[2]" class="grid-content">
        表格区域
    </BmGridItem>
  </BmGridLayout>
</template>

<!--
  1. row-height 为当前子元素的高度,如果是左右布局则直接动态给父页面的高度
  2. row-height 默认无高度,
  3. grid-item 和 grid-layout 的 :h 是当前子元素的高度的系数,具体的高度需要 row-height 和 :h 的乘积
-->

<!-- 这个属性对象 { x: 0, y: 0.3, w: 2, h: 0.3, i: '2' } 描述了一个网格布局中的单个项目（GridItem）。 让我们逐个解释属性：

x: 项目的水平位置，以列数为单位。 x: 0 表示该项目位于第一列（从 0开始计数）。

y: 项目的垂直位置，以行数为单位。 y: 0.3 表示该项目位于第 0.3 行。 需要注意的是，行数可以是小数，这允许项目在行之间进行更精细的定位。

w: 项目的宽度，以列数为单位。 w: 2 表示该项目占据两列的宽度。

h: 项目的高度，以行数为单位。 h: 0.3 表示该项目占据 0.3 行的高度。 同样，高度也可以是小数。

i: 项目的唯一标识符。 i: '2' 表示该项目的 ID 是'2'。 这在处理多个项目时非常重要，用于唯一地识别和操作每个项目。 -->

<script lang="ts" setup>
import { ref , reactive } from 'vue'
import BmGridLayout from '@/components/gridlayout/BmGridLayout.vue'
import BmGridItem from '@/components/gridlayout/BmGridItem.vue'

const layout = reactive([
  // 菜单区域
  { x: 0, y: 0, w: 1.99, h: 1, i: '0' },
  // 查询区域
  { x: 2, y: 0, w: 10, h: 0.148, i: '1' },
  // 表格区域
  { x: 2, y: 0.15, w: 10, h: 0.85, i: '2' },
])

// 监听尺寸变化
const handleSizeChange = (size) => {
  console.log(size)
}

</script>

<style scoped>
.grid-content {
  background-color: #4384e6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
}
</style>