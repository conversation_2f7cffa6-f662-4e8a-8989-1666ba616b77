import { VxeUI,VxeButton } from 'vxe-pc-ui'
import router from '@/router'
import BmComponent from '@/components/BmComponent.vue'

VxeUI.renderer.add('BmComponent', {
  renderFormItemContent (renderOpts, params) {
    return <BmComponent></BmComponent>
  }
})


/**
 * 列表查询操作按钮
 */
VxeUI.renderer.add('ListSearchBtn', {
  renderFormItemContent (renderOpts, params) {
    return <span>
      <VxeButton type="submit" status="primary" icon="vxe-icon-search">查询</VxeButton>
      <VxeButton type="reset" icon="vxe-icon-repeat">重置</VxeButton>
    </span>
  }
})

/**
 * 详情返回按钮
 */
VxeUI.renderer.add('DetailsBackBtn', {
  renderFormItemContent (renderOpts, params) {
    return <VxeButton
      onClick={
        () => {
          router.back()
        }
      }>返回</VxeButton>
  }
})

/**
 * 表单提交操作按钮
 */
VxeUI.renderer.add('EditSubmitBtn', {
  renderFormItemContent (renderOpts, params) {
    return <span>
      <VxeButton
        onClick={
          () => {
            router.back()
          }
        }>取消</VxeButton>
      <VxeButton type="reset" icon="vxe-icon-repeat">重置</VxeButton>
      <VxeButton type="submit" status="primary" icon="vxe-icon-search">保存</VxeButton>
    </span>
  }
})

// 这是一个使用 VxeUI（一个基于 Vue 的 UI 组件库）的表单相关组件定制文件。文件中定义了三个自定义渲染器：

// ListSearchBtn（列表查询操作按钮）：
// 渲染一组查询操作按钮
// 包含一个"查询"按钮（提交类型，主要状态）和一个"重置"按钮
// 使用了 vxe-icon 图标
// DetailsBackBtn（详情返回按钮）：
// 渲染一个返回按钮
// 点击时触发 router.back()，实现返回上一页的功能
// EditSubmitBtn（表单提交操作按钮）：
// 渲染一组编辑表单的操作按钮
// 包含三个按钮：
// "取消"按钮：点击返回上一页
// "重置"按钮：重置表单
// "保存"按钮：提交表单（主要状态）
// 这些组件都是通过 VxeUI.renderer.add() 方法注册的自定义渲染器，可以在 VxeUI 的表单中通过指定对应的渲染器名称来使用。这样的设计使得常用的按钮组合可以被复用，保持了整个应用的一致性，同时也简化了开发过程。

// 文件使用了 TSX 语法（TypeScript + JSX），这允许在 TypeScript 中直接写类似 JSX 的模板代码，使得组件的编写更加直观和灵活。

