/**
 * Description: vxe-table自动计算高度
 * Created by huqingchao
 * Date: 2025/2/6 14:16
 * Update: 2025/10/6 14:16
 */
import { ref, nextTick, watch, onMounted, onBeforeMount } from 'vue'
import {useSkinStore} from '@/stores/skin.ts'
export const useCollapse = (domRef) => {
  const skinStore = useSkinStore();
  const isCollapse = ref(true)
  const handleCollapse = () => {
    isCollapse.value = !isCollapse.value
  }
  const tableWrapperHeight = ref('100px')

  onMounted(() => {
    tableWrapperHeight.value = `calc(100vh - ${domRef.value?.getBoundingClientRect().top + 20}px)`
  })

  watch(isCollapse,() => {
    nextTick(() => {
      tableWrapperHeight.value = `calc(100vh - ${domRef.value?.getBoundingClientRect().top + 20}px)`
    })
  })
  let timer= null
  watch(() => skinStore.size,() => {
    nextTick(() => {
      timer && clearTimeout(timer)
      timer = setTimeout(() => {
        tableWrapperHeight.value = `calc(100vh - ${domRef.value?.getBoundingClientRect().top + 20}px)`
      }, 300)
    })
  })

  onBeforeMount(() => {
    timer && clearTimeout(timer)
  })
  return {
    isCollapse,
    handleCollapse,
    tableWrapperHeight,
  }
}
