1. alova 是一个请求管理库 移除
2. vxe-pc-ui 这是vxe-table组件的全局UI组件库
3. vxe-table 目前顶级的表格组件
4. xe-utils 工具组件
5. unplugin-auto-import 插件自动引入插件
6. unplugin-vue-components 插件按需自动引入插件
7. layui/icons-vue 阿里ICON图标库
8. vite-plugin-lazy-import vxe-table组件懒加载引入
9. sass 引入这个是因为vxe-table的css库需要
10. utils里面的store.js 用于解决刷新数据丢失问题..
11. vuex用于解决给组件传值,页面不刷新以及刷新之后单页数据丢失问题 移除换成 pinia

----以下几个暂时不能使用,会出现错误 暂时移除
12. assist-worker多线程,依赖下面2个特别的依赖.否则会出现引入失败
12.1 npm i @rollup/plugin-commonjs
12.2 npm i vite-plugin-require-transform

13. pinia 状态管理插件,用于解决刷新数据丢失问题
npm i pinia-plugin-persistedstate

14. lodash 工具库,用于解决数据处理问题
npm i --save lodash

15. 基于 axios 的渐进式请求工具。 它不会破坏 axios 原有的能力，帮助您更轻松地处理请求
npm i @varlet/axle -S

16. 日期函数组件库
npm install dayjs

17. 进度条
npm install nprogress

18. 事件中心
npm install mitt
 