/**
 * Description: 用户管理接口请求api
 * Created by huqingchao
 * Date: 2024/10/22 15:49
 * Update: 2024/10/22 15:49
 */
import request from '@/api/index.js'

// 用户登录
export const login = function (data) {
  return request({
    url: 'sysUser/login',
    method: 'post',
    data
  })
}

// 修改密码
export const editPassword = function (data) {
  return request({
    url: '/sysUser/editPassword',
    method: 'post',
    data
  })
}

// 批量重置密码
export const resetPasswords = function (data) {
  return request({
    url: '/sysUser/resetPasswords',
    method: 'post',
    data
  })
}

// 用户列表
export const getPage = function (data) {
  return request({
    url: '/sysUser/pageList',
    method: 'post',
    data
  })
}

// 保存修改用户
export const saveOrUpdate = function (data) {
  return request({
    url: '/sysUser/saveOrUpdateEntity',
    // url: '/sysUser/saveOrUpdate',
    method: 'post',
    data
  })
}

// 删除用户
export const deleteById = function (data) {
  return request({
    
    url: '/sysUser/deleteEntity',
    // url: '/sysUser/deleteById',
    method: 'post',
    data
  })
}

// 获取用户信息
export const getInfoById = function (data) {
  return request({
    
    url: 'sysUser/getOne',
    // url: '/sysUser/getInfoById',
    method: 'post',
    data
  })
}

// 用户获取角色菜单按钮等
export const getUserAuth = function (data) {
  // 解决vite热更新在某些情况下失效的问题
  request.defaults.headers.common['Token'] = localStorage.getItem('token') || ''
  return request({
   // url: '/sysMenu/getMenuTree',
   url: '/sysUser/getUserAuth',

    
    method: 'post',
    data
  })
}

// 用户导出
export const sysUserExport = function (data) {
  return request({
    url: '/sysUser/export',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 上传单个文件
export const postPubAdminUploadSingle = function (data) {
  return request({
    url: '/sysUser/uploadSingle',
    method: 'post',
    data
  })
}



//部门新增用户
export const postSaveDataForDept = function (data) {
  return request({
    url: '/sysUser/saveDataForDept',
    method: 'post',
    data
  })
}