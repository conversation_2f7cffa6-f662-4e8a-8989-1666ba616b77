<!--
* Description: 角色管理
* Created by hu<PERSON><PERSON><PERSON>
* Date: 2024/10/29 18:16
* Update: 2024/10/29 18:16
-->
<script setup>
import { computed, reactive, ref, watch, onMounted, nextTick } from 'vue'
import MoveBox from '@/components/MoveBox.vue'
import useRouteTitle from '@/hooks/useRouteTitle.js'
import { Modal, Loading } from '@opentiny/vue'
import { iconDel, iconEditorBackground, iconPlus } from '@opentiny/vue-icon'
import ChooseUserDialog from './ChooseUserDialog.vue'
import {
  deleteById,
  getOne,
  getList,
  saveOrUpdate,
  sysUserRoleGetPage,
  sysUserRoleSaveOrUpdate,
  deleteByRoleUserId,
  getMeansByRole,
  sysRoleMenuSaveOrUpdate,
  sysRoleMenuBtnGetList,
  sysRoleMenuBtnSaveOrUpdate,
  sysRoleExport
} from '@/api/methods/role.js'
import { getList as getMenuList } from '@/api/methods/menu.js'
import { useOperatePermission } from '@/hooks/useOperatePermission.js'
import { exportData } from '@/utils/exportData.js'
const { btnPermission } = useOperatePermission()
const vLoading = Loading.directive
const TinyIconEditorBackground = iconEditorBackground()
const TinyIconDel = iconDel()
const TinyIconPlus = iconPlus()

const { title } = useRouteTitle()
const searchName = ref('')
const listData = ref([])
const gridOptions = reactive({
  showOverflow: true,
  border: true,
  loading: false,
  height: 'auto',
  columns: [
    { type: 'seq', width: 70, fixed: 'left', align: 'center' },
    { field: 'operate', title: '操作', width: 150, slots: { default: 'operate_default' } },
    { field: 'sname', title: '角色名称', minWidth: 150, slots: { default: 'sname_default' } },
    { field: 'description', title: '角色描述', minWidth: 100, },
    { field: 'userCount', title: '设置成员', width: 150, slots: { default: 'member_default' } },
  ],
  data: []
})

const gridEvents = {
  cellClick({ row }) {
    currentRoleRow.value = row
    getRoleMenuChecked()
  },
}

const handleRoleSearch = () => {
  handleRolePageData()
}

const handleRolePageData = () => {
  gridOptions.loading = true
  getList({
    sname: searchName.value
  }).then(res => {
    gridOptions.data = res.data
    listData.value = res.data
    gridOptions.loading = false
  })
}

const handleExport = () => {
  sysRoleExport({
    sname: searchName.value
  }).then(res => {
    exportData(res, '角色管理列表.xlsx')
  })
}

const roleDialogTitle = computed(() => {
  let title = '新建角色'
  if (roleOperateType.value === 'edit') {
    title = '编辑角色'
  } else if (roleOperateType.value === 'view') {
    title = '查看角色'
  }
  return title
})

const roleFormData = reactive({
  sname: '',
  description: '',
  status: 1, // 状态0-禁用，1-启用
})

const roleFormRules = reactive({})

const roleFormRef = ref(null)

const roleTypeOptions = ref([])
const roleClassOptions = ref([])
const roleStatusOptions = ref([])

const roleDialog = ref(false)
const roleOperateType = ref('')
const currentRoleRow = ref({})

const handleAdd = row => {
  roleOperateType.value = 'add'
  roleDialog.value = true
}

const handleEdit = row => {
  roleOperateType.value = 'edit'
  roleDialog.value = true
  handleDetail(row)
}

const handleDetail = row => {
  getOne({ roleId: row.roleId }).then(res => {
    const { data } = res
    currentRoleRow.value = data
    roleFormData.sname = data.sname
    roleFormData.description = data.remark
    roleFormData.status = data.statusName == '是' ? 1 : 0
  })
}

const handleView = row => {
  roleOperateType.value = 'view'
  roleDialog.value = true
  handleDetail(row)
}

const handleEditMember = (row) => {
  roleOperateType.value = 'edit'
  memberDialog.value = true
  isRefresh.value = false
  currentRoleRow.value = row
  tabActiveName.value = 'first'
  handleDetail(row)
  handleMemberPageData()
}

const handleDelete = (row) => {
  // 只有禁用状态无成员情况下删除按钮才可用，其余情况置灰
  if (row.status || (row.userCount && row.userCount > '0')) return
  Modal.confirm('删除后不可恢复，确定删除？').then(res => {
    if (res === 'confirm') {
      deleteById({
        id: row.id,
      }).then(res => {
        Modal.message({
          status: 'success',
          message: '删除成功',
        })
        currentRoleRow.value = {}
        currentMenuId.value = null
        searchName.value = ''
        handleRolePageData()
      })
    }
  })
}

const handleRoleSave = () => {
  roleFormRef.value.validate(valid => {
    if (valid) {
      const params = {
        ...roleFormData,
      }
      if (roleOperateType.value === 'edit') {
        params.id = currentRoleRow.value.id
      }
      saveOrUpdate(params).then(res => {
        roleDialog.value = false
        memberDialog.value = false
        searchName.value = ''
        handleRolePageData()
      })
    }
  })
}


const memberDialog = ref(false)
const tabActiveName = ref('first')

const memberGridOptions = reactive({
  showOverflow: true,
  border: true,
  loading: false,
  height: '500px',
  pagerConfig: {
    total: 0,
    currentPage: 1,
    pageSize: 10,
  },
  columns: [
    { type: 'seq', width: 70, fixed: 'left', align: 'center' },
    { field: 'operate', title: '操作', width: 150, slots: { default: 'operate_default' } },
    { field: 'userSname', title: '姓名', minWidth: 150 },
    { field: 'username', title: '用户名', minWidth: 100, },
  ],
  data: []
})

const memberGridEvents = {
  pageChange({ pageSize, currentPage }) {
    memberGridOptions.pagerConfig.currentPage = currentPage
    memberGridOptions.pagerConfig.pageSize = pageSize
    handleMemberPageData()
  }
}

const handleMemberPageData = () => {
  memberGridOptions.loading = true
  const params = {
    roleId: currentRoleRow.value.id,
    current: memberGridOptions.pagerConfig.currentPage,
    size: memberGridOptions.pagerConfig.pageSize
  }
  sysUserRoleGetPage(params).then(res => {
    const data = res.data.list
    memberGridOptions.data = data.list
    memberGridOptions.pagerConfig.total = data.total
    memberGridOptions.loading = false
  })
}

const chooseUserDialog = ref(false)

const openUserDialog = () => {
  chooseUserDialog.value = true
}
const isRefresh = ref(false)

const handleMemberDialogClose = () => {
  if (isRefresh.value) {
    handleRolePageData()
  }
}
const handleSaveUser = (chooseList) => {
  sysUserRoleSaveOrUpdate({
    roleId: currentRoleRow.value.id,
    userIdList: chooseList.map(item => item.id)
  }).then(res => {
    chooseUserDialog.value = false
    handleMemberPageData()
    isRefresh.value = true
  })
}

const handleMemberDelete = (row) => {
  Modal.confirm('删除后不可恢复，确定删除？').then(res => {
    if (res === 'confirm') {
      deleteByRoleUserId({
        roleId: row.roleId,
        userId: row.userId,
      }).then(res => {
        Modal.message({
          status: 'success',
          message: '删除成功',
        })
        handleMemberPageData()
        isRefresh.value = true
      })
    }
  })
}

const treeRef = ref(null)
const menuTree = ref([])
const treeLoading = ref(false)

const getMenuData = () => {
  // getMenuList({}).then(res => {
  //   menuTree.value = res.data.list
  // })
}
//点击菜单列表
const getRoleMenuChecked = () => {
  treeLoading.value = true

  getMeansByRole({
    roleId: currentRoleRow.value.roleId,
  }).then(res => {
    treeLoading.value = false
    nextTick(() => {
      menuTree.value = res.data
      if (menuTree.value.length) {
        let select = [];
        menuTree.value.forEach((item) => {
          if (item.isChecked) {
            select.push(item.menuId)
          }
          if (item.children?.length) {

            item.children.forEach((item1) => {
              if (item1.isChecked) {
                select.push(item1.menuId)
              }
            })
          }
        })
        treeRef.value.setCheckedKeys(select)

        // treeRef.value.setCurrentNode({id:menuTree.value[0].menuId})

        handleNodeClick(menuTree.value[0], { checked: res.data.includes(menuTree.value[0])})
      }
    })
  })
}
// 角色菜单保存
const handleSaveMenuChecked = () => {
  const keys = treeRef.value.getCheckedKeys()
  sysRoleMenuSaveOrUpdate({
    roleId: currentRoleRow.value.id,
    menuIdList: keys
  }).then(res => {
    Modal.message({
      status: 'success',
      message: '保存成功',
    })
  })
}
const currentMenuId = ref(null)
const currentMenuNode = ref({
  checked: false
})

const btnList = ref([])

const tabList = ref([])
const btnLoading = ref(false)
// 点击菜单树节点
const handleNodeClick = (data, node) => {
  currentMenuId.value = data.menuId
  currentMenuNode.value = node
  btnLoading.value = true
  sysRoleMenuBtnGetList({
    roleId: currentRoleRow.value.roleId,
    menuId: data.menuId
  }).then(res => {
    btnLoading.value = false
    btnList.value = res.data.btnList
    tabList.value = res.data.cardList
  })
}
// 保存菜单/tab/卡片页面按钮
const handleSaveBtnChecked = () => {
  if (!currentMenuNode.value.checked) {
    Modal.message({
      status: 'warning',
      message: '需要勾选当前菜单列表才能保存！',
    })
    return
  }
  sysRoleMenuBtnSaveOrUpdate({
    roleId: currentRoleRow.value.id,
    menuId: currentMenuId.value,
    ...getIdList()
  }).then(res => {
    Modal.message({
      status: 'success',
      message: '保存成功',
    })
  })
}

const getIdList = () => {
  const tabIds = []
  tabList.value.forEach(item => {
    if (item.checked) {
      tabIds.push({
        id: item.id,
        btnIdList: item.btnList.filter(item => item.checked).map(item => item.id)
      })

    }
  })
  return {
    btnIdList: btnList.value.filter(item => item.checked).map(item => item.id),
    tabList: tabIds
  }
}

const menuAllChecked = ref(false)

const handleMenuAllChange = (value) => {
  btnList.value.forEach(item => {
    item.checked = value
  })
}

watch(btnList, (newVal) => {
  const findItem = newVal.find(item => !item.checked)
  menuAllChecked.value = !findItem
}, { deep: true, immediate: true })

onMounted(() => {
  handleRolePageData()
  getMenuData()
})

</script>

<template>
  <MoveBox height="100%" leftWidth="40%">
    <template #left>
      <div class="inner-left">
        <div class="base-content-operate">
          <div class="title"> {{ title }} </div>
          <div class="right-operate">
            <div class="right-search">
              <tiny-input v-model="searchName" placeholder="请输入角色名称" size="small" clearable
                @keyup.enter="handleRoleSearch"></tiny-input>
              <tiny-button type="primary" size="small" @click="handleRoleSearch">查询</tiny-button>
            </div>
            <tiny-button type="primary" size="small" @click="handleAdd" v-if="btnPermission.add">新建角色</tiny-button>
            <tiny-button type="primary" size="small" plain @click="handleExport">导出</tiny-button>
          </div>
        </div>
        <div :style="{ height: 'calc(100vh - 195px)' }">
          <vxe-grid v-bind="gridOptions" v-on="gridEvents">
            <template #sname_default="{ row }">
              <span class="base-brand" @click="handleView(row)">{{ row.sname }}</span>
            </template>
            <template #operate_default="{ row }">
              <div class="base-table-operate__icon2">
                <div v-if="btnPermission.edit" class="icon-wrap" @click="handleEdit(row)">
                  <TinyIconEditorBackground></TinyIconEditorBackground>
                </div>
                <div v-if="btnPermission.delete"
                  :class="['icon-wrap', row.status || (row.userCount && row.userCount > '0') ? 'is-disabled' : '']"
                  @click="handleDelete(row)">
                  <TinyIconDel></TinyIconDel>
                </div>
              </div>
            </template>
            <template #member_default="{ row }">
              <span v-if="btnPermission.set" class="base-brand member" @click="handleEditMember(row)">
                <span v-if="row.userCount && row.userCount > '0'"> {{ row.userCount }} <TinyIconEditorBackground>
                  </TinyIconEditorBackground></span>
                <span v-else>设置</span>
              </span>
            </template>
          </vxe-grid>
        </div>
      </div>
      <tiny-dialog-box v-model:visible="roleDialog" :title="roleDialogTitle" width="800px" class="base-dialog-box">
        <tiny-form ref="roleFormRef" :model="roleFormData" :rules="roleFormRules" label-width="150px"
          class="base-form-border" :disabled="roleOperateType === 'view'">
          <tiny-form-item label="角色名称" prop="sname" required>
            <tiny-input v-model="roleFormData.sname" placeholder="请输入" clearable :maxlength="30"></tiny-input>
          </tiny-form-item>
          <tiny-form-item label="角色描述" prop="description">
            <tiny-input v-model="roleFormData.description" type="textarea" clearable placeholder="请输入" :maxlength="200"
              show-word-limit></tiny-input>
          </tiny-form-item>
          <tiny-form-item label="是否启用" prop="status" required>
            <vxe-switch v-model="roleFormData.status" open-label="启用" close-label="禁用" :open-value="1" :close-value="0"
              :disabled="roleOperateType === 'view'"></vxe-switch>
          </tiny-form-item>
        </tiny-form>
        <template #footer v-if="roleOperateType !== 'view'">
          <tiny-button @click="roleDialog = false">取消</tiny-button>
          <tiny-button type="primary" @click="handleRoleSave">保存</tiny-button>
        </template>
      </tiny-dialog-box>
      <tiny-dialog-box v-model:visible="memberDialog" :title="roleDialogTitle" width="1000px" class="base-dialog-box"
        @close="handleMemberDialogClose">
        <div class="dialog-operate">
          <tiny-tabs v-model="tabActiveName" class="base-tab">
            <tiny-tab-item title="角色信息" name="first"></tiny-tab-item>
            <tiny-tab-item title="成员设置" name="second"></tiny-tab-item>
          </tiny-tabs>
          <tiny-button type="primary" size="small" @click="openUserDialog"
            v-if="tabActiveName === 'second'">添加</tiny-button>
        </div>
        <tiny-form v-if="tabActiveName === 'first'" ref="formRef" :model="roleFormData" :rules="roleFormRules"
          label-width="150px" class="base-form-border" :disabled="roleOperateType === 'view'">
          <tiny-form-item label="角色名称" prop="sname" required>
            <tiny-input v-model="roleFormData.sname" placeholder="请输入" clearable :maxlength="30"></tiny-input>
          </tiny-form-item>
          <tiny-form-item label="角色描述" prop="description">
            <tiny-input v-model="roleFormData.description" type="textarea" clearable placeholder="请输入" :maxlength="200"
              show-word-limit></tiny-input>
          </tiny-form-item>
          <tiny-form-item label="是否启用" prop="status" required>
            <vxe-switch v-model="roleFormData.status" open-label="启用" close-label="禁用" :open-value="1" :close-value="0"
              :disabled="roleOperateType === 'view'"></vxe-switch>
          </tiny-form-item>
        </tiny-form>
        <template v-else>
          <vxe-grid v-bind="memberGridOptions" v-on="memberGridEvents">
            <template #operate_default="{ row }">
              <div class="base-table-operate__icon2">
                <div class="icon-wrap" @click="handleMemberDelete(row)">
                  <TinyIconDel></TinyIconDel>
                </div>
              </div>
            </template>
          </vxe-grid>
          <ChooseUserDialog v-model="chooseUserDialog" :roleId="currentRoleRow.id" @handleConfirm="handleSaveUser">
          </ChooseUserDialog>
        </template>
        <template #footer v-if="roleOperateType !== 'view' && tabActiveName === 'first'">
          <tiny-button @click="memberDialog = false">取消</tiny-button>
          <tiny-button type="primary" @click="handleRoleSave">保存</tiny-button>
        </template>
      </tiny-dialog-box>
    </template>
    <template #right>
      <div class="inner-right">
        <MoveBox height="100%" leftWidth="40%">
          <template #left>
            <div class="inner-content">
              <div class="base-content-operate">
                <div class="title">菜单列表</div>
                <tiny-button v-if="btnPermission.menuSave" type="primary" size="small" @click="handleSaveMenuChecked"
                  :disabled="!currentRoleRow.id">确定</tiny-button>
              </div>
              <div class="content-wrapper" v-loading="treeLoading">
                <tiny-tree v-if="currentRoleRow.roleId" ref="treeRef" node-key="menuId" :data="menuTree" :props="{
                  label: 'menuName'
                }" highlight-current show-checkbox default-expand-all check-strictly :expand-on-click-node="false"
                  @node-click="handleNodeClick"></tiny-tree>
                <div class="empty-data" v-else>{{ menuTree.length ? '请点击左侧角色列表' : '暂无数据' }}</div>
              </div>
            </div>
          </template>
          <template #right>
            <div class="inner-content">
              <div class="base-content-operate">
                <div class="title">菜单/tab/卡片页面按钮</div>
                <tiny-button v-if="btnPermission.btnSave" type="primary" size="small" @click="handleSaveBtnChecked"
                  :disabled="!currentMenuId">确定</tiny-button>
              </div>
              <div class="content-wrapper" v-loading="btnLoading">
                <template v-if="currentMenuId">
                  <tiny-form :model="roleFormData" label-width="150px" label-position="left"
                    class="base-form-border custom-form">
                    <tiny-form-item label="配置项" prop="parentName">
                      <div>配置按钮</div>
                    </tiny-form-item>
                    <tiny-form-item prop="sname">
                      <template #label>
                        <div style="height: 100%;display: flex;">
                          <tiny-checkbox v-model="menuAllChecked" v-if="btnList.length"
                            @change="handleMenuAllChange">菜单页面</tiny-checkbox>
                          <div v-else>菜单页面</div>
                        </div>
                      </template>
                      <template v-if="btnList.length">
                        <tiny-checkbox v-for="item in btnList" :key="item.id" v-model="item.isChecked">{{ item.sname
                          }}</tiny-checkbox>
                      </template>
                      <div v-else style="color: var(--ti-base-color-common-2, #adb0b8);">暂无数据</div>
                    </tiny-form-item>
                  </tiny-form>
                  <tiny-form :model="roleFormData" label-width="150px" label-position="left"
                    class="base-form-border custom-form" v-if="tabList.length">
                    <tiny-form-item label="tab页/卡片" prop="parentName">
                      <div>配置按钮</div>
                    </tiny-form-item>
                    <tiny-form-item v-for="item in tabList" :key="item.id">
                      <template #label>
                        <div style="height: 100%; display: flex;">
                          <tiny-checkbox v-model="item.checked">{{ item.sname }}</tiny-checkbox>
                        </div>
                      </template>
                      <template v-if="item.btnList.length">
                        <tiny-checkbox v-for="row in item.btnList" :key="row.id" v-model="row.isChecked"
                          >{{ row.sname }}</tiny-checkbox>
                      </template>
                      <div v-else style="color: var(--ti-base-color-common-2, #adb0b8);">暂无数据</div>
                    </tiny-form-item>
                  </tiny-form>
                </template>
                <div class="empty-data" v-else>暂无数据</div>
              </div>
            </div>
          </template>
        </MoveBox>
      </div>
    </template>
  </MoveBox>
</template>
<style scoped lang="less">
.inner-left {
  padding: 10px;

  .right-operate {
    display: flex;
    align-items: center;

    .right-search {
      display: flex;
      align-items: center;
      margin-right: 10px;

      .tiny-input {
        width: 200px;
      }
    }
  }
}

.inner-right {
  height: 100%;

  .inner-content {
    padding: 10px;
    height: 100%;
  }
}

.base-content-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 1px dashed #aaa;

  .title {
    font-size: 16px;
    font-weight: bold;
    color: #000;
  }
}

.member {
  svg {
    font-size: 20px;
    fill: var(--ti-base-color-brand-6);
  }
}

.content-wrapper {
  border: 1px solid #eee;
  padding: 10px;
  overflow: auto;
  height: calc(100% - 60px);

  :deep(.tiny-tree) {

    .tiny-tree-node__content:hover .tiny-tree-node__content-box,
    .tiny-tree-node__content:hover .tiny-tree-node__content-right {
      background-color: var(--vxe-ui-table-row-hover-current-background-color);
    }
  }

  :deep(.tiny-tree--highlight-current .tiny-tree-node.is-current>.tiny-tree-node__content) {
    background-color: var(--vxe-ui-table-row-hover-current-background-color);
  }

  .empty-data {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--ti-base-color-common-2);
    font-size: 14px;
  }
}

.custom-form {
  margin-bottom: 20px;

  :deep(.tiny-form-item) {
    position: relative;

    .tiny-form-item__label {
      padding-left: 10px;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
    }

    .tiny-form-item__content {
      padding-left: 10px;
    }

    .tiny-checkbox {
      line-height: 46px;
    }
  }
}

.dialog-operate {
  display: flex;
  justify-content: space-between;
}
</style>
