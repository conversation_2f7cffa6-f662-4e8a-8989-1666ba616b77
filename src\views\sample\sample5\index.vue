<!--
* Description: 菜单管理
* Created by 郑勇
* Date: 2024/12/26 10:16
* Update: 2024/12/26 10:16
-->
<template>
    <BmGridLayout :layout="layout" :col-num="12" :is-draggable="false" :is-resizable="true" :vertical-compact="false"
        :margin="[0, 0]" :use-css-transforms="true" :auto-size="true" fullscreen>
        <BmGridItem v-bind="layout[0]" @resize="handleResize">
            <bm-grid :grid-options="gridOptions"></bm-grid>
        </BmGridItem>
        <BmGridItem v-bind="layout[1]" @resize="handleResize">
            <bm-grid :grid-options="currentGridOptions" ref="gridRef"></bm-grid>
        </BmGridItem>
        <BmGridItem v-bind="layout[2]" @resize="handleResize">
            333
        </BmGridItem>

    </BmGridLayout>
    <!-- <FormDialog v-bind="formDialogProps" /> -->
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import BmGridLayout from '@/components/gridlayout/BmGridLayout.vue'
import { getList } from '@/api/methods/menu'
import BmGridItem from '@/components/gridlayout/BmGridItem.vue'
import BmGrid from '@/components/BmGrid.vue'
import { ExtendedGridProps } from '@/types/grid'
import { VxeUI } from 'vxe-pc-ui'
import { ExtendedFormProps } from '@/types/form'
import XEUtils from 'xe-utils';
import { getDeptUserList } from '@/api/methods/dept'

// 高度 最大 1 , w 最大 12
const layout = reactive([
    // 菜单区域
    { x: 0, y: 0, w: 4, h: 1, i: '0' },
    // 查询区域
    { x: 4, y: 0, w: 4, h: 1, i: '1' },
    // 表格区域
    { x: 8, y: 0, w: 4, h: 1, i: '2' },
])

const handleResize = (size) => {
    // 第一个布局
    if (size.i === '0') {
        layout[1].x = size.w
        layout[1].w = 12 - size.w - layout[2].w
    }

    // 第二个布局
    if (size.i === '1') {
        layout[2].x = size.x + size.w
        layout[2].w = 12 - layout[2].x
    }

    // 第三个布局
    if (size.i === '2') {
        layout[2].x = 12 - size.w
        layout[1].w = layout[1].w + (12 - size.x - size.w)
    }
}

const tabList = reactive([
    { title: '菜单列表', name: '1' },
    { title: '菜单列表', name: '2' },
])

const gridRef = ref<any>()

//左半部分
const gridOptions = reactive<ExtendedGridProps>({
    headerAlign: 'center',//表头对齐方式
    border: true,//是否显示边框
    scrollY: { enabled: false },//是否开启滚动
    treeConfig: {
        rowField: 'menuId',//行唯一标识
        childrenField: 'children'//子节点字段名
    },
    formConfig: {
        items: [
            { field: 'nickname', title: '菜单列表', span: 16, itemRender: { name: 'VxeInput', props: { width: '30%' } } },
            {
                span: 8,
                itemRender: {
                    name: 'VxeButtonGroup',
                    props: {
                        size: 'small'
                    },
                    options: [
                        { content: '查询', status: 'primary', id: 'query' },
                        { content: '新增菜单', status: 'success', id: 'add' }
                    ],
                    events: {
                        click(cellParams, params) {
                            // 表单和表格 的对象
                            console.log(cellParams)
                            // 查询
                            if (params.option.id === 'query') {
                              cellParams.$grid.commitProxy('query'); // 获取 BmGrid 组件实例
                            }
                            // 新增菜单
                            if (params.option.id === 'add') {
                                console.log(cellParams)
                            }
                        }
                    }
                }
            }
        ]
    },
    columns: [
        { type: 'seq', width: "14%", fixed: 'left', align: 'center' },
        {
            field: 'action',//字段名
            title: '操作',//标题
            width: '20%',//宽度
            cellRenders: [//单元格渲染
                {
                    name: 'VxeIcon',
                    props: {
                        size: 'medium',//组件属性
                        name: 'setting',//组件属性
                        width: '33%',//组件属性
                        status: 'primary'//组件属性
                    },
                    events: {
                        click(cellParams, params) {
                            // console.log(cellParams)
                            VxeUI.modal.message({
                                content: '点击了',
                                status: 'success'
                            })
                        }
                    }
                },
                {
                    name: 'VxeIcon',
                    props: {
                        size: 'medium',
                        name: 'home',
                        width: '33%',
                        status: 'success'
                    },
                    events: {
                        click(cellParams, params) {
                            VxeUI.modal.message({
                                content: '点击了',
                                status: 'success'
                            })
                        }
                    }
                },
                {
                    name: 'VxeIcon',
                    props: {
                        size: 'medium',
                        name: 'home',
                        width: '33%',
                        status: 'warning'
                    },
                    events: {
                        click(cellParams, params) {
                            VxeUI.modal.message({
                                content: '点击了',
                                status: 'success'
                            })
                        }
                    }
                }
            ]
        },
        {
            field: 'sname', title: '菜单名称', width: '30%', treeNode: true,//字段名,标题,宽度,是否树节点
            cellRender: {
                name: 'VxeText',//组件名称
                props:{
                    content:'菜单名称'
                },
                events: {
                    async click(cellParams, params) {
                        console.log(cellParams.row)
                        //根据deptId查询人员信息
                        let res = await getDeptUserList({ deptId: cellParams.row.deptId, queryChild: "false" })
                        console.log(res.data.records)
                        // gridOptions.data = res.data.records
                    }
                }
            }
        },
        {
            field: 'components', title: '菜单类型', width: '800px',
            // 单组件过滤 示例
            // cellRender: {
            //         name: 'VxeSelect',
            //         props: {
            //             width: '100%',
            //         },
            //         // 这里过滤为 单cellRender 下面进行,目前仅支持初次加载.后面动态变化 未来实现??
            //         visibleEvent: (row) => {
            //             return row.sname == '系统设置';
            //         },
            //         options: [
            //             { label: '系统设置', value: '1' },
            //             { label: '业务配置', value: '2' },
            //             { label: '代码示例', value: '3' },
            //         ],
            //         events: {
            //             change(value, formData, formRef) {
            //                 console.log('change', value)
            //                 console.log('formData', formData)
            //                 console.log('formRef', formRef)
            //             }
            //         },
            //         // dict: '0',
            //     },
            cellRenders: [
                {
                    name: 'VxeInput',
                    props: {
                        width: '50%',
                    },
                    // 这里过滤为 单cellRender 下面进行,目前仅支持初次加载.后面动态变化 未来实现??
                    visibleEvent: (row) => {
                        // console.log(row)
                        return row.sname == '业务配置' || row.sname == '代码示例';
                    }
                },
                {
                    name: 'VxeButton',
                    props: {
                        width: '40%',
                        content:'按钮'
                    },
                    // 这里过滤为 单cellRender 下面进行,目前仅支持初次加载.后面动态变化 未来实现??
                    visibleEvent: (row) => {
                        return row.sname == '代码示例';
                    }
                },
                {
                    name: 'VxeSelect',
                    props: {
                        width: '100%',
                    },
                    // 这里过滤为 单cellRender 下面进行,目前仅支持初次加载.后面动态变化 未来实现??
                    // visibleEvent: (row) => {
                        // console.log('row',row)
                        // return row.sname == '系统设置';
                    // },
                    events: {
                        change(value, formData, row) {
                            console.log('change', value)
                            console.log('formData', formData)
                            console.log('row', row)
                        }
                    },
                    dict: '10002',
                },
                {
                    name:'VxeSwitch',
                    // name:'VxeInput',
                    props:{
                        content:'内容',
                        width:'100%',
                        openLabel: '开',
                        closeLabel: '关',
                        openValue: '10001001',  // 设置打开状态的值
                        closeValue: '10001002', // 设置关闭状态的值
                    },
                    events:{
                        click(){
                        //   console.log('click')
                        },
                        change(value,params){
                          console.log('value', '-------------')
                          console.log('params', params)

                          if(params.children.length > 0){
                            console.log('params.children[0]', params.children[0])
                            console.log('params.children[0].components', params.children[0].components)
                            const zz= params.children[0].components.split(',')
                            console.log('zz', zz)
                            console.log(value)
                            zz[3]  = value;
                            // console.log('zz[3]', zz[3])
                            params.children[0].components = zz.join(',');
                            // console.log('params222', params.children[0].components)
                          }
                        }
                    }
                }
            ]
        },
        { field: 'url', title: '菜单分类', width: '18%' },
    ],
    height: "auto",
    proxyConfig: {
        sort: true,//是否开启排序
        form: true,//是否开启表单
        response: {
            result: 'data',//返回数据
        },
        ajax: {
            async query({ page, form, sorts }) {
                const params = {
                    ...page,
                    ...form
                }
                let res = await getList(params)
                return res.data.list
            }
        }
    }
})

//这里因为需要动态显示和隐藏,所以需要使用let声明
let formOptions = reactive<ExtendedFormProps>({
    // 表单项配置数组
    items: [
        {
            field: 'cardType',
            // title: '证件类型',
            span: 10,
            itemRender: {
                name: 'VxeRadioGroup',
                // dict: '0',
                // 如果使用字典,则 option 默认失效
                // selectApi: {
                //     url: '/sys/temp/list',
                //     body: {},
                //     config: {
                //         keyFieldName: 'id',
                //         valueFieldName: 'name'
                //     }
                // },
                options: [
                    { label: '身份证', value: '1' },
                    { label: '护照', value: '2' }
                ],
                props: {
                    placeholder: '请选择证件类型',
                },
                events: {
                    change(value, formData, formRef) {
                        console.log(value, formData)
                        console.log(formRef)
                        formRef.submitForm()
                        currentType.value = value;
                        formRef.setFieldVisible('workCode', value == '1');

                    }
                },
            },
            // visible: false
        },
        {
            field: 'username1',
            title: '证件类型',
            span: 8,
            itemRender: {
                name: 'BmComponent',
            }
        },
        {
            field: 'username',
            // title: '证件类型',
            span: 8,
            // itemRender: {
            //     name: 'VxeInput',
            //     props: {
            //         placeholder: '请选择证件类型',
            //     },
            //     events: {
            //         change(value, formData, formRef) {
            //             // console.log(value, formData)
            //         }
            //     }
            // },
            itemRenders: [
                {
                    name: 'VxeInput',
                    props: {
                        placeholder: '请选择证件类型',
                    },
                    events: {
                        change(value, formData, formRef) {
                            // console.log(value, formData)
                        }
                    }
                },
                {
                    name: 'VxeSelect',
                    // dict: '0',
                    selectApi: {
                        url: '/sys/temp/list',
                        body: {},
                        config: {
                            keyFieldName: 'id',
                            valueFieldName: 'name'
                        }
                    },
                    events: {
                        // 参数1:表单数据,参数2:表单实例,参数3:组件实例
                        onchange(value, formData, formRef) {
                            console.log(value)
                        }
                    }
                }
            ]
        },
        //这里为表单得字段按钮.
        {
            field: 'workCode',
            span: 6,
            itemRender: {
                name: 'VxeButton',
                props: {
                    type: 'primary',
                    content: '测试按钮'
                },
                events: {
                    // 参数1:表单数据,参数2:表单实例,参数3:组件实例
                    click(value, formData, formRef) {
                        console.log(value)
                        alert('点击了')

                    }
                }
            },
            // 这个函数仅在第一次加载的时候 生效,后面无论怎么修改都不生效
            visibleEvent: (data) => {
                // 获取表单内的数据
                console.log("visibleEvent", data)
                return currentType.value == '1';
            }
        },
    ],
    // 表单类型 这里决定了表格上面得工具栏是否能自动获取值
    formType: 'edit',
    // 获取数据的配置
    getData: () => {
        //这里支持 返回接口数据
        return {
            cardType: '1',
            workCode: '2',
            username: '张三',
        }
    },
    // 提交数据的函数
    applyData: async (data) => {
        // 这里可以实现提交数据的逻辑
        console.log('提交的数据:', data)
    },
    // 其他vxe-form的配置项
    titleWidth: 'auto',
    titleAlign: 'right',
    titleColon: true,
    titleOverflow: true,
    // 表单布局
    layout: 'inline',
    // 表单项间距
    itemGap: 10,
    // 是否显示冒号
    colon: true,
    // 是否显示必填星号
    showRequiredMark: true,
    // 校验规则
    rules: {
        sname: [
            { required: true, message: '请输入用户名' }
        ],
        phone: [
            { required: true, message: '请输入手机号' }
        ]
    },
    // 是否只读
    readonly: false,
})

const gridOptions1 = reactive<ExtendedGridProps>({
    headerAlign: 'center',//表头对齐方式
    border: true,//是否显示边框
    scrollY: { enabled: false },//是否开启滚动
    treeConfig: {
        rowField: 'menuId',//行唯一标识
        childrenField: 'children'//子节点字段名
    },
    printConfig: {},
    tools: formOptions,
    columns: [
        { type: 'seq', width: "14%", fixed: 'left', align: 'center' },
        {
            field: 'action',//字段名
            title: '操作',//标题
            width: '20%',//宽度
            // cellRenders: [//单元格渲染
            //     {
            //         name: 'VxeIcon',
            //         props: {
            //             size: 'medium',//组件属性
            //             name: 'setting',//组件属性
            //             width: '33%',//组件属性
            //             status: 'primary'//组件属性
            //         },
            //         events: {
            //             click(cellParams, params) {
            //                 VxeUI.modal.message({
            //                     content: '点击了',
            //                     status: 'success'
            //                 })
            //             }
            //         }
            //     },
            //     {
            //         name: 'VxeIcon',
            //         props: {
            //             size: 'medium',
            //             name: 'home',
            //             width: '33%',
            //             status: 'success'
            //         },
            //         events: {
            //             click(cellParams, params) {
            //                 VxeUI.modal.message({
            //                     content: '点击了',
            //                     status: 'success'
            //                 })
            //             }
            //         }
            //     },
            //     {
            //         name: 'VxeIcon',
            //         props: {
            //             size: 'medium',
            //             name: 'home',
            //             width: '33%',
            //             status: 'warning'
            //         },
            //         events: {
            //             click(cellParams, params) {
            //                 VxeUI.modal.message({
            //                     content: '点击了',
            //                     status: 'success'
            //                 })
            //             }
            //         }
            //     }
            // ],
            cellRender: {
                name: 'VxeButtonGroup',
                // dict: '0',
                // 如果使用字典,则 option 默认失效
                // selectApi: {
                //     url: '/sys/temp/list',
                //     body: {},
                //     config: {
                //         keyFieldName: 'id',
                //         valueFieldName: 'name'
                //     }
                // },
                options: [
                    { content: '身份证', value: '1', code: '1',name:"btn1" },
                    { content: '护照', value: '2', code: '2',name:"btn2" }
                ],
                props: {
                    placeholder: '请选择证件类型',
                    options:{
                        "name": "VxeButton",
                    }
                },
                events: {
                    change(value, formData, formRef) {
                        console.log('cellRender', value)
                        console.log('formData', formData)
                        console.log('formRef', formRef)
                    },
                    click(value, formData, formRef,grid) {
                        console.log('click', value)
                        console.log('formData', formData)
                        console.log('formRef', formRef)
                        console.log('grid', grid)

                        grid.openPrint({})

                        // console.log(value)
                    }
                },
            },
        },
        // {
        //     field: 'sname', title: '菜单名称', width: '30%', treeNode: true,//字段名,标题,宽度,是否树节点
        //     cellRender: {
        //         name: 'VxeText',//组件名称
        //         events: {
        //             async click(cellParams, params) {
        //                 console.log(cellParams.row)
        //                 //根据deptId查询人员信息
        //                 let res = await getDeptUserList({ deptId: cellParams.row.deptId, queryChild: "false" })
        //                 console.log(res.data.records)
        //                 // gridOptions.data = res.data.records
        //             }
        //         }
        //     }
        // },
        { field: 'icon', title: '菜单类型', width: '18%' },
        {
            field: 'url', title: '菜单分类', width: '18%',
            visibleEvent: (data) => {
                console.log('菜单分类',data)
                return true
            }
        },
    ],
    height: "auto",
    proxyConfig: {
        sort: true,//是否开启排序
        form: true,//是否开启表单
        response: {
            result: 'data',//返回数据
        },
        ajax: {
            async query({ page, form, sorts }) {
                const params = {
                    ...page,
                    ...form
                }
                let res = await getList(params)
                return res.data.list
            }
        }
    }
})

const gridOptions2 = reactive<ExtendedGridProps>({
    headerAlign: 'center',//表头对齐方式
    border: true,//是否显示边框
    scrollY: { enabled: false },//是否开启滚动
    treeConfig: {
        rowField: 'menuId',//行唯一标识
        childrenField: 'children'//子节点字段名
    },
    tools: formOptions,
    columns: [
        { type: 'seq', width: "14%", fixed: 'left', align: 'center' },
        {
            field: 'action',//字段名
            title: '操作',//标题
            width: '20%',//宽度
            cellRenders: [//单元格渲染
                {
                    name: 'VxeIcon',
                    props: {
                        size: 'medium',//组件属性
                        name: 'setting',//组件属性
                        width: '33%',//组件属性
                        status: 'primary'//组件属性
                    },
                    events: {
                        click(cellParams, params) {
                            VxeUI.modal.message({
                                content: '点击了',
                                status: 'success'
                            })
                        }
                    }
                },
                {
                    name: 'VxeIcon',
                    props: {
                        size: 'medium',
                        name: 'home',
                        width: '33%',
                        status: 'success'
                    },
                    events: {
                        click(cellParams, params) {
                            VxeUI.modal.message({
                                content: '点击了',
                                status: 'success'
                            })
                        }
                    }
                },
                {
                    name: 'VxeIcon',
                    props: {
                        size: 'medium',
                        name: 'home',
                        width: '33%',
                        status: 'warning'
                    },
                    events: {
                        click(cellParams, params) {
                            VxeUI.modal.message({
                                content: '点击了',
                                status: 'success'
                            })
                        }
                    }
                }
            ]
        },
        // {
        //     field: 'sname', title: '菜单名称', width: '30%', treeNode: true,//字段名,标题,宽度,是否树节点
        //     cellRender: {
        //         name: 'VxeText',//组件名称
        //         events: {
        //             async click(cellParams, params) {
        //                 console.log(cellParams.row)
        //                 //根据deptId查询人员信息
        //                 let res = await getDeptUserList({ deptId: cellParams.row.deptId, queryChild: "false" })
        //                 console.log(res.data.records)
        //                 // gridOptions.data = res.data.records
        //             }
        //         }
        //     }
        // },
        { field: 'icon', title: '菜单类型', width: '18%' },
        {
            field: 'url', title: '菜单分类', width: '18%',
            // visible:false,
            visibleEvent: (data) => {
                return true
            }
        },
    ],
    height: "auto",
    proxyConfig: {
        sort: true,//是否开启排序
        form: true,//是否开启表单
        response: {
            result: 'data',//返回数据
        },
        ajax: {
            async query({ page, form, sorts }) {
                const params = {
                    ...page,
                    ...form
                }
                let res = await getList(params)
                return res.data.list
            }
        }
    }
})

// 添加一个响应式变量来控制当前显示的 gridOptions
let currentType = ref<string>('1')
const currentGridOptions = computed(() => {
    // console.log(currentType.value)
    return currentType.value === '1' ? gridOptions1 : gridOptions2
})

</script>
