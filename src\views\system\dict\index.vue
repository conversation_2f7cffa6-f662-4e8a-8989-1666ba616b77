<!--
* Description: 字典管理
* Created by huqingchao
* Date: 2024/10/29 18:16
* Update: 2024/10/29 18:16
-->
<script setup>
import useRouteTitle from "@/hooks/useRouteTitle.js";
import {onMounted, reactive, ref} from "vue";
import {getDictPage, saveOrUpdateDict, removeDict} from "@/api/methods/dict";
import {iconDel, iconEditorBackground, iconPlus} from "@opentiny/vue-icon";
import ruleMap from '@/utils/ruleMap.js'
import {Modal} from "@opentiny/vue";
import { useOperatePermission } from '@/hooks/useOperatePermission.js'
import { useUserStore } from '@/stores/user'
import { VxeUI, VxeTag } from 'vxe-pc-ui'
import {useCollapse} from '@/hooks/useCollapse.js'
const { btnPermission } = useOperatePermission()
const tableParentRef = ref(null)
const { tableWrapperHeight } = useCollapse(tableParentRef)

const TinyIconEditorBackground = iconEditorBackground()
const TinyIconDel = iconDel()
const TinyIconPlus = iconPlus()


const { title } = useRouteTitle()
const searchForm = reactive({
  sname: '',
  value: '',
})
const searchFormRef = ref()
const handleSearch = () => {
  gridOptions.pagerConfig.currentPage = 1
  handlePageData()
}

const handleResetSearch = () => {
  searchFormRef.value.resetFields()
  handleSearch()
}

const handlePageData = () => {
  gridOptions.loading = true
  getDictPage({
    ...searchForm,
    pageNumber: gridOptions.pagerConfig.currentPage,
    pageSize: gridOptions.pagerConfig.pageSize
  }).then(res => {
    gridOptions.data = res.data.records
    gridOptions.pagerConfig.total = res.data.totalPage
    gridOptions.loading = false
  })
}
const gridOptions = reactive({
  showOverflow: true,
  border: true,
  loading: false,
  height: 'auto',
  pagerConfig: {
    total: 0,
    currentPage: 1,
    pageSize: 10,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
    keyField: 'dictId'
  },
  treeConfig: {
    rowField: 'dictId',
    parentField: 'pid',
    childrenField: 'children',
    reserve: true
  },
  columns: [
    { field: 'name', title: '操作', width: 150, slots: { default: 'operate_default' } },
    { field: 'value', title: '字典编号',treeNode: true },
    { field: 'sname', title: '字典名称' },
    { field: 'sort', title: '排序',width: 120 },
    { field: 'updateUser', title: '更新人' },
    { field: 'updateDate', title: '更新时间' },
  ],
  data: []
})
const gridEvents = {
  pageChange({pageSize, currentPage}) {
    gridOptions.pagerConfig.currentPage = currentPage
    gridOptions.pagerConfig.pageSize = pageSize
    handlePageData()
  }
}

const boxVisibility = ref(false)

const dataForm = reactive({
  sname: null,
  sort: null,
  remark: null,
  value:null
})

const validatePass = (rule, value, callback) => {
  if (!value || ruleMap['positiveInteger']['reg'].test(value)) {
    callback()
  } else {
    callback(new Error(ruleMap['positiveInteger']['message']))
  }
}

const dataRules = ref({
  sort: [
    { validator: validatePass, trigger: 'blur' }
  ],
})

const formRef = ref()
const operateType = ref('add')
const handleAdd = () => {
  operateType.value = 'root'
  boxVisibility.value = true
}

const currentRow = ref()

const handleOperate = (row, type) => {
  currentRow.value = row
  operateType.value = type
  boxVisibility.value = true
  if (type === 'edit') {
    dataForm.sname = currentRow.value.sname
    dataForm.sort = currentRow.value.sort
    dataForm.remark = currentRow.value.remark
    dataForm.value=currentRow.value.value
  }
}

const handleDelete = row => {
  console.log(JSON.stringify(row))
  if(row.children.length>0){
    VxeUI.modal.message({
    content: '不允许删除拥有子集的字典',
    status: 'error'
})
return
  }
  Modal.confirm('您确定要删除吗？').then((res) => {
    if (res === 'confirm') {
      saveOrUpdateDict({
        id: row.id,
        dictId:row.dictId,
        isFlag :'10001002'
      }).then(res => {
        Modal.message({
          status: 'success',
          message: '删除成功',
        })
        handleResetSearch()
      })
    }
  })
}

const handleSave = () => {
  let createUserid = useUserStore().userInfo.userId
  formRef.value.validate(valid => {
    if (valid) {
      let params = dataForm
      console.log(JSON.stringify(currentRow.value))

      if (operateType.value === 'edit') {
        params = {
          ...params,
          dictId: currentRow.value.dictId,
          pid: currentRow.value.pid,
          updateUserid:createUserid
        }
      } else if (operateType.value === 'add') {
        params.pid = currentRow.value.dictId
        params.createUserid=createUserid
      } else if (operateType.value === 'root') {
        params.pid = 0
        params.createUserid=createUserid
      }

      saveOrUpdateDict(params).then(res => {
        boxVisibility.value = false
        handleResetSearch()
      })
    }
  })

}

onMounted(() => {
  handlePageData()
})

</script>

<template>
<div class="dict">
  <div class="base-search">
    <div class="base-title"> {{ title }}</div>
    <tiny-form ref="searchFormRef" :model="searchForm" label-width="120px" size="small" @keyup.enter="handleSearch">
      <tiny-layout :cols="12">
        <tiny-row>
          <tiny-col :span="3">
            <tiny-form-item label="字典编号" prop="value">
              <tiny-input v-model="searchForm.value" clearable placeholder="请输入"></tiny-input>
            </tiny-form-item>
          </tiny-col>
          <tiny-col :span="3">
            <tiny-form-item label="字典名称" prop="sname">
              <tiny-input v-model="searchForm.sname" clearable placeholder="请输入"></tiny-input>
            </tiny-form-item>
          </tiny-col>
          <tiny-col :span="6">
            <div class="base-search-operate">
              <tiny-button size="small" @click="handleResetSearch">重置</tiny-button>
              <tiny-button size="small" type="info" @click="handleSearch">查询</tiny-button>
            </div>
          </tiny-col>
        </tiny-row>
      </tiny-layout>
    </tiny-form>
  </div>
  <div class="base-content">
    <div class="base-content-operate">
      <tiny-button type="info" size="small" @click="handleAdd" v-if="btnPermission.add">新增字典组</tiny-button>
<!--      <tiny-button type="primary" size="small" plain>导出</tiny-button>-->
    </div>
    <div ref="tableParentRef" :style="{ height: tableWrapperHeight, overflow: 'hidden' }">
      <vxe-grid v-bind="gridOptions" v-on="gridEvents">
        <template #operate_default="{ row }">
          <div class="base-table-operate__icon2">
            <div v-if="btnPermission.add" class="icon-wrap" @click="handleOperate(row, 'add')"><TinyIconPlus></TinyIconPlus></div>
            <div v-if="btnPermission.edit" class="icon-wrap" @click="handleOperate(row, 'edit')"><TinyIconEditorBackground></TinyIconEditorBackground></div>
            <div v-if="btnPermission.delete" class="icon-wrap" @click="handleDelete(row)"><TinyIconDel></TinyIconDel></div>
          </div>
        </template>
      </vxe-grid>
    </div>
  </div>
  <tiny-dialog-box v-model:visible="boxVisibility" :title="operateType === 'edit' ? '编辑字典' : '新增字典'" width="800px" class="base-dialog-box">
    <tiny-form ref="formRef" :model="dataForm" :rules="dataRules" label-width="150px" class="base-form-border">
      <tiny-form-item label="字典名称" prop="sname" required>
        <tiny-input v-model="dataForm.sname" placeholder="请输入" clearable></tiny-input>
      </tiny-form-item>
      <tiny-form-item label="字典编号" prop="value" required>
        <tiny-input v-model="dataForm.value" placeholder="请输入" clearable></tiny-input>
      </tiny-form-item>
      <tiny-form-item label="排序" prop="sort">
        <tiny-input v-model="dataForm.sort" placeholder="请输入" clearable></tiny-input>
      </tiny-form-item>
      <tiny-form-item label="备注" prop="remark">
        <tiny-input v-model="dataForm.remark" placeholder="请输入" type="textarea" clearable></tiny-input>
      </tiny-form-item>
    </tiny-form>
    <template #footer>
      <tiny-button @click="boxVisibility = false">取消</tiny-button>
      <tiny-button type="info" @click="handleSave">确定</tiny-button>
    </template>
  </tiny-dialog-box>
</div>
</template>

<style scoped lang="less">

</style>
