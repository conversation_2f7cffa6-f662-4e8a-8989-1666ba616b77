/**
* Description: vxe-table自定义格式
* Created by huqingchao
* Date: 2024/10/15 09:05
* Update: 2024/10/15 09:05
*/
import { VxeUI } from 'vxe-pc-ui'
import XEUtils from 'xe-utils'
// 格式化日期，yyyy-MM-dd HH:mm:ss
VxeUI.formats.add('formatDate', {
  cellFormatMethod ({ cellValue }, format = 'yyyy-MM-dd HH:mm:ss') {
    return XEUtils.toDateString(cellValue, format)
  }
})

// 格式化日期，yyyy-MM-dd
VxeUI.formats.add('formatDay', {
  cellFormatMethod ({ cellValue }, format = 'yyyy-MM-dd') {
    return XEUtils.toDateString(cellValue, format)
  }
})

// 格式化税率
VxeUI.formats.add('formatRate', {
  cellFormatMethod ({ cellValue }, format = 'yyyy-MM-dd') {
    return cellValue ? `${cellValue}%` : cellValue
  }
})
