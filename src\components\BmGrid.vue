<template>
  <vxe-grid v-bind="gridProps" v-on="$attrs" ref="grid">
    <template v-for="(_, name) in $slots" #[name]="slotScope">
      <slot :name="name" v-bind="slotScope"></slot>
    </template>
    <template #toolbarTools>
      <template v-if="gridOptions.tools">
        <bm-form :form-options="gridOptions.tools"></bm-form>
      </template>
    </template>
  </vxe-grid>
</template>

<script lang="tsx" setup>
import { computed, ref, h, reactive, watch, onMounted, onUpdated } from 'vue'
import { useMainStore } from '@/utils/store'
const mainStore = useMainStore()
import request from '@/api/index.js'
import { ExtendedGridProps, ExtendedColumn, ExtendedCellRender, ExtendedFormItemProps, ExtendedFormItemRender,selectApiConfig,KeyConfig } from '@/types/grid'
import { VxeTag, VxeButton, VxeSelect, VxeInput, VxeSwitch, VxeButtonGroup, VxeIcon, VxeCheckbox, VxeImage, VxeImageGroup, VxeRadio, VxeTextarea, VxeRadioGroup, VxeText, VxeDatePicker, VxeUpload } from 'vxe-pc-ui'
import BmForm from './BmForm.vue'

const props = withDefaults(defineProps<{
  gridOptions: ExtendedGridProps
}>(), {
  gridOptions: () => ({})
})

window.addEventListener('load', function() {
    // 页面完全加载完成后执行的代码
    alert('整个页面，包括所有资源（图片、样式等），都已加载完成！');
})

// 获取字典数据
const dictData = (mainStore as any).getValue('dictData') || {}

/**
 * 处理字典渲染
 * @param column 列配置对象
 * @returns 处理后的列配置
 */
const handleDictRender = (column: ExtendedColumn) => {
  if (column.dict) {
    return {
      ...column,
      cellRender: {
        name: 'VxeSelect',
        props: {
          disabled: true
        },
        options: dictData[column.dict]
      }
    }
  }
  return column
}

/**
 * 为树结构数据添加 _id 和 _level 属性
 * @param treeData 树数据对象
 * @param childrenField 子节点字段名，默认为'children'
 * @param parentId 父节点ID，用于递归调用，默认为'0'
 */
function addTreeNodeLevel(
  treeData: Record<string, any>,
  childrenField: string = 'children',
  parentId: string = '0'
): void {
  // 设置当前节点的_id
  treeData['_id'] = parentId;

  // 计算当前节点的层级
  const level = parentId === '0' ? 1 : parentId.split('.').length;
  treeData['_level'] = level;

  // 处理子节点
  if (treeData[childrenField] && Array.isArray(treeData[childrenField]) && treeData[childrenField].length > 0) {
    for (let i = 0; i < treeData[childrenField].length; i++) {
      let childNode = treeData[childrenField][i];
      // 子节点的ID格式为：父节点ID.索引+1
      let childId = `${parentId === '0' ? '' : parentId + '.'}${i + 1}`;
      // 递归处理子节点
      addTreeNodeLevel(childNode, childrenField, childId);
    }
  } else {
    // 没有子节点，标记为叶子节点
    treeData['_end'] = '1';
  }
}

/**
 * 为树结构数据数组添加 _id 和 _level 属性
 * @param treeDataArray 树数据数组
 * @param childrenField 子节点字段名，默认为'children'
 */
function processTreeData(
  treeDataArray: Record<string, any>[],
  childrenField: string = 'children'
): void {
  if (!Array.isArray(treeDataArray)) return;

  for (let i = 0; i < treeDataArray.length; i++) {
    let node = treeDataArray[i];
    // 根节点ID格式为索引+1
    addTreeNodeLevel(node, childrenField, `${i + 1}`);
  }
}


/**
 * 给表格数据添加下标
 */
const setTableDataIndex = async () => {
  const tableData = gridProps.value.data || [];
  // const tableData = grid.value?.getTableData() || []
  processTreeData(tableData, 'children');
  //把数据设置到表格中

  console.log("setTableDataIndex",tableData)
}


/**
 * 设置列的可见性
 */
const setColumnsVisible = async () => {
  const options = { ...props.gridOptions }
  if (options.columns) {
    const tableData = grid.value?.getTableData() || []
    // console.log("onBeforeMount",tableData.fullData)
    for (const column of options.columns) {
      if (column.visibleEvent) {
        column.visible = Boolean(column.visibleEvent(tableData))
      }
    }
  }
}

/**
 * 初始化所有selectApi数据
 * @description 组件初始化时加载所有需要远程获取的下拉选项数据
 */
const initSelectApiData = async () => {
  const options = { ...props.gridOptions }
  if (options.columns) {
    for (const column of options.columns) {
      if (column.cellRenders) {
        for (const render of column.cellRenders) {
          if (render.selectApi) {
            render.options = await fetchOptionsFromApi(render.selectApi)
            // console.log(apiData)
          }
        }
      }
    }
  }

  const formConfig = props.gridOptions.formConfig || {}
  if (formConfig.items) {
    for (const item of formConfig.items) {
      if (item.itemRender?.name === "VxeSelect") {
        if (item.itemRender?.options && item.itemRender.options.length > 0) {
          continue
        } else if (item.itemRender?.dict) {
          // 如果有字典配置，从字典获取数据
          item.itemRender.options = dictData[item.itemRender.dict]
          continue
        } else if (item.itemRender?.selectApi) {
          item.itemRender.options = await fetchOptionsFromApi(item.itemRender.selectApi)

        }
      }

    }
  }
}

/**
 * 处理单个单元格渲染器
 * @param column 列配置对象
 * @returns 处理后的列配置
 */
const handleCellRender = (column: ExtendedColumn): ExtendedColumn => {
  if (!column.cellRender) return column

  const { name, props = {}, events = {}, options = [], selectApi, dict, visibleEvent } = column.cellRender

  let finalOptions = options

  // 处理选项数据的优先级：options > dict > selectApi
  if (options && options.length > 0) {
    finalOptions = options
  } else if (dict) {
    // 如果有字典配置，从字典获取数据
    finalOptions = dictData[dict]
  } else if (selectApi) {
    // 如果有API配置，从API获取数据

  }

  if (name === 'VxeText') {
    return {
      ...column,
      slots: {
        default: (params: any) => {
          return h(componentMap.get(name || ''), {
            ...props,
            style: {
              ...props.style
            },
            onClick: (e: Event) => {
              if (events.click) events.click(params, { name, ...props })
            }
          }, params.row[params.column.field]) as any
        }
      }
    }
  }


  return {
    ...column,
    slots: {
      default: (params: any) => {
        // 检查 visibleEvent 条件
        if (visibleEvent && !visibleEvent(params.row)) {
          return null as any // 如果不可见则返回空
        }

        // 为单元格组件创建一个唯一ID
        const rowId = params.row._id || params.row.id || JSON.stringify(params.row)
        const componentId = `${rowId}!${params.column.field}`

        // 如果组件值尚未设置或与行数据不同步，则更新组件值
        if (componentValues.get(componentId) !== params.row[params.column.field]) {
          componentValues.set(componentId, params.row[params.column.field])
        }

        // 原有的渲染逻辑// 根据组件名称调整渲染逻辑
        if (name === 'VxeText') {
          // return h(componentMap.get(name), {
          //   ...props,
          //   content: params.row[params.column.field], // VxeText 使用 content 属性
          //   onClick: (e: Event) => {
          //     if (events.click) events.click(params.row, { column: params.column, ...props }, e, grid.value)
          //   }
          // }) as any
        } else {
          // 原有的渲染逻辑
          return h(componentMap.get(name || ''), {
            ...props,
            modelValue: componentValues.get(componentId),
            'onUpdate:modelValue': (value: any) => {
              componentValues.set(componentId, value)
              params.row[params.column.field] = value
            },
            // 正确地映射事件
            onClick: (e: Event) => {
              if (events.click) events.click(params.row, { column: params.column, ...props }, e, grid.value)
            },
            onChange: (value: any) => {
              componentValues.set(componentId, value)
              console.log('componentId', componentId + ' @ ' + value.value)
              if (events.change) {
                if (options && options.length > 0) {
                  const selectedOption = options.find((opt: any) =>
                    opt.value === value.value || opt.name === value.value
                  )
                  events.change(value.value, selectedOption || { name: value.value }, params.row, grid.value)
                } else {
                  events.change(value.value, params.row, grid.value)
                }
              }
            },
            options
          }) as any
        }
      }
    }
  }
}

/**
 * 从API获取选项数据
 */
 const fetchOptionsFromApi = async (selectApi: selectApiConfig) => {
  try {
    const response = await request.post(selectApi.url || '', selectApi.body)
    const data = Array.isArray(response as any) ? (response as any) : ((response as any).data || [])

    // 递归处理数据的函数
    const mapDataRecursively = (items: any[]): any[] => {
      return items.map((item: any) => {
        const result = {
          label: item[selectApi.config?.keyFieldName || ''],
          value: item[selectApi.config?.valueFieldName || ''],
          children: [] as any[]
        }

        // 如果存在children字段并且是数组，递归处理
        if (selectApi.config?.childrenFieldName &&
            item[selectApi.config.childrenFieldName] &&
            Array.isArray(item[selectApi.config.childrenFieldName]) &&
            item[selectApi.config.childrenFieldName].length > 0) {
          result.children = mapDataRecursively(item[selectApi.config.childrenFieldName])
        }
        return result
      })
    }

    // 使用递归函数处理数据
    const mappedData = mapDataRecursively(data)

    console.log('递归处理后的数据', mappedData)

    return mappedData
  } catch (error) {
    console.error('Failed to fetch options from API:', error)
    return []
  }
}


/**
 * 处理自定义单元格渲染器（多组件）
 * @param column 列配置对象
 * @returns 处理后的列配置
 */
const handleCellRenders = (column: ExtendedColumn): ExtendedColumn => {
  if (!column.cellRenders) return column

  return {
    ...column,
    slots: {
      ...column.slots,
      default: (params: any) => {
        const totalComponents = column.cellRenders?.length || 0

        // 同步行数据到组件状态
        // 当 params.row[params.column.field] 发生变化时，更新对应的组件值
        if (params.row[params.column.field]) {
          const values = String(params.row[params.column.field]).split(',')

          // 遍历所有渲染器，更新对应的组件值
          column.cellRenders?.forEach((render: ExtendedCellRender, index: number) => {
            // 跳过不可见的组件
            if (render.visibleEvent && !render.visibleEvent(params.row)) {
              return
            }

            const rowId = params.row._id || params.row.id || JSON.stringify(params.row)
            const componentId = `${rowId}!${params.column.field}!${index}`

            // 只有当值存在且不为'null'时才更新
            if (values.length > index && values[index] !== 'null') {
              componentValues.set(componentId, values[index])
            }
          })
        }

        return h('div',
          {
            style: {
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              width: '100%'
            }
          },
          (column.cellRenders || []).map((render: ExtendedCellRender, index: number) => {
            // 检查每个渲染器的 visibleEvent 条件
            if (render.visibleEvent && !render.visibleEvent(params.row)) {
              return null as any
            }

            const { name, props = {}, events = {}, dict, selectApi } = render
            //添加rowId进行区分
            const rowId = params.row._id || params.row.id || JSON.stringify(params.row)
            const componentId = `${rowId}!${params.column.field}!${index}`

            // 初始化组件值 - 如果组件值尚未设置，则从行数据中提取
            if (!componentValues.has(componentId) && params.row[params.column.field]) {
              // 尝试从逗号分隔的字符串中提取对应索引的值
              const values = String(params.row[params.column.field]).split(',')
              if (values.length > index && values[index] !== 'null') {
                componentValues.set(componentId, values[index])
              }
            }

            let options = render.options

            //如果同时存在 options
            if (dict) {
              //如果字典属性存在，则进行字典渲染
              options = dictData[dict]
            }

            let componentWidth = props.width
            if (!componentWidth) {
              componentWidth = `calc(${100 / totalComponents}% - ${(totalComponents - 1) * 8 / totalComponents}px)`
            } else if (typeof componentWidth === 'string' && componentWidth.includes('%')) {
              componentWidth = props.width
            } else {
              componentWidth = `${props.width}px`
            }

            return h(componentMap.get(name || ''), {
              ...props,
              style: {
                width: '100%',
                ...props.style
              },
              options,
              modelValue: componentValues.get(componentId),
              'onUpdate:modelValue': (value: any) => {
                componentValues.set(componentId, value.value)
              },
              onClick: (e: Event) => {
                if (events.click) {
                  events.click(params.row, { name, ...props, componentId },e,grid.value)
                }
              },
              onChange: (value: any) => {
                componentValues.set(componentId, value.value)
                console.log('componentId',componentId + ' @ ' + value.value)
                if (events.change) {
                  if(options && options.length > 0){
                    const selectedOption = options.find((opt: any) =>
                      opt.value === value.value || opt.name === value.value
                    )
                    events.change(value.value, selectedOption || { name: value.value },params.row,grid.value)
                  }else{
                    events.change(value.value, params.row, grid.value)
                  }

                  // 获取所有组件的值
                  const allValues = (column.cellRenders || []).map((render, i) => {
                    // 如果组件有visibleEvent并且不可见，则保持原值
                    if (render.visibleEvent && !render.visibleEvent(params.row)) {
                      // 尝试从原始值中获取
                      if (params.row[params.column.field]) {
                        const originalValues = String(params.row[params.column.field]).split(',')
                        if (originalValues.length > i) {
                          return originalValues[i]
                        }
                      }
                      return 'null'
                    }

                    const rowId = params.row._id || params.row.id || JSON.stringify(params.row)
                    const id = `${rowId}!${params.column.field}!${i}`
                    return componentValues.get(id) || 'null'
                  })

                  // 更新 params.row[params.column.field] 的值
                  params.row[params.column.field] = allValues.join(',')
                  console.log('子组件当前值:', params.row[params.column.field])
                }
              }
            }) as any
          })
        )
      }
    }
  }
}

// 组件映射
const componentMap = new Map<string, any>([
  ['VxeTag', VxeTag],
  ['VxeButton', VxeButton],
  ['VxeSelect', VxeSelect],
  ['VxeInput', VxeInput],
  ['VxeSwitch', VxeSwitch],
  ['VxeButtonGroup', VxeButtonGroup],
  ['VxeIcon', VxeIcon],
  ['VxeCheckbox', VxeCheckbox],
  ['VxeImage', VxeImage],
  ['VxeImageGroup', VxeImageGroup],
  ['VxeRadio', VxeRadio],
  ['VxeTextarea', VxeTextarea],
  ['VxeRadioGroup', VxeRadioGroup],
  ['VxeText', VxeText],
  ['VxeDatePicker', VxeDatePicker],
  ['VxeUpload', VxeUpload]
])

// 组件值存储
const componentValues = reactive(new Map())

/**
 * 处理表单项的渲染配置
 * @param item 表单项配置
 * @returns 处理后的表单项配置
 */
const handleFormItemRender = (item: ExtendedFormItemProps) => {
  if (!item.itemRender) return item

  const { name, props: itemProps = {}, events = {}, options = [], selectApi, dict } = item.itemRender as ExtendedFormItemRender
  const formData = props.gridOptions.formConfig?.formData || {}

  let finalOptions = options

  // 处理选项数据的优先级：options > dict > selectApi
  if (options && options.length > 0) {
    finalOptions = options
  } else if (dict) {
    // 如果有字典配置，从字典获取数据
    finalOptions = dictData[dict]
  } else if (selectApi) {
    // 如果有API配置，从API获取数据
    // 注意：这里需要在外部处理异步加载
    finalOptions = item.itemRender.options || []
  }

  return {
    ...item,
    itemRender: {
      ...item.itemRender,
      name,
      props: {
        ...itemProps,
        modelValue: item.field ? formData[item.field] : undefined,
      },
      events: {
        ...events,
        'update:modelValue': (value: any) => {
          if (formData && item.field) {
            formData[item.field] = value.data[item.field]
          }
        }
      },
      options: finalOptions,
      selectApi,
      dict,
    }
  }
}

/**
 * 初始化所有表单selectApi数据
 */
const initFormSelectApiData = async () => {
  const options = { ...props.gridOptions }
  if (options.formConfig?.items) {
    for (const item of options.formConfig.items) {
      if (item.itemRender?.selectApi) {
        const apiData = await fetchOptionsFromApi(item.itemRender.selectApi)
        if (item.itemRender) {
          item.itemRender.options = apiData
        }
      }
    }
  }
}

/**
 * 计算网格属性
 * @description 处理并转换网格的列配置，包括字典、组件和自定义渲染器的处理
 */
const gridProps = computed(() => {
  let options = props.gridOptions

  if (options.proxyConfig) {
    options['proxyConfig'] = {
      ...options.proxyConfig,
      ajax: {
        ...options.proxyConfig.ajax,
        querySuccess: ({ page, sort, sorts, filters, form, response }) => {
          // console.log('querySuccess',response)
          processTreeData(response, 'children');
          // console.log('querySuccess',grid.value)
          // options.data = response
        }
      }
    }
  }

  if (options.columns) {
    const processedColumns = options.columns.map(column => {
      let processedColumn: ExtendedColumn = { ...column }

      // 保留原始slots配置
      const originalSlots = processedColumn.slots
      // 处理字典渲染
      processedColumn = handleDictRender(processedColumn)
      // 处理组件渲染
      if (processedColumn.cellRender) {
        processedColumn = handleCellRender(processedColumn)
      }

      // 处理自定义单元格渲染器
      if (processedColumn.cellRenders) {
        processedColumn = handleCellRenders(processedColumn)
      }

      // 合并回原始slots配置
      if (originalSlots) {
        processedColumn.slots = {
          ...processedColumn.slots,
          ...originalSlots
        }
      }
      return processedColumn
    })

    options.columns = processedColumns
  }

  // 处理表单配置
  if (options.formConfig?.items) {
    const formData = options.formConfig.data || {}

    // 初始化表单数据
    options.formConfig.items.forEach(item => {
      if (item.field && !(item.field in formData)) {
        formData[item.field] = null
      }
    })

    options.formConfig.items = options.formConfig.items.map(item => handleFormItemRender(item))
  }

  // console.log(options.tools)

  // 处理工具栏配置
  options.toolbarConfig = {
    slots: {
      buttons: 'toolbarTools'
    }
  }

  return {
    ...options,
    formConfig: options.formConfig
  }
})

const grid = ref()

onMounted(async () => {
  await setTableDataIndex()
  await initSelectApiData()
  await initFormSelectApiData()
  //判断是否给指定字段添加 显示逻辑
  await setColumnsVisible()
  // 初始化组件值
  updateComponentValuesFromData()
})


// 处理工具栏按钮点击事件 这里暂时还为放出额外属性
const handleToolbarClick = (btn: any, index: number, gridInfo: any) => {
  if (btn.render?.events?.click) {
    let selectRows = grid.value.getCheckboxRecords(true)
    console.log(selectRows)
    btn.render.events.click(
      selectRows
    )
  }
}

// 添加watch监听formData变化
watch(
  () => props.gridOptions.formConfig?.formData,
  (newData) => {
    if (newData && props.gridOptions.formConfig?.items) {
      // 强制更新grid组件
      grid.value?.reloadForm()
    }
  },
  { deep: true } // 深度监听对象变化
)

// 监听 gridOptions 的变化
watch(
  () => props.gridOptions,
  async () => {
    await initSelectApiData()
    await initFormSelectApiData()
    await setColumnsVisible()
  },
  { deep: false } // 不监听内部的其他属性变化,深度监听会造成页面卡顿
)

// 监听表格数据变化，确保组件状态与行数据同步
watch(
  () => props.gridOptions.data,
  () => {
    // 当表格数据变化时，重新同步组件值
    updateComponentValuesFromData()
  },
  { deep: true }
)

/**
 * 从表格数据更新组件值
 * 当表格数据发生变化时，确保组件状态与行数据保持同步
 */
const updateComponentValuesFromData = () => {
  if (!Array.isArray(props.gridOptions.data) || !props.gridOptions.columns) return

  // 遍历所有行数据
  props.gridOptions.data.forEach((row: any) => {
    // 遍历所有列
    props.gridOptions.columns?.forEach((column: ExtendedColumn) => {
      // 只处理有 cellRenders 的列
      if (column.cellRenders && column.field && row[column.field]) {
        const values = String(row[column.field]).split(',')

        // 遍历所有渲染器，更新对应的组件值
        column.cellRenders.forEach((render: ExtendedCellRender, index: number) => {
          // 跳过不可见的组件
          if (render.visibleEvent && !render.visibleEvent(row)) {
            return
          }

          const rowId = row._id || row.id || JSON.stringify(row)
          const componentId = `${rowId}!${column.field}!${index}`

          // 只有当值存在且不为'null'时才更新
          if (values.length > index && values[index] !== 'null') {
            componentValues.set(componentId, values[index])
          }
        })
      }
    })
  })
}


defineExpose({
  getTableInstance: () => {
    return grid.value
  },
  // 添加刷新组件值的方法，供外部调用
  refreshComponentValues: () => {
    updateComponentValuesFromData()
  }
})

</script>
