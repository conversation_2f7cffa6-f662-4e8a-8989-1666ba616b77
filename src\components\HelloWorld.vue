<template>
  <div>
    <vxe-form ref="formRef" :data="formData" @submit="submitEvent" @reset="resetEvent">
      <vxe-form-item title="名称" field="name" span="24" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.name" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="性别" field="sex" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.sex" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item title="年龄" field="age" span="12" :item-render="{}">
        <template #default="params">
          <vxe-input v-model="formData.age" @change="changeEvent(params)"></vxe-input>
        </template>
      </vxe-form-item>
      <vxe-form-item align="center" span="24" :item-render="{}">
        <template #default>
          <vxe-button type="submit" status="primary" content="提交"></vxe-button>
          <vxe-button type="reset" content="重置"></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>
    <tiny-layout>
      <tiny-row>
        <tiny-button>默认按钮</tiny-button>
        <tiny-button type="primary" native-type="submit"> 主要按钮 </tiny-button>
        <tiny-button type="success"> 成功按钮 </tiny-button>
        <tiny-button type="info"> 信息按钮 </tiny-button>
        <tiny-button type="warning"> 警告按钮 </tiny-button>
        <tiny-button type="danger"> 危险按钮 </tiny-button>
      </tiny-row>
      <tiny-row>
        <tiny-button plain> 朴素按钮 </tiny-button>
        <tiny-button type="primary" plain> 主要按钮 </tiny-button>
        <tiny-button type="success" plain> 成功按钮 </tiny-button>
        <tiny-button type="info" plain> 信息按钮 </tiny-button>
        <tiny-button type="warning" plain> 警告按钮 </tiny-button>
        <tiny-button type="danger" plain> 危险按钮 </tiny-button>
      </tiny-row>
      <tiny-row>
        <tiny-button round> 圆角按钮 </tiny-button>
        <tiny-button type="primary" round> 主要按钮 </tiny-button>
        <tiny-button type="success" round> 成功按钮 </tiny-button>
        <tiny-button type="info" round> 信息按钮 </tiny-button>
        <tiny-button type="warning" round> 警告按钮 </tiny-button>
        <tiny-button type="danger" round> 危险按钮 </tiny-button>
      </tiny-row>
      <tiny-row>
        <tiny-button :icon="IconSearch" circle></tiny-button>
        <tiny-button type="primary" :icon="IconEdit" circle></tiny-button>
        <tiny-button type="success" :icon="IconYes" circle></tiny-button>
        <tiny-button type="info" :icon="IconMail" circle></tiny-button>
        <tiny-button type="warning" :icon="IconStarO" circle></tiny-button>
        <tiny-button type="danger" :icon="IconDel" circle></tiny-button>
      </tiny-row>
    </tiny-layout>
    <!--    <lay-button type="danger" @click="alertEvent">警告按钮</lay-button>
  <lay-icon type="layui-icon-face-smile"></lay-icon> &nbsp;
  <lay-icon type="layui-icon-face-smile" color="#009688"></lay-icon> &nbsp;
  <lay-icon type="layui-icon-face-smile" color="#5FB878"></lay-icon> &nbsp;
  <lay-icon type="layui-icon-face-smile" color="#1E9FFF"></lay-icon> &nbsp;
  <lay-icon type="layui-icon-face-smile" color="#FFB800"></lay-icon> &nbsp;
  <lay-icon type="layui-icon-face-smile" color="#FF5722" size="24px"></lay-icon> &nbsp;
  <AlignCenterIcon></AlignCenterIcon>
  <DiamondIcon></DiamondIcon>
  <lay-json-schema-form :model="form" :schema="schema1" ></lay-json-schema-form>
  <lay-button type="primary" @click="submit1">提交</lay-button>-->

    <div>
      <!--      <button @click="saveObjectData({ name: 'John Doe', age: 30 })">Save Object Data</button>-->
      <!--      <p>Saved Data: {{ savedData }}</p>-->
      <p>Value for key1: {{ value1 }}</p>
      <p>Value for key2: {{ value2 }}</p>
      <p>Value for key3: {{ value3 }}</p>
      <p>Value for key4: {{ value4 }}</p>
      <button @click="setValue('key4', '123')">Add key4</button>
      <button @click="removeValue('key1')">Remove key1</button>
      <button @click="testlodash('key1')">Remove key2</button>
      <button @click="testEvent('key1')">Remove key3</button>
    </div>
    <!--  <div>-->
    <!--    <button @click="saveObjectData1({ name: 'John Doe1', age: 40 })">Save Object Data-1</button>-->
    <!--    <p>Saved Data: {{ savedData1 }}</p>-->
    <!--  </div>-->
  </div>
</template>

<script setup>
import {ref, reactive} from 'vue'
// import { mapState, mapActions } from 'vuex';
// import { VxeUI } from 'vxe-pc-ui'
//这2个组件ICON必须引入才能生效 全局配置完之后 就不需要额外引入了
// import { AlignCenterIcon, DiamondIcon } from '@layui/icons-vue';
import { iconDel, iconYes, iconEdit, iconMail, iconStarO, iconSearch } from '@opentiny/vue-icon'

const IconDel = iconDel()
const IconYes = iconYes()
const IconEdit = iconEdit()
const IconMail = iconMail()
const IconStarO = iconStarO()
const IconSearch = iconSearch()
const formRef = ref()
const formData = ref({
  name: 'test1',
  nickname: 'Testing',
  sex: '',
  age: ''
})
const changeEvent = (params) => {
  const $form = formRef.value
  if ($form) {
    $form.updateStatus(params)
  }
}
const submitEvent = () => {
  VxeUI.modal.message({content: '保存成功', status: 'success'})
}
const resetEvent = () => {
  VxeUI.modal.message({content: '重置事件', status: 'info'})
}

const alertEvent = () => {
  // layer.msg("成功消息", { icon : 1, time: 1000})
}

const form = reactive({
  name: '凡凡',
  password: '',
  like: '',
  textarea: '',
  switch: true,
  date: '',
  rate: 0,
  radio: 0,
  checkbox: [0]
})

const schema1 = reactive({
  name: {
    label: '姓名',
    type: 'input',
    props: {
      type: 'text',
      placeholder: '请输入姓名',
    }
  },
  password: {
    label: '密码',
    type: 'input',
    props: {
      autocomplete: "off",
      type: 'password',
      placeholder: '请输入密码',
    }
  },
  like: {
    label: '爱好',
    type: 'select',
    props: {
      options: [
        {label: '唱', value: '1'},
        {label: '跳', value: '2'},
        {label: 'rap', value: '3'},
        {label: '篮球', value: '4'}
      ],
      placeholder: '请选择爱好',
    }
  },
  remark: {
    label: '备注',
    type: 'textarea',
    props: {
      placeholder: '请输入备注',
    }
  },
  switch: {
    label: '备注',
    type: 'switch',
    props: {}
  },
  rate: {
    label: '评分',
    type: 'rate',
    props: {}
  },
  date: {
    label: '日期-date',
    type: 'date',
    props: {
      type: 'date'
    }
  },
  date1: {
    label: '日期-datetime',
    type: 'date',
    props: {
      type: 'datetime'
    }
  },
  radio: {
    label: '单选',
    type: 'radio',
    props: {
      options: [
        {
          label: "运动",
          value: 0,
        },
        {
          label: "编码",
          value: 1,
        },
        {
          label: "运动",
          value: 2,
        },
      ],
    }
  },
  radio1: {
    label: '单选-button',
    type: 'radio',
    props: {
      button: true,
      options: [
        {
          label: "运动",
          value: 0,
        },
        {
          label: "编码",
          value: 1,
        },
        {
          label: "运动",
          value: 2,
        },
      ],
    }
  },
  checkbox: {
    label: '多选',
    type: 'checkbox',
    props: {
      options: [
        {
          label: "运动",
          value: 0,
          skin: "primary"
        },
        {
          label: "编码",
          value: 1,
        },
        {
          label: "运动",
          value: 2,
        },
      ],
    }
  },
})


const submit1 = () => {
  // layer.msg(`${JSON.stringify(form)}`, { time: 2000 });
}

console.log(XEUtils.isFinite(NaN))

const value1 = computed(() => mainStore.getValue('key1'));
const value2 = computed(() => mainStore.getValue('key2'));
const value3 = computed(() => mainStore.getValue('key3'));
const value4 = computed(() => mainStore.getValue('key4'));

const setValue = (key, value) => {
  mainStore.setValue(key, value);
};

const removeValue = (key) => {
  mainStore.removeValue(key);
};

const testlodash = (key) => {
  console.log(lodash.compact([0, 1, false, 2, '', 3]))
}

const testEvent = (key) => {
  console.log(process.env.BASE_URL)
}

// import { useStore } from 'vuex';
//
// const store = useStore();
// const saveObjectData = (data) => {
//     store.dispatch('persist/saveObjectData', data);
// };
//
// const savedData = computed(() => {
//     const data = store.getters['persist/getData'];
//     return data ? JSON.parse(data) : null;
// });
//
// const saveObjectData1 = (data) => {
//   store.dispatch('persist1/saveObjectData', data);
// };
//
// const savedData1 = computed(() => {
//   const data = store.getters['persist1/getData'];
//   return data ? JSON.parse(data) : null;
// });

//多线程的使用
// const pool = thread.pool();
// const add = (a, b) => {
//     return a + b;
// }
//
// pool.exec(add, [3, 4])
//     .then(function (result) {
//         console.log('result', result); // outputs 7
//     })
//     .catch(function (err) {
//         console.error(err);
//     })
//     .then(function () {
//         pool.terminate(); // terminate all workers when done
//     });


// // create a worker pool using an external worker script
// const pool = thread.pool(__dirname + '/myWorker.js');
//
// // run registered functions on the worker via exec
// pool
//     .exec('fibonacci', [10])
//     .then(function (result) {
//         console.log('Result: ' + result); // outputs 55
//     })
//     .catch(function (err) {
//         console.error(err);
//     })
//     .then(function () {
//         pool.terminate(); // terminate all workers when done
//     });
//
// // or run registered functions on the worker via a proxy:
// pool
//     .proxy()
//     .then(function (worker) {
//         return worker.fibonacci(10);
//     })
//     .then(function (result) {
//         console.log('Result: ' + result); // outputs 55
//     })
//     .catch(function (err) {
//         console.error(err);
//     })
//     .then(function () {
//         pool.terminate(); // terminate all workers when done
//     });

</script>

<style scoped>
.read-the-docs {
  color: #888;
}
.tiny-row {
  margin-bottom: 20px;
}
.tiny-button {
  margin-bottom: 10px;
  margin-left: 0;
  margin-right: 24px;
}
</style>
