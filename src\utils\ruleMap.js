/**
* Description: 表单正则表达式
* Created by huqingchao
* Date: 2024/10/15 09:05
* Update: 2024/10/15 09:05
*/
// 正则集合
const ruleMap = {
  variable: {
    reg: /^[A-Za-z0-9_]+$/,
    message: '请输入字母(A-Z,  a-z)、数字(0-9)或下划线！',
  },
  variable1: {
    reg: /^[A-Za-z0-9]+$/,
    message: '请输入字母(A-Z,  a-z)、数字(0-9)！',
  },
  char: {
    reg: /^[.A-Za-z0-9_-]+$/,
    message: '只允许数字、字母、小数点、-和_'
  },
  positiveInteger: {
    reg: /^[1-9]\d*$/,
    message: '请输入正整数！'
  },
  integerAndDecimal: {
    reg: /^-?\d+(\.\d+)?$/,
    message: '请输入整数或者小数！'
  },
  onlyNumber: {
    reg: /^\d+$/,
    message: '请输入数字'
  }
}
// 自定义校验函数
const getValidator = (key) => {
  return (rule, value, callback) => {
    if (!value || ruleMap[key]['reg'].test(value)) {
      callback()
    } else {
      callback(new Error(ruleMap[key]['message']))
    }
  }
}

Object.keys(ruleMap).forEach(key => {
  if (!ruleMap[key]['validator']) {
    ruleMap[key]['validator'] = getValidator(key)
  }
})

export default ruleMap

