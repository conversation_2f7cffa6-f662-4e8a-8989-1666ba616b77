import { createPersistedState } from 'pinia-plugin-persistedstate';

const mapSerializer = {
    serialize: (map) => JSON.stringify(Array.from(map.entries())),
    deserialize: (data) => new Map(JSON.parse(data)),
};

export default function piniaPersist(pinia) {
    pinia.use(createPersistedState({
        storage: localStorage,
        serializer: {
            serialize: (state) => {
                const newState = { ...state };
                if (newState.values instanceof Map) {
                    newState.values = mapSerializer.serialize(newState.values);
                }
                return JSON.stringify(newState);
            },
            deserialize: (data) => {
                const state = JSON.parse(data);
                console.log(state.values)
                if (typeof state.values === 'string') {
                    state.values = mapSerializer.deserialize(state.values);
                }
                return state;
            },
        },
    }));
}