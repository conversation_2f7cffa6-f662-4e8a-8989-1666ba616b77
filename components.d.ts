/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    BmComponent: typeof import('./src/components/BmComponent.vue')['default']
    BmDialog: typeof import('./src/components/BmDialog.vue')['default']
    BmForm: typeof import('./src/components/BmForm.vue')['default']
    BmGrid: typeof import('./src/components/BmGrid.vue')['default']
    BmGridItem: typeof import('./src/components/gridlayout/BmGridItem.vue')['default']
    BmGridLayout: typeof import('./src/components/gridlayout/BmGridLayout.vue')['default']
    ECharts: typeof import('./src/components/ECharts.vue')['default']
    FormDialog: typeof import('./src/components/FormDialog.vue')['default']
    GroupFormDialog: typeof import('./src/components/GroupFormDialog.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    MoveBox: typeof import('./src/components/MoveBox.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TinyButton: typeof import('@opentiny/vue')['Button']
    TinyCol: typeof import('@opentiny/vue')['Col']
    TinyDatePicker: typeof import('@opentiny/vue')['DatePicker']
    TinyDialogBox: typeof import('@opentiny/vue')['DialogBox']
    TinyForm: typeof import('@opentiny/vue')['Form']
    TinyFormItem: typeof import('@opentiny/vue')['FormItem']
    TinyInput: typeof import('@opentiny/vue')['Input']
    TinyLayout: typeof import('@opentiny/vue')['Layout']
    TinyOption: typeof import('@opentiny/vue')['Option']
    TinyRow: typeof import('@opentiny/vue')['Row']
    TinySelect: typeof import('@opentiny/vue')['Select']
    TinyTag: typeof import('@opentiny/vue')['Tag']
  }
}
