<!--
* Description: 布局-左侧菜单
* Created by huqingchao
* Date: 2024/10/29 18:18
* Update: 2024/10/29 18:18
-->
<template>
    <vxe-menu
    class="left-menu"
    v-model="activeMenu"
    :options="userStore.userMenus"
    @click="handleNodeClick">
    <template #option="{ option }">
      <div class="menu-item-wrapper">
        <component class="svg-icon" v-if="option.icon" :is="svgMap[option.icon]"></component>
        <span>{{ option.title }}</span>
      </div>
    </template>
  </vxe-menu>
  
</template>

<script setup>
import {ref, watch, onMounted} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import { useUserStore } from '@/stores/user.js'
import MyCaseSvg from '@/assets/svg/myCase.svg'
import BusinessSvg from '@/assets/svg/business.svg'
import SystemSvg from '@/assets/svg/system.svg'

const svgMap = {
  myCase: MyCaseSvg,
  business: BusinessSvg,
  system: SystemSvg,
}

const router = useRouter();
const route = useRoute()
const userStore = useUserStore();
// 当前激活的菜单
const activeMenu = ref('')
// 初始化激活的菜单
const initActiveMenu = (name) => {
  activeMenu.value = name
}
onMounted(() => {
  initActiveMenu(route.name)
})
watch(route, ({ name }) => {
  initActiveMenu(name)
})
// 点击菜单项
const handleNodeClick = ({ menu }) => {
  menu.componentName && menu.menuType === '10002002' ? router.push({name: menu.componentName}) : ''
}
</script>

<style lang="less" scoped>
.menu-container {
  height: 100vh;
  overflow-y: auto; /* 添加垂直滚动条 */
}
.left-menu{
  font-size: var(--dx-ui-leftMenu-font-size);
  .menu-item-wrapper{
    display: flex;
    align-items: center;
  }
  .svg-icon{
    width: 18px;
    height: 18px;
    margin-bottom: 2px;
    margin-right: 10px;
  }
}
</style>
