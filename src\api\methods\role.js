/**
 * Description: role.js
 * Created by hu<PERSON>cha<PERSON>
 * Date: 2024/10/25 14:46
 * Update: 2024/10/25 14:46
 */
import request from '../index.js'
// 角色分页
export const getPage = function (data) {
  return request({
    url: '/sysRole/getPage',
    method: 'post',
    data
  })
}
// 角色不分页
export const getList = function (data) {
  return request({
    url: '/sysRole/list',
    method: 'post',
    data
  })
}

export const saveOrUpdate = function (data) {
  return request({
    url: '/sysRole/saveOrUpdate',
    method: 'post',
    data
  })
}

export const deleteById = function (data) {
  return request({
    url: '/sysRole/saveOrUpdate',
    method: 'post',
    data
  })
}

export const getOne = function (data) {
  return request({
    url: '/sysRole/getOne',
    method: 'post',
    data
  })
}

export const sysUserRoleGetPage = function (data) {
  return request({
    url: '/sysUser/getUserByRole',
    method: 'post',
    data
  })
}

export const sysUserRoleSaveOrUpdate = function (data) {
  return request({
    url: '/sysUserRole/saveSysUserRole',
    method: 'post',
    data
  })
}

export const getToUserList = function (data) {
  return request({
    url: '/sysUser/addUserForRole',
    method: 'post',
    data
  })
}

export const deleteByRoleUserId = function (data) {
  return request({
    url: '/sysUserRole/deleteEntity',
    method: 'post',
    data
  })
}

export const getMeansByRole = function (data) {
  return request({
    url: '/sysRoleMenu/getMeansByRole',
    method: 'post',
    data
  })
}

export const sysRoleMenuSaveOrUpdate = function (data) {
  return request({
    url: 'sysRoleMenu/saveRoleMenu',
    method: 'post',
    data
  })
}

export const sysRoleMenuBtnGetList = function (data) {
  return request({
    url: '/sysRoleMenuBtn/getList',
    method: 'post',
    data
  })
}

export const sysRoleMenuBtnSaveOrUpdate = function (data) {
  return request({
    url: '/sysRoleMenuTabBtn/saveDataByRole',
    method: 'post',
    data
  })
}

// 角色导出
export const sysRoleExport = function (data) {
  return request({
    url: '/sysRole/export',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
