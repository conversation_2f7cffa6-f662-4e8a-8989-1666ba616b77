{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "pre": "vite build --mode pre", "testing": "vite build --mode testing", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@alova/scene-vue": "^1.5.0", "@opentiny/vue": "^3.20.0", "@opentiny/vue-icon": "^3.18.0", "@opentiny/vue-theme": "^3.21.2", "@varlet/axle": "^0.7.1", "@vxe-ui/plugin-export-xlsx": "^4.2.1", "@vxe-ui/plugin-menu": "^4.0.9", "@vxe-ui/plugin-render-wangeditor": "^4.0.2", "axios": "^1.7.9", "dayjs": "^1.11.12", "dx-ui-var": "^0.0.1-beta.4", "grid-layout-plus": "^1.0.5", "lodash": "^4.17.21", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "vite-plugin-compression2": "^1.1.2", "vue": "^3.4.21", "vue-router": "^4.3.3", "vxe-pc-ui": "4.6.5", "vxe-table": "4.13.26", "workerpool": "^9.1.3", "xe-utils": "^3.7.4"}, "devDependencies": {"@opentiny/unplugin-tiny-vue": "^0.0.2", "@types/echarts": "^4.9.22", "@types/node": "^22.3.0", "@types/vue": "^2.0.0", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "@vitejs/plugin-vue": "^5.1.2", "@vitejs/plugin-vue-jsx": "^4.0.0", "less": "^4.2.0", "sass": "^1.77.5", "typescript": "^5.0.0", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.27.0", "vite": "^5.2.0", "vite-plugin-lazy-import": "^1.0.7", "vite-svg-loader": "^5.1.0", "vue-tsc": "^2.0.29"}}