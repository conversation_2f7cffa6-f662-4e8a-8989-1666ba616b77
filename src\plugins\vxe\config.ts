import { VxeUI } from 'vxe-pc-ui'
import { postPubAdminUploadSingle } from '@/api/methods/user'

// 全局参数
VxeUI.setConfig({
  version: 0,
  zIndex: 999,

  permissionMethod ({ code }) {
    //临时注释按钮得数据权限

    // const userStore = useUserStore()
    // if (code) {
    //   const visible = userStore.routePermissionCodeList.includes(code as string)
    //   return {
    //     // 是否可视
    //     visible,
    //     // 是否禁用
    //     disabled: false
    //   }
    // }
    return {
      visible: true,
      disabled: false
    }
  },

  table: {
    border: true,
    showOverflow: true,
    autoResize: true,
    columnConfig: {
      resizable: true
    },
    editConfig: {
      trigger: 'click'
    },
    sortConfig: {
      trigger: 'cell'
    },
    scrollY: {
      enabled: true,
      gt: 20
    }
  },
  grid: {
    toolbarConfig: {
      custom: true
    },
    proxyConfig: {
      showResponseMsg: false,
      showActiveMsg: true,
      response: {
        total: 'page.total',
        result: 'data',
        list: 'data'
      },
      ajax: {
        deleteSuccess () {
          VxeUI.modal.message({
            content: '删除成功',
            status: 'success'
          })
        },
        saveSuccess ({ response }) {
          const { data } = response
          VxeUI.modal.message({
            content: `新增 ${data.insertCount} 条，删除 ${data.deleteCount} 条，修改 ${data.updateCount} 条`,
            status: 'success'
          })
        }
      }
    }
  },
  upload: {
    uploadMethod ({ file, updateProgress }) {
      const formData = new FormData()
      formData.append('file', file)
      return postPubAdminUploadSingle(formData, {
        onUploadProgress (progressEvent) {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 0))
          updateProgress(percentCompleted)
        }
      }).then((res) => {
        return {
          ...res.data,
          url: `${import.meta.env.VITE_REQUEST_URL}/myResource/upload/${res.data.id}`
        }
      })
    }
  },
  pager: {
    layouts: ['Home', 'PrevJump', 'PrevPage', 'Jump', 'PageCount', 'NextPage', 'NextJump', 'End', 'Sizes', 'Total']
  }
})

// 这是 VxeUI 的全局配置文件，主要设置了以下几个重要部分：

// 基础配置：
// version: 版本号设为 0
// zIndex: 组件层级为 999
// permissionMethod: 权限控制方法，通过用户权限列表判断是否显示某些功能
// 表格配置（table）：
// 显示边框
// 内容溢出时显示省略号
// 自动调整大小
// 列可调整宽度
// 点击触发编辑
// 单元格触发排序
// 启用垂直滚动（超过20条数据时）
// 网格配置（grid）：
// 启用自定义工具栏
// 代理配置：
// 响应数据结构映射
// 成功操作的提示信息（删除、保存等）
// 上传配置（upload）：
// 自定义上传方法
// 支持进度条显示
// 上传成功后返回完整的文件URL
// 分页配置（pager）：
// 定义了分页器的布局组件：
// 首页、上一页跳转、上一页、页码跳转
// 总页数、下一页、下一页跳转、末页
// 页面大小选择器、总条数