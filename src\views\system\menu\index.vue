<!--
* Description: 菜单管理
* Created by 郑勇
* Date: 2024/12/26 10:16
* Update: 2024/12/26 10:16
-->
<template>
    <BmGridLayout :layout="layout" :col-num="12" :is-draggable="false" :is-resizable="true" :vertical-compact="false"
        :margin="[0, 0]" :use-css-transforms="true" :auto-size="true" fullscreen>
        <BmGridItem v-bind="layout[0]" @resize="handleResize">
            <bm-grid :grid-options="gridOptions"></bm-grid>
        </BmGridItem>
        <BmGridItem v-bind="layout[1]" @resize="handleResize">
            <bm-grid :grid-options="currentGridOptions"></bm-grid>
        </BmGridItem>
        <BmGridItem v-bind="layout[2]" @resize="handleResize" v-if="currentType!='1'">
            <bm-grid :grid-options="gridOptionsTabBtn"></bm-grid>
        </BmGridItem>

    </BmGridLayout>
    <FormDialog v-bind="formDialogProps" />
    <FormDialog v-bind="addMenuBtnformDialogProps" />
    <FormDialog v-bind="addMenuTabformDialogProps" />
    <FormDialog v-bind="addMenuTabBtnformDialogProps" />
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import BmGridLayout from '@/components/gridlayout/BmGridLayout.vue'
import { getList, saveOrUpdateEntity, saveOrUpdate, getMenuBtnList ,getMenuTabBtnList,getMenuTabList,saveOrUpdateMenuTabList,saveOrUpdateMenuTabBtnList} from '@/api/methods/menu'
import BmGridItem from '@/components/gridlayout/BmGridItem.vue'
import BmGrid from '@/components/BmGrid.vue'
import { ExtendedGridProps } from '@/types/grid'
import { Select, VxeUI } from 'vxe-pc-ui'
import FormDialog, { type Props } from '@/components/FormDialog.vue'
import { ExtendedFormProps } from '@/types/form'
import { useUserStore } from '@/stores/user'
import { getCurrentDateTime } from '@/utils/time.js'
import { truncate } from 'fs'
//当前选中的菜单
let currentSelectMenu
//当前选中的菜单按钮
let currentSelectMenuBtn 
//当前选中的tab页
let currentSelectMenuTab 
//当前选中的tab按钮
let currentSelectMenuTabBtn 


//新增菜单对话框模式
let menuformDialogMode='add'
//新增菜单按钮对话框模式
let menuBtnformDialogMode='add'
//新增tab页对话框模式
let menuTabformDialogMode='add'
//新增ta按钮对话框模式
let menuTabBtnformDialogMode='add'


// 高度 最大 1 , w 最大 12
const layout = reactive([
    // 菜单区域
    { x: 0, y: 0, w: 4, h: 1, i: '0' },
    // 查询区域
    { x: 4, y: 0, w: 4, h: 1, i: '1' },
    // 表格区域
    { x: 8, y: 0, w: 4, h: 1, i: '2' },
])

const handleResize = (size) => {
    // 第一个布局
    if (size.i === '0') {
        layout[1].x = size.w
        layout[1].w = 12 - size.w - layout[2].w
    }

    // 第二个布局
    if (size.i === '1') {
        layout[2].x = size.x + size.w
        layout[2].w = 12 - layout[2].x
    }

    // 第三个布局
    if (size.i === '2') {
        layout[2].x = 12 - size.w
        layout[1].w = layout[1].w + (12 - size.x - size.w)
    }
}

const tabList = reactive([
    { title: '菜单列表', name: '1' },
    { title: '菜单列表', name: '2' },
])
/* -------------------left start-------------------------- */
//左半部分布局
const gridOptions = reactive<ExtendedGridProps>({
    headerAlign: 'center',//表头对齐方式
    border: true,//是否显示边框
    scrollY: { enabled: false },//是否开启滚动
    treeConfig: {
        rowField: 'menuId',//行唯一标识
        childrenField: 'children'//子节点字段名
    },
    formConfig: {
        items: [
            { field: 'nickname', title: '菜单列表', span: 15, itemRender: { name: 'VxeInput', props: { width: '30%' } } },
            {
                span: 9,
                itemRender: {
                    name: 'VxeButtonGroup',
                    props: {
                        size: 'small'
                    },
                    options: [
                        { content: '查询', status: 'primary', id: 'query' },
                        { content: '新增菜单', status: 'success', id: 'add' }
                    ],
                    events: {
                        click(cellParams, params) {
                            // 表单和表格 的对象
                            console.log(cellParams)

                            // 查询
                            if (params.id === 'query') {
                                console.log(cellParams)
                            }
                            // 新增菜单
                            if (params.id === 'add') {
                                console.log(cellParams)
                            }
                        }
                    }
                }
            }
        ]
    },
    columns: [
        { type: 'seq', width: "14%", fixed: 'left', align: 'center' },
        {
            field: 'action',//字段名
            title: '操作',//标题
            width: '20%',//宽度
            cellRenders: [//单元格渲染
                {
                    name: 'VxeIcon',
                    props: {
                        size: 'medium',//组件属性
                        name: 'add',//组件属性
                        width: '33%',//组件属性
                        status: 'primary'//组件属性
                    },
                    events: {
                        click(cellParams, params) {
                            currentSelectMenu = cellParams
                            formDialogProps.visible = true
                            menuformDialogMode = 'add'
                            formDialogProps.title = '新增菜单'
                            console.log(cellParams)

                        }
                    }
                },
                {
                    name: 'VxeIcon',
                    props: {
                        size: 'medium',
                        name: 'edit',
                        width: '33%',
                        status: 'success'
                    },
                    events: {
                        click(cellParams, params) {
                            currentSelectMenu = cellParams
                            formDialogProps.visible = true
                            menuformDialogMode = 'edit'
                            formDialogProps.title = '编辑菜单'
                        }
                    }
                },
                {
                    name: 'VxeIcon',
                    props: {
                        size: 'medium',
                        name: 'delete',
                        width: '33%',
                        status: 'warning'
                    },
                    events: {
                        async click(cellParams, params) {
                            currentSelectMenu = cellParams
                            currentSelectMenu.isFlag = '10001002'
                            let del = { menuId: currentSelectMenu.menuId, isFlag: currentSelectMenu.isFlag }
                            await saveOrUpdateEntity(del)
                            currentSelectMenu = null
                            window.location.reload();
                        }
                    }
                }
            ]
        },
        {
            field: 'sname', title: '菜单名称', width: '30%', treeNode: true,//字段名,标题,宽度,是否树节点
            cellRender: {
                name: 'VxeText',//组件名称
                events: {
                    async click(cellParams, params) {
                        currentSelectMenu = cellParams.row
                        //根据deptId查询菜单按钮
                        const menuIdObject = {
                            menuId: currentSelectMenu.menuId
                        };

                        let res = await getMenuBtnList(menuIdObject)
                        gridOptions1.data = res.data.list

                        let res2 =await getMenuTabList(menuIdObject)
                        gridOptions2.data = res2.data.list

                        //置空最右边tab按钮的数据    
                        gridOptionsTabBtn.data =[]
                    }
                }
            }
        },
        {
            field: 'menuType', title: '菜单类型', width: '18%',dict: '10002',
            // 单组件过滤 示例
            // cellRender: {
            //     name: 'VxeInput',
            //     props: {
            //         width: '33%',
            //     },
            //     // 这里过滤为 单cellRender 下面进行,目前仅支持初次加载.后面动态变化 未来实现??
            //     visibleEvent: (row) => {
            //         return row.sname == '业务配置' || row.sname == '代码示例';
            //     }
            // },
            // cellRenders: [
            //     {
            //         name: 'VxeInput',
            //         props: {
            //             width: '33%',
            //         },
            //         // 这里过滤为 单cellRender 下面进行,目前仅支持初次加载.后面动态变化 未来实现??
            //         visibleEvent: (row) => {
            //             return row.sname == '业务配置' || row.sname == '代码示例';
            //         }
            //     },
            //     {
            //         name: 'VxeButton',
            //         props: {
            //             width: '40%',
            //             content: '按钮'
            //         },
            //         // 这里过滤为 单cellRender 下面进行,目前仅支持初次加载.后面动态变化 未来实现??
            //         visibleEvent: (row) => {
            //             return row.sname == '代码示例';
            //         }
            //     },
            // ]
        },
        { field: 'classify', title: '菜单分类', width: '18%',dict: '10005' },
    ],
    height: "auto",
    proxyConfig: {
        sort: true,//是否开启排序
        form: true,//是否开启表单
        response: {
            result: 'data',//返回数据
        },
        ajax: {
            async query({ page, form, sorts }) {
                const params = {
                    ...page,
                    ...form
                }
                let res = await getList(params)
                return res.data.list
            }
        }
    }
})
//新增左侧菜单表单对话框
const formDialogProps = reactive<Props>({
    visible: false,
    title: '新增菜单',
    mode: 'edit',
    width: '40%',
    height: '50%',
    formProps: {
        titleWidth: 100,//标题宽度
        titleAlign: 'right',//标题对齐方式
        titleColon: true,//是否显示冒号
        items: [//表单项配置数组
            { field:'pmenu',title: '所属菜单', span: 24, itemRender: { name: 'VxeInput', /* props: { placeholder: "111", } */ } },//字段名,标题,跨度,渲染配置

            { field: 'sname', title: '菜单名称', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
            {
                field: 'menuType',
                title: '菜单类型',
                span: 24,
                itemRender: {
                    name: 'VxeSelect',//组件名称
                    options: [//选项配置数组
                        { label: '目录', value: '10002001' },
                        { label: '菜单', value: '10002002' },
                        { label: '工具', value: '10002003' }
                    ]
                }
            },
            {
                field: 'classify',
                title: '菜单分类',
                span: 24,
                itemRender: {
                    name: 'VxeSelect',//组件名称
                    options: [//选项配置数组
                        { label: '系统菜单', value: '10005001' },
                        { label: '项目菜单', value: '10005002' }
                    ]
                }
            },
            { field: 'path', title: '路由路径', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
            { field: 'componentPath', title: '组件地址', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
            { field: 'componentName', title: '组件名称', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
            { field: 'url', title: '菜单URL', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
            { field: 'iconGray', title: '菜单图标', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
            { field: 'shading', title: '底纹', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
            { field: 'level', title: '菜单层级', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
            {
                field: 'status',
                title: '菜单状态',
                span: 24,
                itemRender: {
                    name: 'VxeSelect',//组件名称
                    options: [//选项配置数组
                        { label: '静态', value: '10006002' },
                        { label: '动态', value: '10006001' }
                    ]
                }
            },
            { field: 'sort', title: '排序', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
            { field: 'remark', title: '备注信息', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置

        ],
        data: {

        },//表单数据
        formType: 'add',//表单类型
        applyData: async (data) => {
            // 提交数据
            let res = await addDeptUser(data);
            console.log(res)
        },
        getData: () => {
            if(menuformDialogMode=='add'){
               return {pmenu:currentSelectMenu.sname} 
            }else{
                currentSelectMenu.pmenu=currentSelectMenu.sname
                return currentSelectMenu
            }
            // currentSelectMenu.pmenu=currentSelectMenu.sname
            // console.log("-----+++-----"+JSON.stringify(currentSelectMenu))
            // return currentSelectMenu
            // 获取数据
            //return getDeptUserList({ deptId: currentDeptId.value });
        },
        rules: {
            name: [
                { required: true, validator: 'ValidName' }
            ],
            nickname: [
                { required: true, validator: 'ValidNikeName' }
            ]
        },
    },
    buttons: {
        position: 'right',
        itemRenders: [
            {
                name: '取消',
                props: {
                    content: '取消',
                    type: 'default',
                    width: '100px'
                },
                events: {
                    click: async (formData, formRef) => {
                        console.log('取消按钮点击', formData)
                        // 可以访问表单数据和表单实例
                        formDialogProps.visible = false

                    }
                }
            },
            {
                name: '确定',
                props: {
                    content: '确定',
                    type: 'primary',
                    width: '100px'
                },
                events: {
                    click: async (formData, formRef) => {
                        console.log('确定按钮点击--', formData)
                        // 可以进行表单验证等操作
                        formDialogProps.visible = false
                        let pid
                        let isFlag = '10001001'
                        if (menuformDialogMode == 'add') {
                            pid = currentSelectMenu.menuId
                            const createDate = getCurrentDateTime()
                            formData.createDate = createDate
                        } else {
                            pid = currentSelectMenu.pid
                            const updateDate = getCurrentDateTime()
                            formData.updateDate = updateDate
                        }
                        let createUserid = useUserStore().userInfo.userId
                        formData = {
                            ...formData, pid, createUserid, isFlag
                        }
                        const { _X_ROW_KEY,pmenu, children, ...filteredFormData } = formData;


                        await saveOrUpdateEntity(filteredFormData)
                        if (menuformDialogMode == 'add') {
                            window.location.reload();
                        }

                    }
                }
            },
            {
                name: '重置',
                props: {
                    content: '重置',
                    type: 'default',
                    width: '100px'
                },
                events: {
                    click: (formData, formRef) => {
                        // console.log('重置按钮点击', formData)
                        formRef.resetForm()
                    }
                }
            }
        ]
    },
    onCancel: () => {
        formDialogProps.visible = false
    },
    onSubmit: (data) => {
        // 处理提交逻辑
    }
})
/* -------------------end-------------------------- */



/* -------------------tools start-------------------------- */
//中间上半部分tools这里因为需要动态显示和隐藏,所以需要使用let声明
let formOptions = reactive<ExtendedFormProps>({
    // 表单项配置数组
    items: [
        {
            field: 'cardType',
            // title: '证件类型',
            span: 14,
            itemRender: {
                name: 'VxeRadioGroup',
                options: [
                    { label: '菜单按钮列表', value: '1' },
                    { label: '卡片/tab列表', value: '2' }
                ],
                events: {
                    change(value, formData, formRef) {
                        // console.log(value, formData)
                        // console.log(formRef)
                        // formRef.submitForm()
                        currentType.value = value;
                        formRef.setFieldVisible('workCode', value == '1');
                        formRef.setFieldVisible('tab', value != '1');
                    }
                },
            },
            // visible: false
        },
        //这里为表单得字段按钮.
        {
            field: 'workCode',
            span: 6,
            itemRender: {
                name: 'VxeButton',
                props: {
                    type: 'primary',
                    content: '新增菜单按钮'
                },
                events: {
                    // 参数1:表单数据,参数2:表单实例,参数3:组件实例
                    click(value, formData, formRef) {
                        console.log("formOptions"+value)
                        if (currentSelectMenu) {
                            addMenuBtnformDialogProps.visible = true
                            menuBtnformDialogMode= 'add'
                        }


                    }
                }
            },
            // 这个函数仅在第一次加载的时候 生效,后面无论怎么修改都不生效
            visibleEvent: (data) => {
                // 获取表单内的数据
                console.log("visibleEvent", data)
                return currentType.value == '1';
            }
        },
        //这里为表单得字段按钮.
        {
            field: 'tab',
            span: 5,
            itemRender: {
                name: 'VxeButton',
                props: {
                    type: 'primary',
                    content: '新增tab页'
                },
                events: {
                    
                    // 参数1:表单数据,参数2:表单实例,参数3:组件实例
                    click(value, formData, formRef) {
                        if(currentSelectMenu){
                        addMenuTabformDialogProps.visible=true
                        addMenuTabformDialogProps.title='新增tab页'
                        }
                    }
                }
            },
            // 这个函数仅在第一次加载的时候 生效,后面无论怎么修改都不生效
            visibleEvent: (data) => {
                // 获取表单内的数据
                console.log("visibleEvent", data)
                return currentType.value != '1';
            }
        },
    ],
    // 表单类型 这里决定了表格上面得工具栏是否能自动获取值
    formType: 'edit',
    // 获取数据的配置
    getData: () => {
        //这里支持 返回接口数据
        return {
            cardType: '1',
            workCode: '2',
            username: '张三',
        }
    },
    // 提交数据的函数
    applyData: async (data) => {
        // 这里可以实现提交数据的逻辑
        console.log('提交的数据:', data)
    },
    // 其他vxe-form的配置项
    titleWidth: 'auto',
    titleAlign: 'right',
    titleColon: true,
    titleOverflow: true,
    // 表单布局
    layout: 'inline',
    // 表单项间距
    itemGap: 10,
    // 是否显示冒号
    colon: true,
    // 是否显示必填星号
    showRequiredMark: true,
    // 校验规则
    rules: {
        sname: [
            { required: true, message: '请输入用户名' }
        ],
        phone: [
            { required: true, message: '请输入手机号' }
        ]
    },
    // 是否只读
    readonly: false,
})
//右侧上半部分tools
let formOptions1 =reactive<ExtendedFormProps>({
    align:'center',
    // 表单项配置数组
    items: [
        {
            field: 'tab',
            span: 5,
            itemRender: {
                name: 'VxeButton',
                props: {
                    type: 'primary',
                    content: '新增tab按钮'
                },
                events: {
                    // 参数1:表单数据,参数2:表单实例,参数3:组件实例
                    click(value, formData, formRef) {
                        if(currentSelectMenuTab){
                            addMenuTabBtnformDialogProps.visible=true
                            menuTabBtnformDialogMode='add'
                            addMenuTabBtnformDialogProps.title='新增tab按钮'
                        }

                    }
                }
            },
        },
    ],
    // 表单类型 这里决定了表格上面得工具栏是否能自动获取值
    formType: 'edit',
    // 获取数据的配置
    getData: () => {
        // 这里支持 返回接口数据
        return {
            cardType: '1',
            workCode: '2',
            username: '张三',
        }
    },
    // 提交数据的函数
    applyData: async (data) => {
        // 这里可以实现提交数据的逻辑
        console.log('提交的数据:', data)
    },
    // 其他vxe-form的配置项
    titleWidth: 'auto',
    titleAlign: 'right',
    titleColon: true,
    titleOverflow: true,
    // 表单布局
    layout: 'inline',
    // 表单项间距
    itemGap: 10,
    // 是否显示冒号
    colon: true,
    // 是否显示必填星号
    showRequiredMark: true,
    // 校验规则
    rules: {
        sname: [
            { required: true, message: '请输入用户名' }
        ],
        phone: [
            { required: true, message: '请输入手机号' }
        ]
    },
    // 是否只读
    readonly: false,
})
/* -------------------end-------------------------- */





/* -------------------middle start-------------------------- */
//中间下半部分菜单按钮列表
const gridOptions1 = reactive<ExtendedGridProps>({
    headerAlign: 'center',//表头对齐方式
    border: true,//是否显示边框
    scrollY: { enabled: false },//是否开启滚动
    treeConfig: {
        rowField: 'menuId',//行唯一标识
        childrenField: 'children'//子节点字段名
    },
    tools: formOptions,
    columns: [
        { type: 'seq', width: "14%", fixed: 'left', align: 'center' },
        {
            field: 'action',//字段名
            title: '操作',//标题
            width: '20%',//宽度
            align: 'center',
            cellRenders: [//单元格渲染
                {
                    name: 'VxeIcon',
                    props: {
                        size: 'medium',
                        name: 'edit',
                        width: '50%',
                        status: 'success'
                    },
                    events: {
                        click(cellParams, params) {
                            console.log(cellParams)
                            currentSelectMenuBtn=cellParams
                            menuBtnformDialogMode='edit'
                            addMenuBtnformDialogProps.visible=true
                            addMenuBtnformDialogProps.title="修改按钮"
                        }
                    }
                },
                {
                    name: 'VxeIcon',
                    props: {
                        size: 'medium',
                        name: 'delete',
                        width: '50%',
                        status: 'warning'
                    },
                    events: {
                       async click(cellParams, params) {
                            cellParams.isFlag='10001002'
                           await saveOrUpdate(cellParams)
                           window.location.reload();
                        }
                    }
                }
            ]
        },
        {
            field: 'sname', title: '按钮名称', width: '30%', treeNode: true,//字段名,标题,宽度,是否树节点
            cellRender: {
                name: 'VxeText',//组件名称
                events: {
                    async click(cellParams, params) {
                        console.log(cellParams.row)
                        //根据deptId查询人员信息
                        let res = await getDeptUserList({ deptId: cellParams.row.deptId, queryChild: "false" })
                        console.log(res.data.records)
                        // gridOptions.data = res.data.records
                    }
                }
            }
        },
        { field: 'alias', title: '别名', width: '18%' },
        {
            field: 'remark', title: '备注信息', width: '18%',
        },
    ],
    height: "auto",
    // proxyConfig: {
    //     sort: true,//是否开启排序
    //     form: true,//是否开启表单
    //     response: {
    //         result: 'data',//返回数据
    //     },
    //     ajax: {
    //         async query({ page, form, sorts }) {
    //             const params = {
    //                 ...page,
    //                 ...form
    //             }
    //             let res = await getList(params)
    //             return res.data.list
    //         }
    //     }
    // }
})
//新增菜单按钮表单对话框
const addMenuBtnformDialogProps = reactive<Props>({
    visible: false,
    title: '新增按钮',
    mode: 'edit',
    width: '40%',
    height: '50%',
    formProps: {
        titleWidth: 100,//标题宽度
        titleAlign: 'right',//标题对齐方式
        titleColon: true,//是否显示冒号
        items: [//表单项配置数组
            { field: 'pmenu', title: '所属菜单', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: "currentSelectMenu.sname", } } },//字段名,标题,跨度,渲染配置
            { field: 'sname', title: '按钮名称', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
            { field: 'alias', title: '别名', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
            { field: 'sort', title: '排序', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
            { field: 'remark', title: '备注信息', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
        ],
        data: {

        },//表单数据
        formType: 'add',//表单类型
        applyData: async (data) => {
            // 提交数据
            let res = await addDeptUser(data);
            console.log(res)
        },
        getData: () => {
            if(menuBtnformDialogMode=='add'){
               return {pmenu:currentSelectMenu.sname} 
            }else{
                currentSelectMenuBtn.pmenu=currentSelectMenu.sname
                return currentSelectMenuBtn
            }


            // 获取数据
            //return getDeptUserList({ deptId: currentDeptId.value });
        },
        rules: {
            name: [
                { required: true, validator: 'ValidName' }
            ],
            nickname: [
                { required: true, validator: 'ValidNikeName' }
            ]
        },
    },
    buttons: {
        position: 'right',
        itemRenders: [
            {
                name: '取消',
                props: {
                    content: '取消',
                    type: 'default',
                    width: '100px'
                },
                events: {
                    click: async (formData, formRef) => {
                        console.log('取消按钮点击', formData)
                        // 可以访问表单数据和表单实例
                        addMenuBtnformDialogProps.visible = false

                    }
                }
            },
            {
                name: '确定',
                props: {
                    content: '确定',
                    type: 'primary',
                    width: '100px'
                },
                events: {
                    click: async (formData, formRef) => {
                        console.log('确定按钮点击--', formData)
                        // 可以进行表单验证等操作
                        addMenuBtnformDialogProps.visible = false
                        let menuId = currentSelectMenu.menuId
                        let isFlag = '10001001'
                        
                        if (menuBtnformDialogMode == 'add') {
                            const createDate = getCurrentDateTime()
                            formData.createDate = createDate
                        } else {
                            const updateDate = getCurrentDateTime()
                            formData.updateDate = updateDate
                        }
                        let createUserid = useUserStore().userInfo.userId
                        formData = {
                            ...formData, menuId, createUserid, isFlag
                        }
                        const { _X_ROW_KEY, children,pmenu, ...filteredFormData } = formData;

                        await saveOrUpdate(filteredFormData)
                        if (menuBtnformDialogMode == 'add') {
                            window.location.reload();
                        }
                    }
                }
            },
            {
                name: '重置',
                props: {
                    content: '重置',
                    type: 'default',
                    width: '100px'
                },
                events: {
                    click: (formData, formRef) => {
                        // console.log('重置按钮点击', formData)
                        formRef.resetForm()
                    }
                }
            }
        ]
    },
    onCancel: () => {
        addMenuBtnformDialogProps.visible = false
    },
    onSubmit: (data) => {
        // 处理提交逻辑
    }
})
//中间下半部分tab页卡片列表
const gridOptions2 = reactive<ExtendedGridProps>({
    headerAlign: 'center',//表头对齐方式
    border: true,//是否显示边框
    scrollY: { enabled: false },//是否开启滚动
    treeConfig: {
        rowField: 'menuId',//行唯一标识
        childrenField: 'children'//子节点字段名
    },
    tools: formOptions1,
    columns: [
        { type: 'seq', width: "14%", fixed: 'left', align: 'center' },
        {
            field: 'action',//字段名
            title: '操作',//标题
            width: '20%',//宽度
            cellRenders: [//单元格渲染
                // {
                //     name: 'VxeIcon',
                //     props: {
                //         size: 'medium',//组件属性
                //         name: 'setting',//组件属性
                //         width: '33%',//组件属性
                //         status: 'primary'//组件属性
                //     },
                //     events: {
                //         click(cellParams, params) {
                //             VxeUI.modal.message({
                //                 content: '点击了',
                //                 status: 'success'
                //             })
                //         }
                //     }
                // },
                {
                    name: 'VxeIcon',
                    props: {
                        size: 'medium',
                        name: 'edit',
                        width: '50%',
                        status: 'success'
                    },
                    events: {
                        click(cellParams, params) {
                            currentSelectMenuTab=cellParams
                            console.log(cellParams)
                            addMenuTabformDialogProps.visible=true
                            menuTabformDialogMode='edit'
                        }
                    }
                },
                {
                    name: 'VxeIcon',
                    props: {
                        size: 'medium',
                        name: 'delete',
                        width: '50%',
                        status: 'warning'
                    },
                    events: {
                      async  click(cellParams, params) {
                            currentSelectMenuTab = cellParams
                            currentSelectMenuTab.isFlag = '10001002'
                            let del = { menuTabId: currentSelectMenuTab.menuTabId, isFlag: currentSelectMenuTab.isFlag }
                            await saveOrUpdateMenuTabList(del)
                            currentSelectMenuTab = null
                            window.location.reload();

                        }
                    }
                }
            ]
        },
        {
            field: 'sname', title: 'tab名称', width: '33%', treeNode: true,//字段名,标题,宽度,是否树节点
            cellRender: {
                name: 'VxeText',//组件名称
                events: {
                    async click(cellParams, params) {
                        console.log(cellParams.row)

                        currentSelectMenuTab=cellParams.row
                        //根据deptId查询人员信息
                        let res = await getMenuTabBtnList({ menuId:currentSelectMenu.menuId, tabId: currentSelectMenuTab.menuTabId })
                        console.log(res.data.list)
                        gridOptionsTabBtn.data = res.data.list
                        

                    }
                }
            }
        },
        { field: 'alias', title: '别名', width: '33%' },
        
    ],
    height: "auto",
    /* proxyConfig: {
        sort: true,//是否开启排序
        form: true,//是否开启表单
        response: {
            result: 'data',//返回数据
        },
        ajax: {
            async query({ page, form, sorts }) {
                const params = {
                    ...page,
                    ...form
                }
                let res = await getList(params)
                return res.data.list
            }
        }
    } */
})
//新增菜单tab页对话框
const addMenuTabformDialogProps = reactive<Props>({
    visible: false,
    title: '新增tab页',
    mode: 'edit',
    width: '40%',
    height: '50%',
    formProps: {
        titleWidth: 100,//标题宽度
        titleAlign: 'right',//标题对齐方式
        titleColon: true,//是否显示冒号
        items: [//表单项配置数组
            { field: 'pmenu',title: '所属菜单', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: "currentSelectMenu.sname", } } },//字段名,标题,跨度,渲染配置
            { field: 'sname', title: 'tab名称', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
            { field: 'alias', title: '别名', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
            { field: 'sort', title: '排序', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
            { field: 'remark', title: '备注信息', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
        ],
        data: {

        },//表单数据
        formType: 'add',//表单类型
        applyData: async (data) => {

        },
        getData: () => {
            if(menuTabformDialogMode=='add'){
               return {pmenu:currentSelectMenu.sname} 
            }else{
                currentSelectMenuTab.pmenu=currentSelectMenu.sname
                return currentSelectMenuTab
            }





            // 获取数据
            //return getDeptUserList({ deptId: currentDeptId.value });
        },
        rules: {
            name: [
                { required: true, validator: 'ValidName' }
            ],
            nickname: [
                { required: true, validator: 'ValidNikeName' }
            ]
        },
    },
    buttons: {
        position: 'right',
        itemRenders: [
            {
                name: '取消',
                props: {
                    content: '取消',
                    type: 'default',
                    width: '100px'
                },
                events: {
                    click: async (formData, formRef) => {
                        console.log('取消按钮点击', formData)
                        // 可以访问表单数据和表单实例
                        addMenuTabformDialogProps.visible = false

                    }
                }
            },
            {
                name: '确定',
                props: {
                    content: '确定',
                    type: 'primary',
                    width: '100px'
                },
                events: {
                    click: async (formData, formRef) => {
                        console.log('确定按钮点击--', formData)
                        // 可以进行表单验证等操作
                        addMenuTabformDialogProps.visible = false
                        let menuId = currentSelectMenu.menuId
                        let isFlag = '10001001'
                        if (menuTabformDialogMode == 'add') {
                            
                            const createDate = getCurrentDateTime()
                            formData.createDate = createDate
                        } else {
                            const updateDate = getCurrentDateTime()
                            formData.updateDate = updateDate
                        }
                        let createUserid = useUserStore().userInfo.userId
                        formData = {
                            ...formData, menuId, createUserid, isFlag
                        }
                        const { _X_ROW_KEY, children,pmenu, ...filteredFormData } = formData;

                        await saveOrUpdateMenuTabList(filteredFormData)
                        if (menuTabformDialogMode == 'add') {
                            window.location.reload();
                        }
                        
                        // if (addMenuTabformDialogProps.mode == 'add') {
                        //     window.location.reload();
                        // }
                    }
                }
            },
            {
                name: '重置',
                props: {
                    content: '重置',
                    type: 'default',
                    width: '100px'
                },
                events: {
                    click: (formData, formRef) => {
                        // console.log('重置按钮点击', formData)
                        formRef.resetForm()
                    }
                }
            }
        ]
    },
    onCancel: () => {
        addMenuTabformDialogProps.visible = false
    },
    onSubmit: (data) => {
        // 处理提交逻辑
    }
})
/* -------------------end-------------------------- */




// 添加一个响应式变量来控制当前显示的 gridOptions
let currentType = ref<string>('1')
const currentGridOptions = computed(() => {
    // console.log(currentType.value)
    return currentType.value === '1' ? gridOptions1 : gridOptions2
})





/* -------------------right start-------------------------- */
//最右tab btn
const gridOptionsTabBtn = reactive<ExtendedGridProps>({
    headerAlign: 'center',//表头对齐方式
    border: true,//是否显示边框
    scrollY: { enabled: false },//是否开启滚动
    treeConfig: {
        rowField: 'menuId',//行唯一标识
        childrenField: 'children'//子节点字段名
    },
    tools: formOptions1,
    columns: [
        { type: 'seq', width: "14%", fixed: 'left', align: 'center' },
        {
            field: 'action',//字段名
            title: '操作',//标题
            width: '20%',//宽度
            cellRenders: [//单元格渲染
                // {
                //     name: 'VxeIcon',
                //     props: {
                //         size: 'medium',//组件属性
                //         name: 'setting',//组件属性
                //         width: '33%',//组件属性
                //         status: 'primary'//组件属性
                //     },
                //     events: {
                //         click(cellParams, params) {
                //             VxeUI.modal.message({
                //                 content: '点击了',
                //                 status: 'success'
                //             })
                //         }
                //     }
                // },
                {
                    name: 'VxeIcon',
                    props: {
                        size: 'medium',
                        name: 'edit',
                        width: '50%',
                        status: 'success'
                    },
                    events: {
                        click(cellParams, params) {
                            currentSelectMenuTabBtn=cellParams
                            console.log(cellParams)
                            addMenuTabBtnformDialogProps.visible=true
                            menuTabBtnformDialogMode='edit'
                        }
                    }
                },
                {
                    name: 'VxeIcon',
                    props: {
                        size: 'medium',
                        name: 'delete',
                        width: '50%',
                        status: 'warning'
                    },
                    events: {
                      async  click(cellParams, params) {
                            currentSelectMenuTabBtn = cellParams
                            console.log(currentSelectMenuTabBtn)
                            currentSelectMenuTabBtn.isFlag = '10001002'
                            let del = { menuTabBtnId: currentSelectMenuTabBtn.menuTabBtnId, isFlag: currentSelectMenuTabBtn.isFlag }
                            await saveOrUpdateMenuTabBtnList(del)
                            currentSelectMenuTabBtn = null
                            window.location.reload();

                        }
                    }
                }
            ]
        },
        {
            field: 'sname', title: '按钮名称', width: '33%', treeNode: true,//字段名,标题,宽度,是否树节点
            cellRender: {
                name: 'VxeText',//组件名称
                events: {
                    async click(cellParams, params) {
                        console.log(cellParams.row)
                        //根据deptId查询人员信息
                        // let res = await getDeptUserList({ deptId: cellParams.row.deptId, queryChild: "false" })
                        // console.log(res.data.records)
                        // gridOptions.data = res.data.records
                    }
                }
            }
        },
        { field: 'alias', title: '别名', width: '33%' },
        
    ],
    height: "auto",
    /* proxyConfig: {
        sort: true,//是否开启排序
        form: true,//是否开启表单
        response: {
            result: 'data',//返回数据
        },
        ajax: {
            async query({ page, form, sorts }) {
                const params = {
                    ...page,
                    ...form
                }
                let res = await getList(params)
                return res.data.list
            }
        }
    } */
})
//新增tabBtn对话框
const addMenuTabBtnformDialogProps = reactive<Props>({
    visible: false,
    title: '新增tab按钮',
    mode: 'edit',
    width: '40%',
    height: '50%',
    formProps: {
        titleWidth: 100,//标题宽度
        titleAlign: 'right',//标题对齐方式
        titleColon: true,//是否显示冒号
        items: [//表单项配置数组
            { field: 'pmenu',title: '所属菜单', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: "currentSelectMenu.sname", } } },//字段名,标题,跨度,渲染配置
            { field: 'ptab',title: '所属tab', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: "currentSelectMenu.sname", } } },//字段名,标题,跨度,渲染配置
            { field: 'sname', title: 'tab按钮', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
            { field: 'alias', title: '别名', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
            { field: 'sort', title: '排序', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
            { field: 'remark', title: '备注信息', span: 24, itemRender: { name: 'VxeInput', props: { placeholder: '请输入' } } },//字段名,标题,跨度,渲染配置
        ],
        data: {

        },//表单数据
        formType: 'add',//表单类型
        applyData: async (data) => {

        },
        getData: () => {
            if(menuTabBtnformDialogMode=='add'){
               return {pmenu:currentSelectMenu.sname,ptab:currentSelectMenuTab.sname} 
            }else{
                currentSelectMenuTabBtn.pmenu=currentSelectMenu.sname
                currentSelectMenuTabBtn.ptab=currentSelectMenuTab.sname
                return currentSelectMenuTabBtn
            }



            //return currentSelectMenuTab
            // 获取数据
            //return getDeptUserList({ deptId: currentDeptId.value });
        },
        rules: {
            name: [
                { required: true, validator: 'ValidName' }
            ],
            nickname: [
                { required: true, validator: 'ValidNikeName' }
            ]
        },
    },
    buttons: {
        position: 'right',
        itemRenders: [
            {
                name: '取消',
                props: {
                    content: '取消',
                    type: 'default',
                    width: '100px'
                },
                events: {
                    click: async (formData, formRef) => {
                        console.log('取消按钮点击', formData)
                        // 可以访问表单数据和表单实例
                        addMenuTabBtnformDialogProps.visible = false

                    }
                }
            },
            {
                name: '确定',
                props: {
                    content: '确定',
                    type: 'primary',
                    width: '100px'
                },
                events: {
                    click: async (formData, formRef) => {
                        console.log('确定按钮点击--', formData)
                        // 可以进行表单验证等操作
                        addMenuTabBtnformDialogProps.visible = false
                        let menuId = currentSelectMenu.menuId
                        let tabId =currentSelectMenuTab.menuTabId
                        let isFlag = '10001001'
                        console.log("1111---'$'"+tabId)
                        if (menuTabBtnformDialogMode == 'add') {
                            const createDate = getCurrentDateTime()
                            formData.createDate = createDate
                        } else {
                            const updateDate = getCurrentDateTime()
                            formData.updateDate = updateDate
                        }
                        let createUserid = useUserStore().userInfo.userId
                        formData = {
                            ...formData, menuId, createUserid, isFlag,tabId
                        }
                        const { _X_ROW_KEY, children,pmenu, ...filteredFormData } = formData;

                        await saveOrUpdateMenuTabBtnList(filteredFormData)
                        if (menuTabBtnformDialogMode == 'add') {
                            window.location.reload();
                        }
                    }
                }
            },
            {
                name: '重置',
                props: {
                    content: '重置',
                    type: 'default',
                    width: '100px'
                },
                events: {
                    click: (formData, formRef) => {
                        // console.log('重置按钮点击', formData)
                        formRef.resetForm()
                    }
                }
            }
        ]
    },
    onCancel: () => {
        addMenuTabBtnformDialogProps.visible = false
    },
    onSubmit: (data) => {
        // 处理提交逻辑
    }
})
/* -------------------end-------------------------- */
</script>
