<template>
  <!-- <PageView> -->
  <grid-layout :layout="layout" :col-num="12" :row-height="30" :is-draggable="false" :is-resizable="true"
               :vertical-compact="false" :margin="[10, 10]" :use-css-transforms="true">
    <!-- 左侧菜单树 -->
    <grid-item :x="0" :y="0" :w="2" :h="26" :i="0">
      <bm-grid :grid-options="gridOptions1"></bm-grid>
    </grid-item>

    <!-- 右侧表格区域 -->
    <grid-item :x="2" :y="0" :w="10" :h="26" :i="1">
      <bm-grid ref="gridRef" :grid-options="gridOptions" v-on="gridEvents">
        <template #top>
          <vxe-tip status="error" icon="vxe-icon-warning-circle-fill"
                   permission-code="userManageActionInsert">新增用户的初始密码为：<vxe-text click-to-copy>123456</vxe-text></vxe-tip>
        </template>

        <template #editName="{ row }">
          <vxe-input v-model="row.name" :disabled="!!row.code"></vxe-input>
        </template>

        <template #defaultPictureUrl="{ row }">
          <VxeUpload singleMode urlMode v-model="row.pictureUrl" :show-button-text="false" :show-remove-button="false"
                     :imageStyle="{ width: 40, height: 40 }"
                     :readonly="userStore.userRoleLevel >= row.roleLevel || !VxeUI.permission.checkVisible('userManageActionInsert|userManageActionUpdate')"
                     mode="image" button-icon="vxe-icon-edit">
          </VxeUpload>
        </template>

        <template #action="{ row }">
          <vxe-button mode="text" status="error" icon="vxe-icon-delete"
                      permission-code="userManageActionDelete" @click="removeRow(row)">删除</vxe-button>
        </template>
      </bm-grid>
    </grid-item>
  </grid-layout>

<!--  <VxeButton @click="handleAdd">上传</VxeButton>-->
  <!-- 添加/编辑表单弹窗 -->
  <FormDialog v-model="showForm" :title="formType === 'add' ? '添加部门' : '编辑部门'" width="40%" height="50%"
              :loading="loading" :form-props="formProps" :mode="formType" @submit="handleFormSubmit"
              @cancel="() => showForm = false" />
  <!-- </PageView> -->
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, h } from 'vue'
import {VxeGridProps, VxeColumnPropTypes, VxeGridInstance} from 'vxe-table'
import { VxeUI, VxeFormItemPropTypes, VxeSelectProps, VxeFormProps, VxeUploadPropTypes, VxeButton } from 'vxe-pc-ui'
import { GridLayout, GridItem } from 'grid-layout-plus'
import FormDialog from '@/components/FormDialog.vue'
import axios from 'axios'
import BmGrid from '@/components/BmGrid.vue'
import { ExtendedGridProps } from '@/types/grid'
import { ExtendedFormItemProps, ExtendedFormProps } from '@/types/form'
import { add } from 'xe-utils'
import {useUserStore} from "@/stores/user";

// Define the layout configuration
const layout = ref([
  { x: 0, y: 0, w: 4, h: 24, i: '0' }, // Left side menu tree
  { x: 4, y: 0, w: 8, h: 24, i: '1' }  // Right side grid
])

const userStore = useUserStore()
const gridRef = ref<VxeGridInstance<UserVO>>()

const roleCodeEditRender = reactive<VxeColumnPropTypes.EditRender<UserVO, VxeSelectProps>>({
  name: 'VxeSelect',
  options: [],
  defaultValue: 'default',
  props: {
    disabled: true
  }
})

const roleCodesEditRender = reactive<VxeColumnPropTypes.EditRender<UserVO, VxeSelectProps>>({
  name: 'VxeSelect',
  props: {
    multiple: true
  },
  options: [],
  defaultValue: [
    'default'
  ],
  events: {
    change({ row }) {
      if (!row.roleCodes.includes(row.roleCode)) {
        row.roleCode = row.roleCodes[0]
      }
    }
  }
})

const roleCodeItemRender = reactive<VxeFormItemPropTypes.ItemRender<UserVO, VxeSelectProps>>({
  name: 'VxeSelect',
  props: {
    clearable: true
  },
  options: []
})

const roleCodesItemRender = reactive<VxeFormItemPropTypes.ItemRender<UserVO, VxeSelectProps>>({
  name: 'VxeSelect',
  props: {
    multiple: true,
    clearable: true
  },
  options: []
})

const gridOptions = reactive<ExtendedGridProps>({
  border: true,
  headerAlign: 'center',
  showOverflow: true,
  height: 'auto',
  scrollY: { enabled: false },
  id: 'UserManageList',
  keepSource: true,
  customConfig: {
    storage: true
  },
  rowConfig: {
    isHover: true,
    keyField: '_id'
  },
  sortConfig: {
    remote: true,
    multiple: true
  },
  editConfig: {
    mode: 'row',
    showStatus: true,
    enabled: VxeUI.permission.checkVisible('userManageActionInsert|userManageActionUpdate'),
    beforeEditMethod({ row, column }) {
      if (userStore.userRoleLevel >= row.roleLevel) {
        return false
      }
      if (row.code && column.field === 'name') {
        return false
      }
      return true
    }
  },
  checkboxConfig: {
    checkMethod({ row }) {
      return userStore.userRoleLevel < row.roleLevel
    }
  },
  toolbarConfig: {
    refresh: true,
    zoom: true,
    buttons: [
      // { name: '新增', code: 'insert_edit', status: 'primary', icon: 'vxe-icon-add', permissionCode: 'userManageActionInsert' },
      // { name: '标记/删除', code: 'mark_cancel', status: 'error', icon: 'vxe-icon-delete', permissionCode: 'userManageActionDelete' },
      // { name: '保存', code: 'save', status: 'success', icon: 'vxe-icon-save', permissionCode: 'userManageActionInsert|userManageActionDelete|userManageActionUpdate' }
    ]
  },
  editRules: {
    name: [
      { required: true, message: '请输入用户名' }
    ],
    roleCodes: [
      { required: true, type: 'array', message: '至少需要授权一个角色' }
    ]
  },
  formConfig: {
    titleWidth: 80,
    titleAlign: 'right',
    items: [
      // 隐藏用户名
      { field: 'name', title: '用户名', span: 6, itemRender: { name: 'VxeInput', props: { clearable: true } }, visible: true },
      // 其他字段显示
      { field: 'nickname', title: '昵称', span: 6, itemRender: { name: 'VxeInput', props: { clearable: true } } },
      { field: 'email', title: '邮箱', span: 6, itemRender: { name: 'VxeInput', props: { clearable: true } } },
      { field: 'roleCodes', title: '关联角色', span: 6, itemRender: roleCodesItemRender },
      { field: 'roleCode', title: '默认角色', span: 6, folding: true, itemRender: roleCodeItemRender },
      { field: 'startDate', title: '开始时间', span: 6, folding: true, itemRender: { name: 'VxeDatePicker', props: { clearable: true } } },
      { field: 'endDate', title: '结束时间', span: 6, folding: true, itemRender: { name: 'VxeDatePicker', props: { clearable: true } } },
      {
        field: 'testSelect', title: '测试下拉', span: 6, folding: true,
        itemRender: {
          name: 'VxeSelect', props: { clearable: true },
          options: [
            { label: '女', value: 'Women' },
            { label: '男', value: 'Men' }
          ],
          // selectApi: 'http://************:8083/sys/temp/list',
          // dict: 'test_select',
        }
      },
      { span: 24, align: 'right', collapseNode: true, itemRender: { name: 'ListSearchBtn' } }
    ]
  },
  pagerConfig: {},
  columns: [
    { type: 'checkbox', width: 60 },
    { type: 'seq', width: 70 },
    { field: 'code', title: '用户编码', width: 300, visible: false },
    { field: 'name', title: '用户名', minWidth: 160, sortable: true, editRender: { name: 'VxeInput' }, slots: { edit: 'editName' } },
    { field: 'roleCodes', title: '关联角色', minWidth: 300, editRender: roleCodesEditRender },
    { field: 'roleCode', title: '默认角色', width: 140, editRender: roleCodeEditRender },
    { field: 'nickname', title: '昵称', minWidth: 220, editRender: { name: 'VxeInput' } },
    { field: 'pictureUrl', title: '头像', width: 120, slots: { default: 'defaultPictureUrl' } },
    { field: 'email', title: '邮箱', minWidth: 220, editRender: { name: 'VxeInput' } },
    { field: 'createTime', title: '注册时间', width: 160, formatter: 'FormatDateTime', sortable: true },
    { field: 'updateTime', title: '最后更新时间', width: 160, formatter: 'FormatDateTime', sortable: true },
    { field: 'action', title: '操作', fixed: 'right', width: '8%', slots: { default: 'action' } }
  ],
  data: [
    {
      _id: '1',
      code: 'USER001',
      name: '张三',
      roleCodes: ['admin', 'user'],
      roleCode: 'admin',
      nickname: '小张',
      pictureUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user2',
      email: '<EMAIL>',
      createTime: '2024-01-15 10:00:00',
      updateTime: '2024-01-15 10:00:00',
      roleLevel: 1
    },
    {
      _id: '2',
      code: 'USER002',
      name: '李四',
      roleCodes: ['user'],
      roleCode: 'user',
      nickname: '小李',
      pictureUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user3',
      email: '<EMAIL>',
      createTime: '2024-01-15 11:00:00',
      updateTime: '2024-01-15 11:00:00',
      roleLevel: 2
    },
    {
      _id: '3',
      code: 'USER003',
      name: '王五',
      roleCodes: ['user', 'editor'],
      roleCode: 'editor',
      nickname: '小王',
      pictureUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user4',
      email: '<EMAIL>',
      createTime: '2024-01-15 12:00:00',
      updateTime: '2024-01-15 12:00:00',
      roleLevel: 2
    }
  ],
  // proxyConfig: {
  //   sort: true,
  //   form: true,
  //   ajax: {
  //     query({ page, form, sorts }) {
  //       const params = {
  //         ...page,
  //         ...form,
  //         roleCodes: form.roleCodes ? form.roleCodes.join(',') : '',
  //         orderBy: sorts.map(item => `${item.field}|${item.order}`).join(',')
  //       }
  //       return getPubAdminUserListPage(params)
  //     },
  //     save({ body }) {
  //       return postPubAdminUserSaveBatch(body)
  //     }
  //   }
  // }
})

const removeRow = async (row) => {
  const $grid = gridRef.value.getTableInstance()
  console.log("$grid",$grid)
  if ($grid && $grid.isInsertByRow(row)) {
    $grid.remove(row)
    return
  }
  const type = await VxeUI.modal.confirm({
    content: `请确认是否删除 “ ${row.name} ”？`
  })
  if (type === 'confirm') {
    // deletePubAdminUserDelete({ _id: row._id }).then(() => {
    //   if ($grid) {
    //     $grid.commitProxy('query')
    //   }
      VxeUI.modal.message({
        content: '删除成功',
        status: 'success'
      })
    // })
  }
}

const gridEvents: VxeGridListeners = {
  editDisabled({ row }) {
    if (userStore.userRoleLevel >= row.roleLevel) {
      VxeUI.modal.message({
        id: 'noPermissionEdit',
        content: '无法编辑，权限不够！',
        status: 'warning'
      })
    }
  }
}


// 添加树形数据
const treeData = reactive([
  {
    id: '1',
    sname: '平台架构',
    children: [
      { id: '1-1', sname: '主站' },
      { id: '1-2', sname: '测试' }
    ]
  }
])

// 当前选中的节点
const currentNode = ref(null)

// 表单新配置
const formProps = ref<ExtendedFormProps>({
  items: [
    {
      field: 'name',
      title: '名称',
      span: 12,
      itemRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入名称'
        }
      }
    },
    {
      field: 'nickname',
      title: '昵称',
      span: 12,
      itemRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入昵称'
        }
      }
    },
    {
      field: 'sex',
      title: '性别',
      span: 12,
      itemRender: {
        name: 'VxeSelect',
        // 三种方式选择一种使用
        // 1. 直接使用options
        options: [
          { label: '女', value: 'Women' },
          { label: '男', value: 'Men' }
        ],
        // 2. 使用字典
        // dict: 'sys_sex',
        // 3. 使用API
        // selectApi: 'http://************:8083/sys/temp/list',
        props: {
          placeholder: '请选择性别'
        }
      }
    },
    {
      field: 'age',
      title: '年龄',
      span: 12,
      itemRender: {
        name: 'VxeInput',
        props: {
          type: 'number',
          placeholder: '请输入年龄'
        }
      }
    },
    {
      field: 'address',
      title: '地址',
      span: 24,
      itemRenders: [
        {
          name: 'VxeTextarea',
          props: {
            width: '30%',
            height: '30%',
            placeholder: '请输入地址'
          }
        },
        {
          name: 'VxeInput',
          props: {
            width: '40%',
            type: 'number',
            placeholder: '请输入地址'
          }
        }
      ]
    }
  ],
  getData: {
    url: 'http://xxxx.xxx.xxx',
    method: 'post',//仅支持post/get
    body: {
      id: 1
    },
    header: {
      token: '11111111111111'
    }
  },
  applyData: {
    url: 'http://************:8080/saveOrUpdate',
    method: 'post',//仅支持post/get
    body: { //如果body节点不传,默认传data节点的数据

    },
    header: {
      token: '11111111111'
    }
  },
  data: {
    name: '11',
    nickname: '11',
    sex: '11',
    age: '',
    address: ''
  },
  rules: {
    name: [
      { required: true, validator: 'ValidName' }
    ],
    nickname: [
      { required: true, validator: 'ValidNikeName' }
    ]
  },
  formType: 'edit'
})

// 表单显示控制
const showForm = ref(false)
const loading = ref(false)
const formType = ref<'add' | 'edit' | 'view'>('add')

// 处理表单提交
const handleFormSubmit = async (data: any) => {
  loading.value = true
  try {
    if (formType.value === 'add') {
      await addDept(data)
    } else {
      await updateDept(data)
    }
    VxeUI.modal.message({ content: '保存成功', status: 'success' })
    showForm.value = false
    // 刷新数据
    await getMenuTree()
  } catch (error) {
    console.error('保存失败:', error)
    VxeUI.modal.message({ content: '保存失败', status: 'error' })
  } finally {
    loading.value = false
  }
}

// 打开添加对话框
const handleAdd = () => {
  formType.value = 'add'
  formProps.value = {
    ...formProps.value,
    data: {
      name: '',
      nickname: '',
      sex: '',
      age: '',
      address: ''
    }
  }
  showForm.value = true
}

// 打开编辑对话框
const handleEdit = () => {
  formType.value = 'edit'
  formProps.value = {
    ...formProps.value,
    data: {
      name: 'Test',
      nickname: 'Test',
      sex: 'Men',
      age: '18',
      address: 'Test Address'
    }
  }
  showForm.value = true
}

// 树节点点击事件
const handleNodeClick = (params: any) => {
  currentNode.value = params.row
  console.log('当前节点:', currentNode.value)
}

// 删除按钮点击事件
const handleDelete = async () => {
  if (!currentNode.value) return
  // 如果有子节点则不允许删除
  if (currentNode.value.children?.length) {
    VxeUI.modal.message({
      content: '该节点包含子节点,不能删除！',
      status: 'warning'
    })
    return
  }

  const type = await VxeUI.modal.confirm({
    content: `确定要删除 "${currentNode.value.sname}" 吗？`
  })
  if (type === 'confirm') {
    try {
      // TODO: 调用删除API
      await deleteDept(currentNode.value.id)
      VxeUI.modal.message({
        content: '删除成功',
        status: 'success'
      })
      // 刷新树形数据
      await getMenuTree()
    } catch (error) {
      console.error('删除失败:', error)
    }
  }
}

// 获取菜单树的 API
const getMenuTree = async () => {
  // try {
  //   const response = await getPubAdminMenuTree()
  //   treeData.value = response.data
  // } catch (error) {
  //   console.error('获取菜单树失败:', error)
  // }
}

// 添加到 setup 中
onMounted(async () => {
  await getMenuTree()
})

// API 接口函数示例 (需要根据实际情况实现)
const addDept = async (data: any) => {
  // return await api.post('/dept/add', data)
}

const updateDept = async (data: any) => {
  // return await api.post('/dept/update', data)
}

const deleteDept = async (id: string) => {
  // return await api.post('/dept/delete', { id })
}

const fileList = ref([])

const uploadMethod: VxeUploadPropTypes.UploadMethod = ({ file, updateProgress }) => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('path', '2024-11-19')
  formData.append('output', 'json')
  return axios.post('http://172.16.0.229:1201/group1/upload', formData, {
    onUploadProgress(progressEvent) {
      const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 0))
      // 更新进度
      updateProgress(percentCompleted)
    }
  }).then((res) => {
    return {
      ...res.data
    }
  })
}

const gridOptions1 = reactive<ExtendedGridProps>({
  headerAlign: 'center',
  border: false,
  scrollY: { enabled: false },
  treeConfig: {
    rowField: 'id',
    childrenField: 'children'
  },
  columns: [
    { field: 'name', title: '部门', width: '30%', treeNode: true },
    {
      field: 'description',
      title: '描述',
      width: '40%',
      component: { name: 'VxeInput', props: { rows: 3 } },
    },
    {
      field: 'status',
      title: '状态',
      width: '30%',
      // component: {name:'VxeSelect',options:[{label:'启用',value:1},{label:'禁用',value:0}],props:{}},
      cellRender: {
        name: 'VxeSelect',
        props: {
          size: 'small'
        },
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 }
        ],
        events: {
          change(cellParams, params) {
            console.log("params=", params)
            console.log("cellParams=", cellParams)
            VxeUI.modal.message({
              content: `点击了 ${params.value}`,
              status: 'success'
            })
          }
        }
      }
    },
    {
      field: 'switch',
      title: '状态',
      width: '30%',
      component: { name: 'VxeSwitch' },
    },
    {
      field: 'action',
      title: '操作',
      width: '60%',
      // component: { name: 'TableActionBtn' },
      // cellRender: {
      //   name: 'VxeButtonGroup',
      //   props: {
      //     mode: 'text'
      //   },
      //   options: [
      //     { content: '查看', name: 'view' },
      //     { content: '删除', status: 'error', name: 'del' }
      //   ],
      //   events: {
      //     click(cellParams, params) {
      //       VxeUI.modal.message({
      //         content: `点击了 ${params.name}`,
      //         status: 'success'
      //       })
      //     }
      //   }
      // },
      cellRenders: [
        {
          name: 'VxeSelect',
          // selectApi: 'http://************:8083/sys/temp/list',
          // options: [
          //   { label: '启用', value: '1' },
          //   { label: '禁用', value: '0' }
          // ],
          props: {
            size: 'small',
            width: '50%'
          },
          events: {
            change(cellParams, params) {
              console.log('cellParams', cellParams.value.value)
              // console.log('params', params)
              VxeUI.modal.message({
                content: `点击了 ${params.name}`,
                status: 'success'
              })
            }
          }
        },
        {
          name: 'VxeSelect',
          options: [
            { label: '启用', value: 1 },
            { label: '禁用', value: 0 },
            { label: '删除', value: 3 },
          ],
          props: {
            size: 'small',
            width: '40%'
          },
          events: {
            change(cellParams, params) {
              console.log('cellParams', cellParams.value.value)
              VxeUI.modal.message({
                content: `点击了 ${params.name}`,
                status: 'success'
              })
            }
          }
        },
        {
          name: 'VxeInput',
          props: {
            size: 'small',
            width: '40%'
          },
          events: {
            change(cellParams, params) {
              console.log('cellParams', cellParams.value.value)
              VxeUI.modal.message({
                content: `点击了 ${params.name}`,
                status: 'success'
              })
            }
          }
        }
      ]
    }
  ],
  data: [
    {
      id: '1',
      name: '平台架构',
      description: '这是一个测试描述',
      status: 0,
      switch: 1,
      children: [
        {
          id: '1-1',
          name: '主站',
          description: '主站描述',
          status: 1,
          switch: 0
        },
        {
          id: '1-2',
          name: '测试',
          description: '测试描述',
          status: 0,
          switch: 1
        }
      ]
    }
  ],
  height: "auto"
})

</script>

<style scoped>
.grid-item>* {
  flex: 1;
  width: 100%;
  height: 100%;
}
</style>

