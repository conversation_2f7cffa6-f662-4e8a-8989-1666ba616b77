/**
* Description: 路由
* Created by h<PERSON>qingchao
* Date: 2024/10/29 18:18
* Update: 2024/10/29 18:18
*/
// 静态路由
export const staticRoutes = [
  {
    path: '/',
    name: 'home',
    redirect: '/login',
  },
  {
    path: '/login',
    name: 'login',
    meta: {
      title: '登录',
    },
    component: () => import('@/views/login/index.vue'),
  },
  {
    path: '/layout',
    name: 'layout', // 只有一级菜单且没有子菜单的父级路由
    redirect: '/login',
    children: [],
    component: () => import('@/layout/index.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/404.vue'),
  },
]

// 动态菜单
export const remoteMenus = [
  {
    sname: '我的案件', // 菜单名称
    path: '/myCase', // 路由url
    componentPath: 'views/myCase/index', // 组件路径
    componentName: 'myCase', //组件名称
    menuType: 1, // 0目录，1菜单项
    icon: '', // 图标
    hidden: false, // 菜单是否隐藏，为true时隐藏
    children: []
  },
  {
    sname: '业务配置',
    path: '/business',
    componentName: 'business',
    menuType: 0,
    icon: '',
    hidden: false,
    children: [
      {
        sname: '模型配置',
        path: '/model',
        componentPath: 'views/business/model/index',
        componentName: 'model',
        menuType: 1,
        icon: '',
        hidden: false,
      },
      {
        sname: '清单模板',
        path: '/inventoryTemplate',
        componentPath: 'views/business/inventoryTemplate/index',
        componentName: 'inventoryTemplate',
        menuType: 1,
        icon: '',
        hidden: false,
      },
      {
        sname: '清单维护',
        path: '/inventory',
        componentPath: 'views/business/inventory/index',
        componentName: 'inventory',
        menuType: 1,
        icon: '',
        hidden: false,
      },
      {
        sname: '定额维护',
        path: '/quota',
        componentPath: 'views/business/quota/index',
        componentName: 'quota',
        menuType: 1,
        icon: '',
        hidden: false,
      },
      {
        sname: '材料分类维护',
        path: '/category',
        componentPath: 'views/business/category/index',
        componentName: 'category',
        menuType: 1,
        icon: '',
        hidden: false,
      },
      {
        sname: '材料编码维护',
        path: '/code',
        componentPath: 'views/business/code/index',
        componentName: 'code',
        menuType: 1,
        icon: '',
        hidden: false,
      },
      {
        sname: '材料维护',
        path: '/material',
        componentPath: 'views/business/material/index',
        componentName: 'material',
        menuType: 1,
        icon: '',
        hidden: false,
      },
      {
        sname: '材料价格维护',
        path: '/price',
        componentPath: 'views/business/price/index',
        componentName: 'price',
        menuType: 1,
        icon: '',
        hidden: false,
      },
      {
        sname: '物损清单库',
        path: '/lossInventory',
        componentPath: 'views/business/lossInventory/index',
        componentName: 'lossInventory',
        menuType: 1,
        icon: '',
        hidden: false,
      },
      {
        sname: '计价程序维护',
        path: '/procedure',
        componentPath: 'views/business/procedure/index',
        componentName: 'procedure',
        menuType: 1,
        icon: '',
        hidden: false,
      },
      {
        sname: '清单组合维护',
        path: '/combination',
        componentPath: 'views/business/combination/index',
        componentName: 'combination',
        menuType: 1,
        icon: '',
        hidden: false,
      },
      {
        sname: '特征库维护',
        path: '/feature',
        componentPath: 'views/business/feature/index',
        componentName: 'feature',
        menuType: 1,
        icon: '',
        hidden: false,
      },
      {
        sname: '族维护',
        path: '/race',
        componentPath: 'views/business/race/index',
        componentName: 'race',
        menuType: 1,
        icon: '',
        hidden: false,
      },
    ]
  },
  {
    sname: '系统设置',
    path: '/system',
    componentName: 'system',
    menuType: 0,
    icon: '',
    hidden: false,
    children: [
      {
        sname: '字典管理',
        path: '/dict',
        componentPath: 'views/system/dict/index',
        componentName: 'dict',
        menuType: 1,
        icon: '',
        hidden: false,
      },
      {
        sname: '菜单管理',
        path: '/menu',
        componentPath: 'views/system/menu/index',
        componentName: 'menu',
        menuType: 1,
        icon: '',
        hidden: false,
      },
      {
        sname: '角色管理',
        path: '/role',
        componentPath: 'views/system/role/index',
        componentName: 'role',
        menuType: 1,
        icon: '',
        hidden: false,
      },
      {
        sname: '用户管理',
        path: '/user',
        componentPath: 'views/system/user/index',
        componentName: 'user',
        menuType: 1,
        icon: '',
        hidden: false,
      },
    ]
  },
]


