<!--
* Description: 容器拖拽，支持左右和上下拖拽
* Created by huqingchao
* Date: 2024/10/15 08:59
* Update: 2024/10/15 08:59
-->
<script setup>
import {onMounted, ref} from 'vue'
const props = defineProps({
  // 水平或者垂直拖拽
  mode: {
    type: String,
    default: 'horizontal', // horizontal | vertical
  },
  // 容器高度
  height: String,
  // 左边宽度，水平模式下用
  leftWidth: {
    type: String,
    default: '50%'
  },
  // 上边高度，垂直模式下用
  topHeight: {
    type: String,
    default: '50%',
  },
  // 中间拖拽的背景颜色
  lineBackground: {
    type: String,
    default: '#f8f8f9',
  },
  // 拖拽的线宽
  lineWidth: {
    type: String,
    default: '10px',
  },
  // 是否显示拖拽bar
  showBar: {
    type: Boolean,
    default: false,
  }
})
const emit = defineEmits(['resizeStart', 'resizeEnd'])
const leftDom = ref(null)
const resizeDom = ref(null)
const rightDom = ref(null)
const boxDom = ref(null)
const topDom = ref(null)
const bottomDom = ref(null)
// 水平模式初始化宽
const handleInitWidth = () => {
  leftDom.value.style.width = props.leftWidth
  resizeDom.value.style.left = props.leftWidth
  if (props.leftWidth.includes('%')) {
    rightDom.value.style.width = `${100 - parseInt(props.leftWidth)}%`
  } else if (props.leftWidth.includes('px')) {
    rightDom.value.style.width = `calc(100% - ${props.leftWidth})`
  }

}
// 垂直模式初始化高度
const handleInitHeight = () => {
  topDom.value.style.height = props.topHeight
  resizeDom.value.style.top = props.topHeight
  if (props.topHeight.includes('%')) {
    bottomDom.value.style.height = `${100 - parseInt(props.topHeight)}%`
  } else if (props.topHeight.includes('px')) {
    bottomDom.value.style.height = `calc(100% - ${props.topHeight})`
  }
}
// 水平模式初始化
const handleHorizontalInit = () => {
  handleInitWidth()
  resizeDom.value.onmousedown = (e) => {
    emit('resizeStart')
    resizeDom.value.style.cursor = 'ew-resize'
    const startX = e.clientX;
    const resizeLeft = resizeDom.value.offsetLeft
    // 鼠标拖动事件
    document.onmousemove = (e) => {
      const endX = e.clientX
      let leftDistance = resizeLeft + (endX - startX) // （endx-startx）=移动的距离。resizeDom.value.left+移动的距离=左边区域最后的宽度
      const maxT = boxDom.value.clientWidth - resizeDom.value.offsetWidth

      if (leftDistance < 32) leftDistance = 32 // 左边区域的最小宽度为32px
      if (leftDistance > maxT - 150) leftDistance = maxT - 150 //右边区域最小宽度为150px
      const leftPercent = leftDistance / boxDom.value.clientWidth * 100
      const rightPercent = 100 - leftPercent
      resizeDom.value.style.left = leftPercent + '%'
      leftDom.value.style.width = leftPercent + '%'
      rightDom.value.style.width = rightPercent + '%'
      e.preventDefault()
      e.stopPropagation()
    };
    // 鼠标松开事件
    document.onmouseup =  () => {
      resizeDom.value.style.cursor = 'col-resize'
      document.onmousemove = null
      document.onmouseup = null
      emit('resizeEnd')
    };
  };
}
// 垂直模式初始化
const handleVerticalInit = () => {
  handleInitHeight()
  resizeDom.value.onmousedown = (e) => {
    emit('resizeStart')
    resizeDom.value.style.cursor = 'ns-resize'
    const startY = e.clientY;
    const resizeTop= resizeDom.value.offsetTop
    // 鼠标拖动事件
    document.onmousemove = (e) => {
      const endY = e.clientY;
      let topDistance = resizeTop + (endY - startY)
      const maxT = boxDom.value.clientHeight - resizeDom.value.offsetHeight

      if (topDistance < 32) topDistance = 32
      if (topDistance > maxT - 150) topDistance = maxT - 150
      const topPercent = topDistance / boxDom.value.clientHeight * 100
      const bottomPercent = 100 - topPercent
      resizeDom.value.style.top = topPercent + '%'
      topDom.value.style.height = topPercent + '%'
      bottomDom.value.style.height = bottomPercent + '%'
      e.preventDefault()
      e.stopPropagation()
    };
    // 鼠标松开事件
    document.onmouseup =  () => {
      resizeDom.value.style.cursor = 'row-resize'
      document.onmousemove = null
      document.onmouseup = null
      emit('resizeEnd')
    };
  };
}

onMounted(() => {
  if (props.mode === 'horizontal') {
    handleHorizontalInit()
  } else {
    handleVerticalInit()
  }
})
</script>

<template>
<div class="move-box" ref="boxDom" :style="{ height, 'flex-direction' : mode === 'horizontal' ? 'row' : 'column' }">
  <template v-if="mode === 'horizontal'">
    <div class="move-box__left" ref="leftDom">
      <slot name="left"></slot>
    </div>
    <div :class="['move-box__line-vertical', showBar ? 'vertical--bar' : '']" ref="resizeDom" title="鼠标按下拖拽" :style="{ background: lineBackground, width: lineWidth }">
      <div class="vertical--bar-wrapper" v-if="showBar">
        <i class="vertical--bar-item" v-for="item in 6" :key="item"></i>
      </div>
    </div>
    <div class="move-box__right" ref="rightDom" :style="{ paddingLeft: lineWidth }">
      <slot name="right"></slot>
    </div>
  </template>
  <template v-else>
    <div class="move-box__top" ref="topDom">
      <slot name="top"></slot>
    </div>
    <div :class="['move-box__line-horizontal', showBar ? 'horizontal--bar' : '']" ref="resizeDom" title="鼠标按下拖拽" :style="{ background: lineBackground, height: lineWidth }">
      <div class="horizontal--bar-wrapper" v-if="showBar">
        <i class="horizontal--bar-item" v-for="item in 6" :key="item"></i>
      </div>
    </div>
    <div class="move-box__bottom" ref="bottomDom" :style="{ paddingTop: lineWidth }">
      <slot name="bottom"></slot>
    </div>
  </template>
</div>
</template>

<style scoped lang="less">
.move-box{
  display: flex;
  background: #ffffff;
  position: relative;
  &__left{
    width: 50%;
  }
  &__line-vertical{
    position: absolute;
    left: 50%;
    width: 10px;
    height: 100%;
    cursor: col-resize;
    user-select: none;
  }
  .vertical--bar{
    background: #f8f8f9;
    border: 1px solid #dcdee2;
    border-top: none;
    border-bottom: none;
    &-wrapper{
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 100%;
    }
    &-item{
      display: block;
      width: 100%;
      height: 1px;
      margin-bottom: 5px;
      background: rgba(23, 35, 61, 0.25);
    }
  }
  &__right{
    width: 50%;
    padding-left: 10px;
  }
  &__top{
    width: 100%;
    height: 30%;
    overflow-y: auto;
  }
  &__line-horizontal{
    position: absolute;
    left: 0;
    top: 30%;
    height: 10px;
    width: 100%;
    cursor: row-resize;
    user-select: none;
  }
  .horizontal--bar{
    background: #f8f8f9;
    border: 1px solid #dcdee2;
    border-left: none;
    border-right: none;
    &-wrapper{
      position: absolute;
      left: 50%;
      top: 0;
      transform: translateX(-50%);
      height: 100%;
      display: flex;
    }
    &-item{
      height: 100%;
      width: 1px;
      margin-left: 5px;
      background: rgba(23, 35, 61, 0.25);
    }
  }
  &__bottom{
    padding-top: 10px;
    width: 100%;
    overflow-y: auto;
  }
}
</style>
