/**
* Description: 全局基础样式
* Created by h<PERSON>qingchao
* Date: 2024/10/15 10:27
* Update: 2024/10/15 10:27
*/
* {
  padding: 0;
  margin: 0;
  font-size: 100%;
  border: 0;
  outline: 0;
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-size: 12px;
  line-height: unset;
}

body {
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif !important;
  -webkit-font-smoothing: antialiased;
}

a {
  text-decoration: none;
}

ol,
ul {
  list-style: none;
}

#app {
  width: 100%;
  height: 100%;
}

.base-search{
  padding: 20px 0 10px 10px;
  background-color: #fff;
  margin-bottom: 10px;
  &-collapse{
    color: var(--ti-base-color-brand-6);
    svg{
      margin-left: 5px;
      fill: var(--ti-base-color-brand-6);
      transform: rotate(-90deg);
      font-size: 16px;
    }
    font-size: 14px;
    padding-top: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    width: 50px;
  }
  &-collapse.is--collapse{
    svg{
      transform: rotate(90deg);
    }
  }
  .tiny-cascader{
    width: 100%;
  }
}

.base-back-wrapper{
  display: flex;
  justify-content: space-between;
  padding-right: 10px;
}

.base-title{
  font-size: 16px;
  font-weight: bold;
  color: #000;
  margin-bottom: 20px;
}

.base-search-operate{
  text-align: right;
}

.base-content{
  background: #ffffff;
  padding: 10px;
  .base-content-operate{
    display: flex;
    justify-content: end;
    margin-bottom: 10px;
    min-height: 28px;
  }
}
.base-table-operate__icon1{
  display: flex;
  .icon-wrap{
    margin-right: 5px;
    padding: 0 5px;
    color: var(--ti-base-color-brand-6);
    border: 1px solid var(--ti-base-color-brand-6);
    border-radius: 10px;
    display: flex;
    align-items: center;
    cursor: pointer;
    &:last-child{
      margin-right: 0;
    }
    svg{
      fill: var(--ti-base-color-brand-6);
    }
  }
  .is-disabled{
    cursor: not-allowed;
    color: #c2c2c2;
    border-color: #dbdbdb;
    svg {
      fill: #c2c2c2;
    }
  }
}
.base-table-operate__icon2{
  display: flex;
  .icon-wrap{
    cursor: pointer;
    margin-right: 10px;
    &:last-child{
      margin-right: 0;
    }
    font-size: var(--dx-ui-font-size-xl);
    svg{
      fill: var(--ti-base-color-brand-6);
    }
  }
  .is-disabled{
    cursor: not-allowed;
    color: #c2c2c2;
    border-color: #dbdbdb;
    svg {
      fill: #c2c2c2;
    }
  }
}

.base-input-button{
  display: flex;
  .tiny-input{
    width: 200px;
  }
}

.base-brand{
  color: var(--ti-base-color-brand-6);
  cursor: pointer;
}

.vxe-table--render-default .vxe-body--row.row--current{
  position: relative;
  &:after {
    content: ' ';
    position: absolute;
    left: 0;
    top: 0;
    width: 5px;
    height: 100%;
    background-color: var(--ti-base-color-brand-6);
  }
}

.tiny-popover.login-header-popover{
  padding-left: 0;
  padding-right: 0;
  .operate{
    width: 80px;
    &-item{
      cursor: pointer;
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      &:hover{
        background-color: #f5f5f5;
        color: #191919;
      }
    }
  }
}

.flex-center{
	display: flex;
	align-items: center;
}
.flex-between{
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.flex-end{
	display: flex;
	align-items: center;
	justify-content: flex-end;
}
.base-form-border{
  
  border: 1px solid #dfe1e6;
  border-top: none;
  .tiny-form-item{
    margin-bottom: 0;
    background: #fafafa;
  }
  .tiny-form-item__content{
    border: 1px solid #dfe1e6;
    border-right: none;
    border-bottom: none;
    line-height: 46px;
    background: #ffffff;
  }
  .tiny-form-item__label{
    border-top: 1px solid #dfe1e6;
    line-height: 46px;
    height: 46px;
  }
  .tiny-input__inner,.tiny-numeric__input-inner{
    height: 46px !important;
    line-height: 46px;
    border: none;
  }
  .tiny-textarea__inner,.tiny-textarea.is-disabled .tiny-textarea__inner,.tiny-numeric__input.is-disabled .tiny-numeric__input-inner{
    border: none;
  }
  .tiny-input__inner:active, .tiny-input__inner:focus, .tiny-input__inner:hover, .tiny-textarea__inner:hover, .tiny-numeric__input-inner:hover{
    border: 1px solid #575d6c;
  }
  .tiny-input.is-active .tiny-input__inner, .tiny-input__inner:focus, .tiny-textarea__inner:focus, .tiny-numeric__input-inner:active, .tiny-numeric__input-inner:focus{
    border: 1px solid #5e7ce0;
  }
  .tiny-form-item.is-error .tiny-input__inner, .tiny-form-item.is-error .tiny-input__inner:focus, .tiny-form-item.is-error .tiny-textarea, .tiny-form-item.is-error .tiny-textarea:focus, .tiny-form-item.is-error .tiny-textarea__inner, .tiny-form-item.is-error .tiny-textarea__inner:focus,.tiny-form-item.is-error .tiny-numeric__input-inner, .tiny-form-item.is-error .tiny-numeric__input-inner:focus{
    border: 1px solid #f66f6a;
  }
  .tiny-form-item.is-error .tiny-textarea__inner, .tiny-form-item.is-error .tiny-textarea__inner:focus{
    border-width: 0;
  }
  .tiny-input.is-disabled .tiny-input__inner, .tiny-input-group__append, .tiny-input-group__prepend{
    border: none;
  }
  .tiny-range-editor:not(.is-display-only).is-disabled:focus, .tiny-range-editor:not(.is-display-only).is-disabled:hover{
    border: none;
  }
  .tiny-cascader{
    width: 100%;
  }
  .tiny-date-editor .tiny-range-separator{
    line-height: 40px;
  }
  .tiny-radio{
    line-height: 46px;
  }
}

.vxe-button{
  min-width: var(--dx-ui-button-width);
}

.vxe-header--column{
  font-weight: normal;
}

.tiny-dialog-box .tiny-dialog-box__footer .tiny-button{
  min-width: var(--dx-ui-button-width);
}

.tiny-date-table td.today span{
  line-height: 28px;
  height: 28px;
  min-width: 28px;
}
.tiny-date-table td div{
  height: 28px;
}
html[data-dx-ui-size=large]{
  .tiny-range-editor--small:not(.is-display-only) .tiny-range-separator{
    line-height: 32px;
  }
}
.tiny-tag{
  height: var(--dx-ui-tag-height);
  line-height: var(--dx-ui-tag-height);
}