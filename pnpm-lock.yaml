lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@alova/scene-vue':
        specifier: ^1.5.0
        version: 1.6.2
      '@opentiny/vue':
        specifier: ^3.20.0
        version: 3.21.2(echarts@5.4.1)
      '@opentiny/vue-icon':
        specifier: ^3.18.0
        version: 3.21.0
      '@opentiny/vue-theme':
        specifier: ^3.21.2
        version: 3.21.2
      '@varlet/axle':
        specifier: ^0.7.1
        version: 0.7.1(vue@3.5.13(typescript@5.7.3))
      '@vxe-ui/plugin-export-xlsx':
        specifier: ^4.0.7
        version: 4.0.13
      '@vxe-ui/plugin-menu':
        specifier: ^4.0.2
        version: 4.0.7
      '@vxe-ui/plugin-render-wangeditor':
        specifier: ^4.0.2
        version: 4.0.3
      axios:
        specifier: ^1.7.9
        version: 1.7.9
      dayjs:
        specifier: ^1.11.12
        version: 1.11.13
      dx-ui-var:
        specifier: ^0.0.1-beta.4
        version: 0.0.1-beta.4
      grid-layout-plus:
        specifier: ^1.0.5
        version: 1.0.6(vue@3.5.13(typescript@5.7.3))
      lodash:
        specifier: ^4.17.21
        version: 4.17.21
      mitt:
        specifier: ^3.0.1
        version: 3.0.1
      nprogress:
        specifier: ^0.2.0
        version: 0.2.0
      pinia:
        specifier: ^2.1.7
        version: 2.3.1(typescript@5.7.3)(vue@3.5.13(typescript@5.7.3))
      pinia-plugin-persistedstate:
        specifier: ^3.2.1
        version: 3.2.3(pinia@2.3.1(typescript@5.7.3)(vue@3.5.13(typescript@5.7.3)))
      vite-plugin-compression2:
        specifier: ^1.1.2
        version: 1.3.3(rollup@4.34.6)(vite@5.4.14(@types/node@22.13.1)(less@4.2.2)(sass@1.84.0))
      vue:
        specifier: ^3.4.21
        version: 3.5.13(typescript@5.7.3)
      vue-router:
        specifier: ^4.3.3
        version: 4.5.0(vue@3.5.13(typescript@5.7.3))
      vxe-pc-ui:
        specifier: 4.3.50
        version: 4.3.50(vue@3.5.13(typescript@5.7.3))
      vxe-table:
        specifier: 4.9.19
        version: 4.9.19(vue@3.5.13(typescript@5.7.3))
      workerpool:
        specifier: ^9.1.3
        version: 9.2.0
      xe-utils:
        specifier: ^3.5.32
        version: 3.7.0
    devDependencies:
      '@opentiny/unplugin-tiny-vue':
        specifier: ^0.0.2
        version: 0.0.2(@babel/parser@7.26.8)(rollup@4.34.6)(vite@5.4.14(@types/node@22.13.1)(less@4.2.2)(sass@1.84.0))(vue@3.5.13(typescript@5.7.3))
      '@types/echarts':
        specifier: ^4.9.22
        version: 4.9.22
      '@types/node':
        specifier: ^22.3.0
        version: 22.13.1
      '@types/vue':
        specifier: ^2.0.0
        version: 2.0.0(typescript@5.7.3)
      '@typescript-eslint/eslint-plugin':
        specifier: ^4.6.1
        version: 4.33.0(@typescript-eslint/parser@4.33.0(eslint@7.32.0)(typescript@5.7.3))(eslint@7.32.0)(typescript@5.7.3)
      '@typescript-eslint/parser':
        specifier: ^4.6.1
        version: 4.33.0(eslint@7.32.0)(typescript@5.7.3)
      '@vitejs/plugin-vue':
        specifier: ^5.1.2
        version: 5.2.1(vite@5.4.14(@types/node@22.13.1)(less@4.2.2)(sass@1.84.0))(vue@3.5.13(typescript@5.7.3))
      '@vitejs/plugin-vue-jsx':
        specifier: ^4.0.0
        version: 4.1.1(vite@5.4.14(@types/node@22.13.1)(less@4.2.2)(sass@1.84.0))(vue@3.5.13(typescript@5.7.3))
      less:
        specifier: ^4.2.0
        version: 4.2.2
      sass:
        specifier: ^1.77.5
        version: 1.84.0
      typescript:
        specifier: ^5.0.0
        version: 5.7.3
      unplugin-auto-import:
        specifier: ^0.17.6
        version: 0.17.8(rollup@4.34.6)
      unplugin-vue-components:
        specifier: ^0.27.0
        version: 0.27.5(@babel/parser@7.26.8)(rollup@4.34.6)(vue@3.5.13(typescript@5.7.3))
      vite:
        specifier: ^5.2.0
        version: 5.4.14(@types/node@22.13.1)(less@4.2.2)(sass@1.84.0)
      vite-plugin-lazy-import:
        specifier: ^1.0.7
        version: 1.0.7
      vite-svg-loader:
        specifier: ^5.1.0
        version: 5.1.0(vue@3.5.13(typescript@5.7.3))
      vue-tsc:
        specifier: ^2.0.29
        version: 2.2.0(typescript@5.7.3)

packages:

  '@alova/scene-vue@1.6.2':
    resolution: {integrity: sha512-wHUEVmZWCz9y/nqq3NrSxnxC+vjlQTm5F8aOFI5p8Q4ADb/HV9bgO3p7Xh29gO4ZGiyLnUv1LtoRt5b0HrDj+A==}

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}

  '@antfu/utils@0.7.10':
    resolution: {integrity: sha512-+562v9k4aI80m1+VuMHehNJWLOFjBnXn3tdOitzD0il5b7smkSBal4+a3oKiQTbrwMmN/TBUMDvbdoWDehgOww==}

  '@babel/code-frame@7.12.11':
    resolution: {integrity: sha512-Zt1yodBx1UcyiePMSkWnU4hPqhwq7hGi2nFL1LeA3EUl+q2LQx16MISgJ0+z7dnmgvP9QtIleuETGOiOH1RcIw==}

  '@babel/code-frame@7.26.2':
    resolution: {integrity: sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.26.8':
    resolution: {integrity: sha512-oH5UPLMWR3L2wEFLnFJ1TZXqHufiTKAiLfqw5zkhS4dKXLJ10yVztfil/twG8EDTA4F/tvVNw9nOl4ZMslB8rQ==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.26.8':
    resolution: {integrity: sha512-l+lkXCHS6tQEc5oUpK28xBOZ6+HwaH7YwoYQbLFiYb4nS2/l1tKnZEtEWkD0GuiYdvArf9qBS0XlQGXzPMsNqQ==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.26.8':
    resolution: {integrity: sha512-ef383X5++iZHWAXX0SXQR6ZyQhw/0KtTkrTz61WXRhFM6dhpHulO/RJz79L8S6ugZHJkOOkUrUdxgdF2YiPFnA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.25.9':
    resolution: {integrity: sha512-gv7320KBUFJz1RnylIg5WWYPRXKZ884AGkYpgpWW02TH66Dl+HaC1t1CKd0z3R4b6hdYEcmrNZHUmfCP+1u3/g==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.26.5':
    resolution: {integrity: sha512-IXuyn5EkouFJscIDuFF5EsiSolseme1s0CZB+QxVugqJLYmKdxI1VfIBOst0SUu4rnk2Z7kqTwmoO1lp3HIfnA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-create-class-features-plugin@7.25.9':
    resolution: {integrity: sha512-UTZQMvt0d/rSz6KI+qdu7GQze5TIajwTS++GUozlw8VBJDEOAqSXwm1WvmYEZwqdqSGQshRocPDqrt4HBZB3fQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-member-expression-to-functions@7.25.9':
    resolution: {integrity: sha512-wbfdZ9w5vk0C0oyHqAJbc62+vet5prjj01jjJ8sKn3j9h3MQQlflEdXYvuqRWjHnM12coDEqiC1IRCi0U/EKwQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.25.9':
    resolution: {integrity: sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.26.0':
    resolution: {integrity: sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-optimise-call-expression@7.25.9':
    resolution: {integrity: sha512-FIpuNaz5ow8VyrYcnXQTDRGvV6tTjkNtCK/RYNDXGSLlUD6cBuQTSw43CShGxjvfBTfcUA/r6UhUCbtYqkhcuQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.26.5':
    resolution: {integrity: sha512-RS+jZcRdZdRFzMyr+wcsaqOmld1/EqTghfaBGQQd/WnRdzdlvSZ//kF7U8VQTxf1ynZ4cjUcYgjVGx13ewNPMg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-replace-supers@7.26.5':
    resolution: {integrity: sha512-bJ6iIVdYX1YooY2X7w1q6VITt+LnUILtNk7zT78ykuwStx8BauCzxvFqFaHjOpW1bVnSUM1PN1f0p5P21wHxvg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    resolution: {integrity: sha512-K4Du3BFa3gvyhzgPcntrkDgZzQaq6uozzcpGbOO1OEJaI+EJdqWIMTLgFgQf6lrfiDFo5FU+BxKepI9RmZqahA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.25.9':
    resolution: {integrity: sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.25.9':
    resolution: {integrity: sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.25.9':
    resolution: {integrity: sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.26.7':
    resolution: {integrity: sha512-8NHiL98vsi0mbPQmYAGWwfcFaOy4j2HY49fXJCfuDcdE7fMIsH9a7GdaeXpIBsbT7307WU8KCMp5pUVDNL4f9A==}
    engines: {node: '>=6.9.0'}

  '@babel/highlight@7.25.9':
    resolution: {integrity: sha512-llL88JShoCsth8fF8R4SJnIn+WLvR6ccFxu1H3FlMhDontdcmZWf2HgIZ7AIqV3Xcck1idlohrN4EUBQz6klbw==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.26.8':
    resolution: {integrity: sha512-TZIQ25pkSoaKEYYaHbbxkfL36GNsQ6iFiBbeuzAkLnXayKR1yP1zFe+NxuZWWsUyvt8icPU9CCq0sgWGXR1GEw==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-syntax-jsx@7.25.9':
    resolution: {integrity: sha512-ld6oezHQMZsZfp6pWtbjaNDF2tiiCYYDqQszHt5VV437lewP9aSi2Of99CK0D0XB21k7FLgnLcmQKyKzynfeAA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-typescript@7.25.9':
    resolution: {integrity: sha512-hjMgRy5hb8uJJjUcdWunWVcoi9bGpJp8p5Ol1229PoN6aytsLwNMgmdftO23wnCLMfVmTwZDWMPNq/D1SY60JQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typescript@7.26.8':
    resolution: {integrity: sha512-bME5J9AC8ChwA7aEPJ6zym3w7aObZULHhbNLU0bKUhKsAkylkzUdq+0kdymh9rzi8nlNFl2bmldFBCKNJBUpuw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/template@7.26.8':
    resolution: {integrity: sha512-iNKaX3ZebKIsCvJ+0jd6embf+Aulaa3vNBqZ41kM7iTWjx5qzWKXGHiJUW3+nTpQ18SG11hdF8OAzKrpXkb96Q==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.26.8':
    resolution: {integrity: sha512-nic9tRkjYH0oB2dzr/JoGIm+4Q6SuYeLEiIiZDwBscRMYFJ+tMAz98fuel9ZnbXViA2I0HVSSRRK8DW5fjXStA==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.26.8':
    resolution: {integrity: sha512-eUuWapzEGWFEpHFxgEaBG8e3n6S8L3MSu0oda755rOfabWPnh0Our1AozNFVUxGFIhbKgd1ksprsoDGMinTOTA==}
    engines: {node: '>=6.9.0'}

  '@better-scroll/core@2.5.0':
    resolution: {integrity: sha512-+3aKf8T3kUl4Gj1M7NKV3fNFhsrBpTWwHoDClkXVmQ8S3TxMMHf6Kyw6l1zKsg4r+9ukW5lDDkyif7/gY76qXQ==}

  '@better-scroll/shared-utils@2.5.1':
    resolution: {integrity: sha512-AplkfSjXVYP9LZiD6JsKgmgQJ/mG4uuLmBuwLz8W5OsYc7AYTfN8kw6GqZ5OwCGoXkVhBGyd8NeC4xwYItp0aw==}

  '@better-scroll/wheel@2.5.0':
    resolution: {integrity: sha512-+cru8CtMtgGGMv3yOxn33ApbtatOZBVUCa7+X3UqVVyaxi6FbCrcSZCBlXhXpsFhJo1R282O6nQyik6KUidvoA==}

  '@esbuild/aix-ppc64@0.21.5':
    resolution: {integrity: sha512-1SDgH6ZSPTlggy1yI6+Dbkiz8xzpHJEVAlF/AM1tHPLsf5STom9rwtjE4hKAF20FfXXNTFqEYXyJNWh1GiZedQ==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.21.5':
    resolution: {integrity: sha512-c0uX9VAUBQ7dTDCjq+wdyGLowMdtR/GoC2U5IYk/7D1H1JYC0qseD7+11iMP2mRLN9RcCMRcjC4YMclCzGwS/A==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.21.5':
    resolution: {integrity: sha512-vCPvzSjpPHEi1siZdlvAlsPxXl7WbOVUBBAowWug4rJHb68Ox8KualB+1ocNvT5fjv6wpkX6o/iEpbDrf68zcg==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.21.5':
    resolution: {integrity: sha512-D7aPRUUNHRBwHxzxRvp856rjUHRFW1SdQATKXH2hqA0kAZb1hKmi02OpYRacl0TxIGz/ZmXWlbZgjwWYaCakTA==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.21.5':
    resolution: {integrity: sha512-DwqXqZyuk5AiWWf3UfLiRDJ5EDd49zg6O9wclZ7kUMv2WRFr4HKjXp/5t8JZ11QbQfUS6/cRCKGwYhtNAY88kQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.21.5':
    resolution: {integrity: sha512-se/JjF8NlmKVG4kNIuyWMV/22ZaerB+qaSi5MdrXtd6R08kvs2qCN4C09miupktDitvh8jRFflwGFBQcxZRjbw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.21.5':
    resolution: {integrity: sha512-5JcRxxRDUJLX8JXp/wcBCy3pENnCgBR9bN6JsY4OmhfUtIHe3ZW0mawA7+RDAcMLrMIZaf03NlQiX9DGyB8h4g==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.21.5':
    resolution: {integrity: sha512-J95kNBj1zkbMXtHVH29bBriQygMXqoVQOQYA+ISs0/2l3T9/kj42ow2mpqerRBxDJnmkUDCaQT/dfNXWX/ZZCQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.21.5':
    resolution: {integrity: sha512-ibKvmyYzKsBeX8d8I7MH/TMfWDXBF3db4qM6sy+7re0YXya+K1cem3on9XgdT2EQGMu4hQyZhan7TeQ8XkGp4Q==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.21.5':
    resolution: {integrity: sha512-bPb5AHZtbeNGjCKVZ9UGqGwo8EUu4cLq68E95A53KlxAPRmUyYv2D6F0uUI65XisGOL1hBP5mTronbgo+0bFcA==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.21.5':
    resolution: {integrity: sha512-YvjXDqLRqPDl2dvRODYmmhz4rPeVKYvppfGYKSNGdyZkA01046pLWyRKKI3ax8fbJoK5QbxblURkwK/MWY18Tg==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.21.5':
    resolution: {integrity: sha512-uHf1BmMG8qEvzdrzAqg2SIG/02+4/DHB6a9Kbya0XDvwDEKCoC8ZRWI5JJvNdUjtciBGFQ5PuBlpEOXQj+JQSg==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.21.5':
    resolution: {integrity: sha512-IajOmO+KJK23bj52dFSNCMsz1QP1DqM6cwLUv3W1QwyxkyIWecfafnI555fvSGqEKwjMXVLokcV5ygHW5b3Jbg==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.21.5':
    resolution: {integrity: sha512-1hHV/Z4OEfMwpLO8rp7CvlhBDnjsC3CttJXIhBi+5Aj5r+MBvy4egg7wCbe//hSsT+RvDAG7s81tAvpL2XAE4w==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.21.5':
    resolution: {integrity: sha512-2HdXDMd9GMgTGrPWnJzP2ALSokE/0O5HhTUvWIbD3YdjME8JwvSCnNGBnTThKGEB91OZhzrJ4qIIxk/SBmyDDA==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.21.5':
    resolution: {integrity: sha512-zus5sxzqBJD3eXxwvjN1yQkRepANgxE9lgOW2qLnmr8ikMTphkjgXu1HR01K4FJg8h1kEEDAqDcZQtbrRnB41A==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.21.5':
    resolution: {integrity: sha512-1rYdTpyv03iycF1+BhzrzQJCdOuAOtaqHTWJZCWvijKD2N5Xu0TtVC8/+1faWqcP9iBCWOmjmhoH94dH82BxPQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-x64@0.21.5':
    resolution: {integrity: sha512-Woi2MXzXjMULccIwMnLciyZH4nCIMpWQAs049KEeMvOcNADVxo0UBIQPfSmxB3CWKedngg7sWZdLvLczpe0tLg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-x64@0.21.5':
    resolution: {integrity: sha512-HLNNw99xsvx12lFBUwoT8EVCsSvRNDVxNpjZ7bPn947b8gJPzeHWyNVhFsaerc0n3TsbOINvRP2byTZ5LKezow==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.21.5':
    resolution: {integrity: sha512-6+gjmFpfy0BHU5Tpptkuh8+uw3mnrvgs+dSPQXQOv3ekbordwnzTVEb4qnIvQcYXq6gzkyTnoZ9dZG+D4garKg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.21.5':
    resolution: {integrity: sha512-Z0gOTd75VvXqyq7nsl93zwahcTROgqvuAcYDUr+vOv8uHhNSKROyU961kgtCD1e95IqPKSQKH7tBTslnS3tA8A==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.21.5':
    resolution: {integrity: sha512-SWXFF1CL2RVNMaVs+BBClwtfZSvDgtL//G/smwAc5oVK/UPu2Gu9tIaRgFmYFFKrmg3SyAjSrElf0TiJ1v8fYA==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.21.5':
    resolution: {integrity: sha512-tQd/1efJuzPC6rCFwEvLtci/xNFcTZknmXs98FYDfGE4wP9ClFV98nyKrzJKVPMhdDnjzLhdUyMX4PsQAPjwIw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]

  '@eslint/eslintrc@0.4.3':
    resolution: {integrity: sha512-J6KFFz5QCYUJq3pf0mjEcCJVERbzv71PUIDczuh9JkwGEzced6CO5ADLHB1rbf/+oPBtoPfMYNOpGDzCANlbXw==}
    engines: {node: ^10.12.0 || >=12.0.0}

  '@floating-ui/core@1.6.9':
    resolution: {integrity: sha512-uMXCuQ3BItDUbAMhIXw7UPXRfAlOAvZzdK9BWpE60MCn+Svt3aLn9jsPTi/WNGlRUu2uI0v5S7JiIUsbsvh3fw==}

  '@floating-ui/dom@1.6.13':
    resolution: {integrity: sha512-umqzocjDgNRGTuO7Q8CU32dkHkECqI8ZdMZ5Swb6QAM0t5rnlrN3lGo1hdpscRd3WS8T6DKYK4ephgIH9iRh3w==}

  '@floating-ui/utils@0.2.9':
    resolution: {integrity: sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==}

  '@humanwhocodes/config-array@0.5.0':
    resolution: {integrity: sha512-FagtKFz74XrTl7y6HCzQpwDfXP0yhxe9lHLD1UZxjvZIcbyRz8zTFF/yYNfSfzU414eDwZ1SrO0Qvtyf+wFMQg==}
    engines: {node: '>=10.10.0'}
    deprecated: Use @eslint/config-array instead

  '@humanwhocodes/object-schema@1.2.1':
    resolution: {integrity: sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA==}
    deprecated: Use @eslint/object-schema instead

  '@interactjs/types@1.10.27':
    resolution: {integrity: sha512-BUdv0cvs4H5ODuwft2Xp4eL8Vmi3LcihK42z0Ft/FbVJZoRioBsxH+LlsBdK4tAie7PqlKGy+1oyOncu1nQ6eA==}

  '@jridgewell/gen-mapping@0.3.8':
    resolution: {integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}

  '@juggle/resize-observer@3.4.0':
    resolution: {integrity: sha512-dfLbk+PwWvFzSxwk3n5ySL0hfBog779o8h68wK/7/APo/7cgyWp5jcXockbxdk5kFRkbeXWm4Fbi9FrdN381sA==}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}

  '@opentiny/fluent-editor@3.25.2':
    resolution: {integrity: sha512-A3QxpmkkfimsOax9DWUPe9PMKJEjtNBWYuxI7LP2/KjK9Yps13GC75v4z+w6O1cd+8QcpV3Ol6X3gzGn8u/ZGQ==}

  '@opentiny/huicharts@1.0.1':
    resolution: {integrity: sha512-KsVNZwCstMJec2vM/uMff9jj/nNHXCchiwGGU4+MGP1nRwg7Hkj8pjgJQh/tJR6+AEKPY/h+TewRM3S6KBtn5g==}

  '@opentiny/unplugin-tiny-vue@0.0.2':
    resolution: {integrity: sha512-VChuA8iQ1h7fBJjy2nZHOn0VfydO6vWFGbsxEBGykxBjrqMYGkbTjbAMSJYXKB3840pbBUOsMZ9vgKHUJVC9kQ==}
    peerDependencies:
      vite: '>=4'

  '@opentiny/utils@1.0.0':
    resolution: {integrity: sha512-At1fWN7qkJP0F5d4WtyE9uMPXdWwjvmrbymRzOtR+brEolJYzj1lCJHEDc6CMxSpckHETF7Y/6pKmwL2ZaiEFw==}

  '@opentiny/vue-action-menu@3.21.0':
    resolution: {integrity: sha512-qql1NV/0LaUJSF/H8PE0VBdcgWAw7goyXhFINVR+FhP6ZWKfSbpTkuKQz0G6afJ47E2ZAi51EEYcfSqp0pMSIQ==}

  '@opentiny/vue-action-sheet@3.21.0':
    resolution: {integrity: sha512-h1k219ItL3QPUGzPRZJGgse/5rh/ZBKcKa7pU3G3dyU2SVIBapmerFzFkBi/1k0lr4gtAFk+jMYvPWqjXfSEGg==}

  '@opentiny/vue-alert@3.21.0':
    resolution: {integrity: sha512-QbNsmjhHwbqZdgv46DQ/TQo0pGYgaaE8914VetnBjtmszLjwZj6HXHH0CsPhmHerYdIJuN7xsbEbqFH+C/Jc8Q==}

  '@opentiny/vue-amount@3.21.0':
    resolution: {integrity: sha512-KukI05gmrB4QKWjTvdrdnm/FX2dN6gLRWKgwPY4yfX9rjfw8b24kJMntR4DcNkwZFszjsVMd7Vf2HoB9kLsbsw==}

  '@opentiny/vue-anchor@3.21.0':
    resolution: {integrity: sha512-0xjW3xwO310VKxVKfMCRWBoFN+t3xaV5F6FwT5kZ4F6xqic/Y4VhF3EKXeSUCBxEYMHrSVVR/FS3gQBcEx02YQ==}

  '@opentiny/vue-area@3.21.0':
    resolution: {integrity: sha512-xNp7KlJlaF2izWSD1gEEVLw5f2Y6T79m9KQX81x3lIFxXPnvpQXOZiMGI77F7avU+kzjN5bhOddD5wHD19qreQ==}

  '@opentiny/vue-async-flowchart@3.21.0':
    resolution: {integrity: sha512-doKgcA2QVaXiN1qqS3avnRjgoY7WJaYnKZsEZONvkB3B8z22jWDoe4I+GB7V7n3/RmC8mJucXCg5qSb9rq6vHw==}

  '@opentiny/vue-autocomplete@3.21.0':
    resolution: {integrity: sha512-Me9c4bAizdCAMrh4pF1oGExEZOJGI9SRwbNfRWPCZkApPCRrb8hQZmBeq7XNBICekuwf8H1L2zLTOVSKacdq2Q==}

  '@opentiny/vue-avatar@3.21.0':
    resolution: {integrity: sha512-c2atOHurs1BgLF3C3tLWRCYVtQDCTQXbSvVmHFXyZvD9UNX7l6q83mKXqNjcgIr5EL3HNfDjcU7zFDhdZkKMzw==}

  '@opentiny/vue-badge@3.21.0':
    resolution: {integrity: sha512-A0rYR9NEW0Im+Fcff9mtXh3j+lsZI/UxzvTg1lE7E96eKrJgFGfPaTz6cJefeZgns1J++ixK8uSfxdCJ+X3zPQ==}

  '@opentiny/vue-base-select@3.21.0':
    resolution: {integrity: sha512-UbpJJwPPTKPgRxC2kCQwDROq4KVJHkRvY/5FQuNCp/y5zdCCzEAr11pUxN4TNVNg3dGYnqwZnfZ4qHi17yRX6Q==}

  '@opentiny/vue-breadcrumb-item@3.21.0':
    resolution: {integrity: sha512-7uUbJKVXBOLm4DcYaT6dHxi/bJkS81QFHDKKaZjMWQ6OiVt7oTOSPqkr7B4sTNIvogm7GIXL3UjL78C5+g71Ig==}

  '@opentiny/vue-breadcrumb@3.21.0':
    resolution: {integrity: sha512-t2IvC98CCG2j2aYXt8VR1/WBKUcuFwLsPJsvbRPnl/9/W5RghLQt0JGZxZlxSgeJcuYtWWSmt4GXH2sH7Z4Ssg==}

  '@opentiny/vue-bulletin-board@3.21.0':
    resolution: {integrity: sha512-fp8T+IZ1hkyHtTj0s7BClU2zAgf3sm5u6eNl4QAngNqu+xo1EklafSQtILdmitqMWDN9zmwkIbaBnLcVWbHbHA==}

  '@opentiny/vue-button-group@3.21.0':
    resolution: {integrity: sha512-8I62Fyq6klDWZaush7kv4X0mjpPX9mTY2uOV3daGWAtzsC5f+f9IcWXDbk/pNs4EhpjjBuzLt2D9/eAtzai0xg==}

  '@opentiny/vue-button@3.21.0':
    resolution: {integrity: sha512-a6aKpdsrgnio5yQAQUyP73ek/JnGnt2kyM54piDY7B27tAKVsrOGvpjbYfKVezSBbmz+CPzFGGGS/KP6E0/8pA==}

  '@opentiny/vue-calendar-bar@3.21.0':
    resolution: {integrity: sha512-Fqqr5TOhcNSkMz8QqK3rp/KIBQhuIJfKNySL2NQ8CNNQa+keWenc4h+0IYtQNtP8BHRUM/BugL1l+zvvKtL0iA==}

  '@opentiny/vue-calendar-view@3.21.0':
    resolution: {integrity: sha512-PjotBX2Si9qprqcN5F3CkUruGA0B0lo1zEVa1beJd36C8KapvA5hMKcjpBOjLYEAASxaTg1GIaknjS4BqlK30w==}

  '@opentiny/vue-calendar@3.21.0':
    resolution: {integrity: sha512-ZHy9AHuKftKWtci+LJoH3YyJNHMs+7GC6K6WpBgo071tvVB8EJ7Uwim+qFeGM3KKjsMGViGeQeF0D0onMyo64w==}

  '@opentiny/vue-card-group@3.21.0':
    resolution: {integrity: sha512-pdGz7kewbUU0U1kmDqCG55dzGsWc2nB1RUvs+J3R36s1ly7ds5xh86s+7fvTr2FoYF518QlCd96ggoiLvzPoAw==}

  '@opentiny/vue-card-template@3.21.0':
    resolution: {integrity: sha512-3UeVQNNLTREa8C//l5ZJie+g5NJyUOTyqIY5AVxcRfyDgpBh4aTnpt7+FbsHJZxFmYthUyPnA5SomgcNAVYpEQ==}

  '@opentiny/vue-card@3.21.0':
    resolution: {integrity: sha512-u0eiEzQYiGWviKqWeIsN2GO6rhTd007rVVsWUAz1tIP/ZuwOYpmKeFGcVJN/LqRq7GcYMkEnaDUI6Ev6Eib47A==}

  '@opentiny/vue-carousel-item@3.21.0':
    resolution: {integrity: sha512-lrVm1VxBJ9m5+GtZedONyv7v/mIf6jgw6DMpY5rPyc4d5MJIeoL4xMuxP3B2XPDgOwT1C0DFVeiQ4Gy3gljPeQ==}

  '@opentiny/vue-carousel@3.21.0':
    resolution: {integrity: sha512-WdJ5zzK2IFaCjw67IzCd0C4jZ8REBlT3l/XNQBkwNELwZb7jOu0DVrTy0toopYMUyztY6Ehf9wsuFB7pBpK3UQ==}

  '@opentiny/vue-cascader-menu@3.21.0':
    resolution: {integrity: sha512-LGoDHlmFUQQuo7Kl6WLPyMe9LwmZuG+5N/uh/gqq4sawUIXXhoHm2Y3m70lvYD7uEiN0EKyvw3E+64fMWx5NuA==}

  '@opentiny/vue-cascader-mobile@3.21.0':
    resolution: {integrity: sha512-EnnN2qrCuQBFrkmrWWrH/Hy1L2ejpNqXVQW5bvwO+LU6+5uYcowN6EsfunX3RH/Z6VbysV+qSWu/PxAflVaLEg==}

  '@opentiny/vue-cascader-node@3.21.0':
    resolution: {integrity: sha512-K/QXCFpjf7Ds2EFtBog9574WOlhJv0hXjEoCH/hBXCebSZfHDcjCi7Rj00cpdtL3nuy4bj1pRpnREPCdWz7QvA==}

  '@opentiny/vue-cascader-panel@3.21.0':
    resolution: {integrity: sha512-9fmIKjWT+83feCXKge9s9j1Qt/qOmC6lApch44bzSsue9QuTXSzk/ie9n75x+Yx82UukXwSnVrV5Vq/ecQe6pA==}

  '@opentiny/vue-cascader-select@3.21.0':
    resolution: {integrity: sha512-lkXVu9HNG49XDufEJiN9YrwzN0buZQ3FxANvSLN/3Lnzszd3sK1wHk3f9nT43EAEInMOEP57uPdYSceS5q0x8w==}

  '@opentiny/vue-cascader-view@3.21.0':
    resolution: {integrity: sha512-4x6yahySEQr0U7/tbL2xELF7vHDfrMDpkW+tiUuYGf6sU+qdANpWB3JFx2YHrFaGghPJAn/vRJPcfn3Wf13JNg==}

  '@opentiny/vue-cascader@3.21.0':
    resolution: {integrity: sha512-KOBH/Pr5J4difTvfKj84bD3ez/DRcthv/87uH+kEYEcpDihcVCetzNmfadSw83dKyyH8B5b4jrrjJpTAqH+FHg==}

  '@opentiny/vue-cell@3.21.0':
    resolution: {integrity: sha512-DiY2MiH1Er5GqF+d13fkD1kJeMrXoSv7ZJKeo6UeCEEawlSN3l6owTpUBGGkX9bz1S1IJJBvSPKEYAPN3uX8rw==}

  '@opentiny/vue-checkbox-button@3.21.0':
    resolution: {integrity: sha512-EkYSk+7hOKoRCnrw9+bI8gKYlpOJaCjo2cyhocy58AN8KnAABR+lv7Nz3AWdPkaDWCrOzY7uU3DthwQ558EQuw==}

  '@opentiny/vue-checkbox-group@3.21.0':
    resolution: {integrity: sha512-qe9OqWR64cXdeYkB+zuTtP4HYu0pyngbsiss7wwAcnRIEPdNo741ejrg1qmLmbyLT+hku+OF9J7T7omrAbbGpQ==}

  '@opentiny/vue-checkbox@3.21.0':
    resolution: {integrity: sha512-wGi26BQTO3PyKUPYMQAZkcK4Ae5N4x+iyfc2dYkaHrc7la09v9v6qvQKh6ZAT/IsET+q8Qj8uUd7zL/F43j6DQ==}

  '@opentiny/vue-col@3.21.0':
    resolution: {integrity: sha512-3+sOOnqaGv4WOkHgyTCKdlIVexm/H0gGBrbWPJmOYJv0xWihbUhop/b6GkpPB8T5/GMYB+Wtd6xRp8vcasRjdw==}

  '@opentiny/vue-collapse-item@3.21.0':
    resolution: {integrity: sha512-nA81d8OK0VsDES7S9UcBcUreLvndxjHyiuvYqRZDaEU6rdCmkezietyv6ACZPZVrtVqs1ZxZ0mgOGLyp2W3jtA==}

  '@opentiny/vue-collapse-transition@3.21.0':
    resolution: {integrity: sha512-iOMNRBS65e9uUkLByx/KPJwOlwuvb1o3Phtg++YCY9Pqe/qR3mQobrhnMjN53SawlyjTOUQzcb2LWNfD6yFS4A==}

  '@opentiny/vue-collapse@3.21.0':
    resolution: {integrity: sha512-lQLNkXDpiG9mQsiSm37mGJqF6WF35G18UTseksEcwskrOFXiG+7x4JBXeTz6549hsFo2t/5tx0AaavvHRXWBdA==}

  '@opentiny/vue-color-picker@3.21.0':
    resolution: {integrity: sha512-CT6WfiAfeQJ31Wf/r2h1uzGZVlazLb+GO9ioV9sm6wfh7gvuEK2HjvUjeWyQnKFLw6j6FAdERGJK/Nb+7F7/FQ==}

  '@opentiny/vue-color-select-panel@3.21.0':
    resolution: {integrity: sha512-zJ6a/McUXPYlgla+qaGP2KxGfljjm6j02j7JU/e6uK14C8r0zQlT2Ig3CxYU2Obn1XrsIwJQRwU15k2gl40Yxg==}

  '@opentiny/vue-column-list-group@3.21.0':
    resolution: {integrity: sha512-PSziFeAvuAvZANmq9s6Mg6gne1F25U/T+yhORJm87OWsNOhGhqHySZqoYGN4xAm+woUpNEXV9OQa37NnFT1fSw==}

  '@opentiny/vue-column-list-item@3.21.0':
    resolution: {integrity: sha512-MGTFdS4KsMoeBNYT14q1+NtR4j8NKO0Vw7ly4irGxAwDzls1AynyyDK5dU9CUOIqdPERgT8U32k3uRRIByEJMQ==}

  '@opentiny/vue-common@3.21.0':
    resolution: {integrity: sha512-9k86BwnLbvcYyl/u1qN5TWnLH7L3LYM9WVC4HmbCxWWMTZWisCbW92o9Klx98P8mDLUPVq2mTksc+dBQtQxquw==}

  '@opentiny/vue-company@3.21.0':
    resolution: {integrity: sha512-N6zT8CDd33oug/q+O5iRkoEHFDx7PobVMnlIoe6/MTzQ6CN4DCNm3DymMKVW4ZJMr8pd2tmH4AM8419K1HmLTQ==}

  '@opentiny/vue-config-provider@3.21.0':
    resolution: {integrity: sha512-66QQR6Xn+ShyT0s3+jy5wYcSIwenFGhR8zGifY2vTolxMqvtgYSJ3BeTp+RuaoBB/9goBPHNds5PXoO++20OvA==}

  '@opentiny/vue-container@3.21.0':
    resolution: {integrity: sha512-uCUXSIeiLfxuaHC4B8XUvjsDpnCfFLDqQDTxVVKv81SkXhO+hSfdkTkKd+gStObMW2jlVKgmuFZGHAxdukX+Vg==}

  '@opentiny/vue-country@3.21.0':
    resolution: {integrity: sha512-HbmIOdxIS+NzAvHsa6wKczaaTgPjdwnW8WTjNanoLdplrjaF+3gCu7tSmSawNNG9ve6JglbEjW9Z2Wa4qpURtg==}

  '@opentiny/vue-crop@3.21.0':
    resolution: {integrity: sha512-OGpidXd/bYGhggyi+hZbtkPOdzQWadEXs0xdyt+B5K+S11hM9zjx8d8L1uU3wi/Q/od4wD0Kl285morrVH1eqw==}

  '@opentiny/vue-currency@3.21.0':
    resolution: {integrity: sha512-NgJGjyIXzz46snPzQIIfkqTatjmLKcqLp4T0nnw1gfkDL5pnmuMC9fC4vF1jHsPt2L0RZHVzmNqsjbVGcYCpXw==}

  '@opentiny/vue-date-panel@3.21.0':
    resolution: {integrity: sha512-hfZcJ2iWhRkDyFIbuKT8SGfo2HM5QtQhVGyV9hL9knbMZt8ZKqq6eYyJIucG1gcV9U0ZmZTx+DiqtcVwD/GmMw==}

  '@opentiny/vue-date-picker-mobile-first@3.21.0':
    resolution: {integrity: sha512-OtdRyfdv9YrO54TjHF5Xen141puiu+s4Jx1XYgpIreJNo/lZbxR1NuACY5kv28UpBuTx8GJ13ydKSxD8MEeCpQ==}

  '@opentiny/vue-date-picker@3.21.0':
    resolution: {integrity: sha512-m+7QuPULg6HJhCF/PeihJEe0wIH6y8XJ2DbBJ4yz0fDphW/Ig5KbcuQQ0AhFck6tosAqCVx4cG+FsQE0yKnaBQ==}

  '@opentiny/vue-date-range@3.21.0':
    resolution: {integrity: sha512-K+VgSWh0mGdyVEuQolyIiy6Wuw0gFMS79HtmeZdzN0hMvOEFuf+5VUP5OeQd+QDfmFsUVOr1QPoTXCOgw34p1g==}

  '@opentiny/vue-date-table@3.21.0':
    resolution: {integrity: sha512-yudyiiuhc57HUADBkX7eWDYykzuvuIP1vsK8a+1nkEoEKCuLdWD7AQVDYNpd47nhkndWx073nY1SLguCY0xHnw==}

  '@opentiny/vue-dept@3.21.0':
    resolution: {integrity: sha512-h/N9B3K7K2rksfXDy9CRdr5YwuoR3LfUdS1RiLhSFO4ypTi622lziU+DRcy8YIkuTAMJ2Ml7/b0rDDbxDHeM9A==}

  '@opentiny/vue-dialog-box@3.21.0':
    resolution: {integrity: sha512-/Q0NOr79wNWHjNdxO2F7oywVWybEuQPrTf4hqcj2P7/8GGufMH7MQOEkNeOzjA9s1jWeqOCTKYPTBu/Sr6kEAw==}

  '@opentiny/vue-dialog-select@3.21.0':
    resolution: {integrity: sha512-lDdeVhO8fkUuW3tkvb2l+sSpGlIruyE69jClALLCqNp9VoGPJmfapbErDqgtjKmCa7cR8hHJciZfcYnWHNp/mw==}

  '@opentiny/vue-directive@3.21.0':
    resolution: {integrity: sha512-u4I0vviKg0MRgHWVI+27a47moYbWlrW6LPTWsigXbiJEzodK+gH25pdmkB1h5PmXSqFYPG+sUx6AacGdjK98Gw==}

  '@opentiny/vue-divider@3.21.0':
    resolution: {integrity: sha512-J7iWKGtoaAw67mb8ldLjla/+2ThkkdkWefw7qm93HzjZgr+Q3ID5XLamSJEr2Ha64HbP7YG05jjAOXJ4n8wvNQ==}

  '@opentiny/vue-drawer@3.21.0':
    resolution: {integrity: sha512-AHhMN6E7hKsLPMXtUPhLMQwEQLjfp6qED9v+Q5fU/v/um4blId9s4K8RNAEKCQKPRlw+0sFyp4dp9C5Lh1e8NA==}

  '@opentiny/vue-drop-roles@3.21.0':
    resolution: {integrity: sha512-ZM35rkjTCuJz16d90zWVSdYHWh6GrQFlClEJIM+2HWOCFl8XwiJ/3fzJA2leTG/g0dpjfD0bK7kAarmcrjoNEw==}

  '@opentiny/vue-drop-times@3.21.0':
    resolution: {integrity: sha512-LtjwEQ49rqFETeIqyjIeHuZVtKP3tpifNkQxaxZ3sP7ndhTOrpoFjpGHUk1anXlHcTOYWRuU13uq6w44CQTGMQ==}

  '@opentiny/vue-dropdown-item@3.21.0':
    resolution: {integrity: sha512-msZwaooIufn/N4kO/i4ID6gmlYxkUy1oqhN7D+Y3CTJ+qfwm1qhCJIew1uXjcNB02OuMR1uSlWqbep+qqPNdtQ==}

  '@opentiny/vue-dropdown-menu@3.21.0':
    resolution: {integrity: sha512-xbxwB70kiSxRpmXU5aHqjYI6/hVOv4KWghESXepyO4Z9LMdCN1F2V72pyTBrGClldG/C6Qhidh9yArn7x2h6cA==}

  '@opentiny/vue-dropdown@3.21.1':
    resolution: {integrity: sha512-puwt60/1Qa0o/uPylm3pLetBCkQ3uNRoUGrOAChkTlCZ6j2QoARfMhGiW53ki9TF9jyvOa6IV7aBlVb66xDTVg==}

  '@opentiny/vue-dynamic-scroller-item@3.21.0':
    resolution: {integrity: sha512-BGV4Y9nW3Ty1wRm3FVz2IxE0YmoKJ6i1yjMl7UaLMqScLEp1xaTqPWAco+n21VDaVChB395t4yBgssIcyI7+qw==}

  '@opentiny/vue-dynamic-scroller@3.21.0':
    resolution: {integrity: sha512-GJULVVYWM1M02jidfivi5TmrOKgvo60emYkwA5IhTMRHZd51CS0fowcOMK63QSbvbHnlrErraldOsWHw6MHYFw==}

  '@opentiny/vue-espace@3.21.0':
    resolution: {integrity: sha512-KIVSD3aLF2ZR46f5iobujeL0E8qOr7YXAA8kRGwk4x4BQqw7yp1rz22tjRZCpbkBjbbMUjCy4zyiOwP+JLjUlw==}

  '@opentiny/vue-exception@3.21.0':
    resolution: {integrity: sha512-8YF3kJqatkZ7ONxUWHxTt0jh9TUEuU7LOVpYmo10fBpRByOL6Y6OokJCb0He5xEKW4vZT/l8BlnPXAvKxdOcQg==}

  '@opentiny/vue-fall-menu@3.21.0':
    resolution: {integrity: sha512-viBpFkquIeAeRkmeYbq8Kw0s9GhSuZRATbMY5jF7o/uyA6IqYFIwaqd4r55GCjd7uIL9pSDEqWMixAxtO47CRw==}

  '@opentiny/vue-file-upload@3.21.0':
    resolution: {integrity: sha512-QHD8WiYdkfAzodu2dpbBJ74CF2krlX1xrqRGyKqQ53Z6thE6BbLwf6NIzYCjdCXuCHFAs52BtFFHViOiffp/MQ==}

  '@opentiny/vue-filter-bar@3.21.0':
    resolution: {integrity: sha512-0mONikz5aZJ0tUvWsNyEclwOhEpTVH/NjcFrgjkPTckNB6laOXqIrM706TNrlqhd7fXKosi4DYWU2ds4YGUFuA==}

  '@opentiny/vue-filter-box@3.21.0':
    resolution: {integrity: sha512-0p+/zPjnzXtEegwNw3tq7JhqV80Gn2npDzKWrrdOK0vfQtgdpLlZ0NiAi3XzNHkinPaNp8QfiYYdPuPtut387g==}

  '@opentiny/vue-filter-panel@3.21.0':
    resolution: {integrity: sha512-VluX3ytI+M2MW29HHRrD5rtLLFNomqxftUwWe3fLCIfGpCtN+LI4SPcdXwwCo0uuMB7EAPQTOf4EBIW+go4UPA==}

  '@opentiny/vue-filter@3.21.0':
    resolution: {integrity: sha512-E14fIYHx4kUAMbY4oiHOueFyFd4IS81UwebBH9JA8u1orzoFesTFQQQOnn2/5K9ikCZIDJwobzXxBubZ6wmWYg==}

  '@opentiny/vue-float-button@3.21.0':
    resolution: {integrity: sha512-1rgaM6nphdNiCUURqWkrwiGAMcu89QntOb2nLHJUiD6x8UXdlegAAWMk5dkEnbTyfFIVzmjLrL0WHvTo3Ax3hA==}

  '@opentiny/vue-floatbar@3.21.0':
    resolution: {integrity: sha512-D+asXsnhmEytX+Y7Et/uPoswGQErpvDVX8AfwCZSF75tw2osQX4a94PQdrKy3hrX7/nSeAR7eihNhg41+KwX0g==}

  '@opentiny/vue-floating-button@3.21.0':
    resolution: {integrity: sha512-JS1kCMz/lW0RFLo9kdqiMitSDoAJXMRj6D+1haUXdkjqETddH58flbsNdFZKlsbSBo2S1RUzgBHhCZHsdorNaA==}

  '@opentiny/vue-flowchart@3.21.0':
    resolution: {integrity: sha512-R12Wdg4/pAiCN3Ql8uhQgQOIbgPT8wzdFmPIk/dOJjBG5DM2jBBwy42bmXR6sgsS6Sx+kJgIf9VTbHN8vcVbBw==}

  '@opentiny/vue-fluent-editor@3.21.0':
    resolution: {integrity: sha512-2J4i5WVQfIa2FSoA6NDLIh/RRtDz2dc9wGZolRaFDH15/nCw+HpzVF9QbS2Rt5kL9eIuOtlYCeLk3E2knrb4GQ==}

  '@opentiny/vue-form-item@3.21.0':
    resolution: {integrity: sha512-7yNFEepF8nKH+AWNIvdALej4lDXRtvfiePL1Wm5EBg8itQRKgwEGhCDsKG30duoJ+1nDqOW8t9CilSGzWgPQYQ==}

  '@opentiny/vue-form@3.21.0':
    resolution: {integrity: sha512-zp8I60Hw3/qJkj1Dj0xqWAnGOdbiwh4MbHsKL1o/3hXGRaaep4R3EiPm2TSgUoGp950SRXEsA/wEK3AWs8fdsQ==}

  '@opentiny/vue-fullscreen@3.21.0':
    resolution: {integrity: sha512-SBGq49tLgtmSqbcf1bbcWHkhoQNtB3wwoIZl/sGR99x7Nryh6XiK26nkDiwApcFlEx/KozMnGBH6bWuM+1dEsQ==}

  '@opentiny/vue-grid-column@3.21.0':
    resolution: {integrity: sha512-9dOsVqtGX15fOykn68t4gVtW56gLl4TAj9IhPZun7c/zhKsiPlz12f4/EJWvHkijaXxsfO7SzM+9DVQ2yoCIsw==}

  '@opentiny/vue-grid-manager@3.21.0':
    resolution: {integrity: sha512-4BIy5ZBiaxiZQbbozmWZpLCl5IpWZmqGZzuu9jtE8uolJoO6IAxdTbGaETiKetLizLC0lLw4bq4jguQcLBKLbA==}

  '@opentiny/vue-grid-select@3.21.0':
    resolution: {integrity: sha512-wuavIWoSVTAvkbTUQiwnyv41QBNbliVEU44Hqku6pwgm/WQp/MDR2vUJReuopXv81f6nLlVQEtbFJhnyOioOXA==}

  '@opentiny/vue-grid-toolbar@3.21.0':
    resolution: {integrity: sha512-oZjfOZE/sEHLCKDwqBV4O5fO1qFsOXGNlwdK5+GgMP81BZ4XK1jxzJ5/qWkUZY/9SQXsXJWFHyx3EBGy6H2Z7w==}

  '@opentiny/vue-grid@3.21.2':
    resolution: {integrity: sha512-Q/vXTMnfhhlLIKYYCmg7weZ5B8MQY28XHTrwMjVDzx0O8ZVuzjwwfyQBrWic6bwpyEhjEgnE10Eh0GG3LRD9jw==}

  '@opentiny/vue-guide@3.21.0':
    resolution: {integrity: sha512-gbaIGZlsXhbWXlbb0zvBmW3NhVrfJa0W/30CtypgVivdIIPv4cJfgb2U4e9S7nWHzqAu9VCnemyJ2W2ERy+KQw==}

  '@opentiny/vue-hrapprover@3.21.0':
    resolution: {integrity: sha512-cRZTuHld0SD/28F6Ek4dgzQ99aTd9rNgzpPNjNyR4S4LoPELfhyz1HbX+/ZLjt1YequvYUAUR86Qcrh6yX33EA==}

  '@opentiny/vue-huicharts-amap@3.21.0':
    resolution: {integrity: sha512-vD+cUfPRFcJSX+LxZ2ekHP/YyA8BIWRrjCH9WRzqcvibLK82C8Pm96jeKubl6l5FzZEHUGOmaMqGgqyzu4Razw==}

  '@opentiny/vue-huicharts-bar@3.21.0':
    resolution: {integrity: sha512-tRLKQbEXM0QscvfhlWJQuRNeTizdu/wr1SEPjgcOU218cNgX8+NMVbeUmLmRy+N6Uk8uz1KpkgKJKoUp8pX+Ag==}

  '@opentiny/vue-huicharts-bmap@3.21.0':
    resolution: {integrity: sha512-tSTzN8hNrvlW6QvN8i1kWUPrAwJN0l4yf4btWraP1l/os5CeJbri3dpHeZ6fqmCBAPELesXgPav5pYncPZjiNA==}

  '@opentiny/vue-huicharts-boxplot@3.21.0':
    resolution: {integrity: sha512-XbNlOZQlYrqAkkFFqNfgxYpqdzf7cJ28vdAq3ZLVFcxMz7ltK02Meuv/lb3qJn5fsdQM40Hj91sYFK6guFRwEw==}

  '@opentiny/vue-huicharts-candle@3.21.0':
    resolution: {integrity: sha512-ORZRdlXxaJ9C7McvBcvLYepCaFQlizy/5vMgJW3JlejDUWgJZD+jMloOV1a/BiPQyZG6VBb9Ubk+bsxz86ra9Q==}

  '@opentiny/vue-huicharts-core@3.21.0':
    resolution: {integrity: sha512-EyFGP7F+1PjMO/mUymcqto26in49MQXZplCKcukGbEzs7GtYHolUzJA4xpiQnWc9gz/a73UMdOOCWh+6WKBeiA==}

  '@opentiny/vue-huicharts-funnel@3.21.0':
    resolution: {integrity: sha512-yg+wq23TDrY7KFlkZP/NSZwKit8JNRIcvtHqZlf/l/TnTCho2P/F5ylG5AgNDY5D1OLtT1NBevAEwv4NkfzoPQ==}

  '@opentiny/vue-huicharts-gauge@3.21.0':
    resolution: {integrity: sha512-bQSdbh3CwMhhyu4obhnVeRxqq2lqQJgaGwuB5iWZTV3tmic62dIRfmtgMjW6HhjqmeMGLIf+aUgM/jM0oVIdqg==}

  '@opentiny/vue-huicharts-graph@3.21.0':
    resolution: {integrity: sha512-VJkesW4jpb2rPwuy6h/+GPH5ONE1qIuIEeKYXhRrOCuHO68Da6ir43RJA0CvKVzFRi2V/JAnr9BOsBWK1KiyjQ==}

  '@opentiny/vue-huicharts-heatmap@3.21.0':
    resolution: {integrity: sha512-a81aULGmffGpBEhsM0cxg+wkVirL0GFtz5ddmqi7hNwPYWXW5GJQ/Yfe0wyq4Pbz4UoWAM9d2irCsYAfeb0X5Q==}

  '@opentiny/vue-huicharts-histogram@3.21.0':
    resolution: {integrity: sha512-ABYRo7hezLJ0ILLIBT6e5E5h/L46aJAfeylKRvu95cHCWXoo4YKSEl3jXjrgm6+BV1bJWKzc5EEAJDU5086mSA==}

  '@opentiny/vue-huicharts-line@3.21.0':
    resolution: {integrity: sha512-4Iiuq4ZGbKF0e9SEN8AhDmiNAHD5FfYnjqLfCvOvxkKCdM6hR0rSn7SJOetc0DvhyFV7weZVPdld7focOF0z+g==}

  '@opentiny/vue-huicharts-liquidfill@3.21.0':
    resolution: {integrity: sha512-ViBp4xFU7I/VlZKHGEIZZ82u7X58cG+/m9PlbMlhKpWoJOn96DMC6nR2G6vtwg1IpU7mRXxoxajEz4VLtQmOrw==}

  '@opentiny/vue-huicharts-map@3.21.0':
    resolution: {integrity: sha512-FwIaXs1VS8HX5/JHnUgagG7c4O0g8peMKfC1vUvFk2XgpQFvrnX95qoUD6RVe/6KQ9x6kTdEhn9MqcWKI/vmmQ==}

  '@opentiny/vue-huicharts-pie@3.21.0':
    resolution: {integrity: sha512-rOFD1DqESDMmHLhY68lPkaHyZbc/S8OsMpyMhFU3ms+mbrVfO7NQmKlud9qkTkp5gGTD2pPKSR109jrZTx/iFg==}

  '@opentiny/vue-huicharts-process@3.21.0':
    resolution: {integrity: sha512-9VXPLjb/rn7Me7x7QpNBjOhKzrKtX2X8a5hFgVGXk0FaqGbdm9jAmO3TgZumLsEWbt3NvF45Cu8CZxqZJcXeyg==}

  '@opentiny/vue-huicharts-radar@3.21.0':
    resolution: {integrity: sha512-UgDtBnDJ5iua3n8I/s5fnYd4O8mc/nDpQmaCdXtkZ0cpVke7QLXFchzk/o8vyib6hmYW9S1HGRSQR7mZDIVcMg==}

  '@opentiny/vue-huicharts-ring@3.21.0':
    resolution: {integrity: sha512-s2PO2nSX9fb7iocvSwZR89nIbLrSvGX8Ns9mutz1PHOC5EkI+sIyM0BbdFDy4wC4e9Hquv5oJII7aR8hQb7PCg==}

  '@opentiny/vue-huicharts-sankey@3.21.0':
    resolution: {integrity: sha512-pTKINqbyjPN1ymgIBf74p5WxUc23PIGyjclVK6zgfpD9Ve/WnBZzdxPimuCsDpL+C+Rt+zSGjGD+kDlrPri5XA==}

  '@opentiny/vue-huicharts-scatter@3.21.0':
    resolution: {integrity: sha512-bnGgeQjkjVHtb/nRjd3HqUPsVjJfsIGbZU3NOwaRGFXl1lEdCifVSlL39ySPolMqgr1ne+34bmgikdYDp9LOGw==}

  '@opentiny/vue-huicharts-sunburst@3.21.0':
    resolution: {integrity: sha512-2i5sF6MaGejWYdmy+bT681A1Fj0YLKLAdH/USZBPf6sC4UU6sti3jND7NnKY4Jk/q9R+Lcw4iWRnucmTqo+trg==}

  '@opentiny/vue-huicharts-tree@3.21.0':
    resolution: {integrity: sha512-Qa2fUMMe9BXh7wFBbdGDBLQHN1vhIxG1S973k0ND7P9c2VToqUdMi+jMGBDMIfEdf19wQo9aiSTYBkkRcPXL+A==}

  '@opentiny/vue-huicharts-waterfall@3.21.0':
    resolution: {integrity: sha512-sqFCMLrfM4FHoQwY4UiawYTsfpSpFJtWNxQyJo78iD+qT+wnfGSoDK6/pSgsgcpgl8tjES4AyAaPqFVeCDuNVQ==}

  '@opentiny/vue-huicharts-wordcloud@3.21.0':
    resolution: {integrity: sha512-Vot+HXHtaRAfW0PGnaVSiqccFfGM9GB75RdIWkCrORDlWr2+xbmDV0M0OjARWfhpkNjPXsIf4/ewZRSCFV8KsQ==}

  '@opentiny/vue-huicharts@3.21.0':
    resolution: {integrity: sha512-OULf3kmXq7Ha5zCpsyJWBLNdXwFvpw51/uoyXMlLEf3WBVptcnWR1qAkdTNV9ndC0GTjSlI1LlNFkWPz0PX2Cg==}

  '@opentiny/vue-icon@3.21.0':
    resolution: {integrity: sha512-y1Rv++JWNp1Kd+kMS7BWFfaIc5G9zTxzsOb8pc6z3zYnaCezOSPnUVx+V3xZje08r8jkhT7w9NE3P7T452r/wg==}

  '@opentiny/vue-image-viewer@3.21.0':
    resolution: {integrity: sha512-yaJsds3tkLNMUgia3Q5xxcKUJiXnFVo8BXgrMLiZ9E21hRkFcGb1ZTv3F6WN6uJC/UOYWFEHx3bTrlE3WsS7vA==}

  '@opentiny/vue-image@3.21.0':
    resolution: {integrity: sha512-2d66ZSJ3V3QfAYBZ7m89bqSJCrDN30+4znrHoCia7tNv4XbZMX+mJktdrbOD4+dbj9G6vhhr+w8KePfnan+UAQ==}

  '@opentiny/vue-index-bar-anchor@3.21.0':
    resolution: {integrity: sha512-294YUzbIp9DAEsoxnGK29IEeQBB6/Na5QobYTVEA4vBF3u6UIXDxaeMMOl6omjBc0vnDbyTZm4bvrc0cmwA9oA==}

  '@opentiny/vue-index-bar@3.21.0':
    resolution: {integrity: sha512-lFt5b/fgnsgl/chr0KGJrnesKUSoCEKCUNrQi9IcA9VV9F47Cc1IDcg/nn5jsRzCJpqmRmtfgG1dfMsY9UrLoA==}

  '@opentiny/vue-input@3.21.0':
    resolution: {integrity: sha512-n9KyNakH/UeaoRYNgV1MT/5zlj9pur37hn/9chMIp8YEC/HonjNKwviADoSI/JQ9nrBIs+E5868zfaHV79XZWQ==}

  '@opentiny/vue-ip-address@3.21.0':
    resolution: {integrity: sha512-OPxz11kA4ZutqRxn1Gcuu6wo2j7ktrWwLStYPD27nF2NIlVGOmALXXayz5sT5cQRkJV0o1Kw4uWUnlpuaeJ/bw==}

  '@opentiny/vue-label@3.21.0':
    resolution: {integrity: sha512-xflnssqRgsuuIvh48iXa2nZSPB7phWPsB7giHYGjKhX7mia50lDzx7cWcXrYIJbS2TWsLRbtX9yNSXHQ/F2nQg==}

  '@opentiny/vue-layout@3.21.0':
    resolution: {integrity: sha512-BQRBV1PnLQ08pTGREojee2pULkiTKyrCEe9yq9GqFSOrUaOSjJv59+fQP1FiQhJy+iRVb2OjuvkZRBniEz86Aw==}

  '@opentiny/vue-link-menu@3.21.0':
    resolution: {integrity: sha512-uuRvgBli02XU41X/om53JHF8PpJOL2e6cyI4aHXhqdf9yxc3+df7yco9JxejnSwVopDtGZaLltsa+cJ+MJUkyg==}

  '@opentiny/vue-link@3.21.0':
    resolution: {integrity: sha512-M+yEFVGCuQWj/QM/oHfriWunV00errd/r1moXI+aJB2aBx8ka/QgRQ+Bnj8VEhVnhDBJP3AdLVn/s7X2bNaqkQ==}

  '@opentiny/vue-list@3.21.0':
    resolution: {integrity: sha512-B3kIIE+4HaC87a2DIqtUlek4kpwrfelda5FWn/uyJWn3a4d4/1QTmVCYIhGMoeMJRufVLiz4a4ZbrwpPPFb+vw==}

  '@opentiny/vue-load-list@3.21.0':
    resolution: {integrity: sha512-wjbJDzOrM3cgvSXOdXBzeE3277Tc/jB+Rdz/Gun16GslQpyEzjqj/roDvuN9Tw4HIExU1Gbg4Xz0NXbuXr28Uw==}

  '@opentiny/vue-loading@3.21.0':
    resolution: {integrity: sha512-eVpSjDNZAAnF5otilurYol7RzVYDva4LQvoWJgOoAA/wTew2Vs48rj79BCHBGvXmk6kiHRsik/QokC00kE45vQ==}

  '@opentiny/vue-locale@3.21.0':
    resolution: {integrity: sha512-L4v+ycHInXFZ/pUtXqUKFpYwjRv3ki42bm/LSFkvvvcrRMLvkN72C15a+uX7iAxvcbf2uxR6bjHMYNIXwFCxfw==}

  '@opentiny/vue-locales@3.21.0':
    resolution: {integrity: sha512-EyMRDmCATcM709GefYeOr4jxN6nHwpKK4w02oQ4vSuZdkMn4j9W9eztiHUuekWYlcaKu9coL4x5/AcEY0TfIGA==}

  '@opentiny/vue-logon-user@3.21.0':
    resolution: {integrity: sha512-GbzWSJ2lb41tVnUbC1GGSeLpeYb+Z/n3FoJZRiOVOvTQdEP/cuaF9IrKVRDjpimNu0DRIEqbHFdyHo7kTiyhgQ==}

  '@opentiny/vue-logout@3.21.0':
    resolution: {integrity: sha512-zxeaIJurilzJYveIl2XtiCyHyQiYU6OsfU+UzhMLPEUZQruAmy8VYIq0dHQrGKvmWrrWX+v7k+pYl1kJ5BA9sg==}

  '@opentiny/vue-mask@3.21.0':
    resolution: {integrity: sha512-W5mDHusmgcc1qqiRFN6yPTXjk65hKNeA2slQ34PwODLHxmYDCBIMiJ/SaVrSGnYN57Waw3Mzj6mHKcMrlOeQBg==}

  '@opentiny/vue-menu@3.21.0':
    resolution: {integrity: sha512-hkyr7hD0+4LpvYVn96hB0uQpgAWoWnfGq3p6LLyqZfD5VFwb/wTMo0nstnoZb6L6bKeywrrIXBIJRnW+xHIUCQ==}

  '@opentiny/vue-message@3.21.0':
    resolution: {integrity: sha512-gLAzxdUyvLIXnCaBKTd8KhiasjDkdU1ULjrOiM73SAloEiG34e5HyjuO5tPLF9kqr9eFUp4uhNNCEKoed23JXw==}

  '@opentiny/vue-milestone@3.21.0':
    resolution: {integrity: sha512-7ihdqmSzgYMKRsG6TqgIMWPYHGqC8QbIMmrjyaNiAbh91ieQwhI/TDaA2O7naw7973svqvvJvuv3hN+GBUaptQ==}

  '@opentiny/vue-mind-map@3.21.0':
    resolution: {integrity: sha512-kwwDeS0ua8sPktrEuMd/aAswberhbYrTOyHAELr7PvoVpcQeETGlZwyLJK982c1EVFhfZr7ZZqJWTKpdxerkzQ==}

  '@opentiny/vue-mini-picker@3.21.0':
    resolution: {integrity: sha512-vSaQyVNOZ3y+v54Hvz6rdk9gu6VLHtQ+AB4KKjQX0qh0bVzJ1+bEaX2eCy6sPBy3BjIT6ahFtqk3qmGtUAs7Ng==}

  '@opentiny/vue-modal@3.21.0':
    resolution: {integrity: sha512-0q4C/kHEzRIsJZtKe6iCkkJcWWpADLCfJ9fzrM7pOmu/9O1S9y30bJRRtAx+CiXBADrYyukcfm/dFpXuW41TCQ==}

  '@opentiny/vue-month-range@3.21.0':
    resolution: {integrity: sha512-FbZXmcKo+TaOexCGdfqbeM02beAfJFdjQRdmi3JEf4WZdonXpzCpB5XsnIyeNZXjTiJw6T+rCqluPPITD57j4g==}

  '@opentiny/vue-month-table@3.21.0':
    resolution: {integrity: sha512-Vj17/sfrMq4POjTJeyKO1z0Bw4FF+uXlYWqo0y4d3RqXecHUqArw/Z8ngDuTALfHIRIUZ1CbzPwQd/cGTkERyA==}

  '@opentiny/vue-multi-select-item@3.21.0':
    resolution: {integrity: sha512-1mjYTc7Q4ftJUfEmm1bqAe41DoaJbE+tldvrVMC8xZ7qNNyonpFEenmJ/GSLrjhMrTEFMF6Gw/p9ZmHKCv35GQ==}

  '@opentiny/vue-multi-select@3.21.0':
    resolution: {integrity: sha512-/JtOVhPNAuYCs4l0fvfvDEYWW6XZZftwRzx/k1WbaHxPJH16UKXUzT3wW4qRHS+0HW4Aw7iRLPbdYucbNu+m+w==}

  '@opentiny/vue-nav-bar@3.21.0':
    resolution: {integrity: sha512-omf5W9gdxN1VwmLT1PAx0tWS4PD2Bc4v83PKVnPkcZLvDsSXcHWghIXL+Szf1Y79ChUKxp+Vgni/Netbd/eAZg==}

  '@opentiny/vue-nav-menu@3.21.0':
    resolution: {integrity: sha512-30ZxJ+ububvjOGKPSuZe2bK9sIdu4tNsNxkz5gZTdWmuLnBe3KS24WZNW9Fe1eI9+rs7uHjwQZsADK1S/fFOGQ==}

  '@opentiny/vue-notify@3.21.0':
    resolution: {integrity: sha512-WmbDMbtT9SDUNTR9m5VCUJkQ7ByVxZkSk/bcP5eLVZnswY41BNVOk5MYDuxceC1xs/ptNN/g3C3nCVHcB2R10A==}

  '@opentiny/vue-numeric@3.21.0':
    resolution: {integrity: sha512-2REZqrl9joqhk3wVin/BCQzlcnITrzLHVOocyu9A9773g6TTMU0IfxRaJioMFXerZK5lnYmKkmncHsp1+LwnpA==}

  '@opentiny/vue-option-group@3.21.0':
    resolution: {integrity: sha512-YWHgEvPyCp2BSNy24GdtnydIBo7OgF+2X0IyTgzxqlChED64vZGA1ZitTuRBjlZn578355d499U2uVQw+9ucbw==}

  '@opentiny/vue-option@3.21.0':
    resolution: {integrity: sha512-PpQLvkog/nm5zvfoQxGq2NUa7AiMFGZPYCkNDqDeh+sCmexhHkHbuhVYIun7Q0N4RyQNqGLti7xMNwMFvgBDvw==}

  '@opentiny/vue-pager-item@3.21.0':
    resolution: {integrity: sha512-Hdpp3Q09qHqJGd5Q2pMAjN+xhoxzxmRAvKQx9fSF/x784T+ydh7rRPqm0qRzuxtzGd0f5Z3pMlMQUPd6MFUwIQ==}

  '@opentiny/vue-pager@3.21.0':
    resolution: {integrity: sha512-qeOSm5Je8v68GNwkXVIDuUfYeEE71GIBLXlQcHP9fG7BBEbbwYOccGXKlS+YJUfIMJv1KzmGXdJ7+zG8Sg/B4Q==}

  '@opentiny/vue-panel@3.21.0':
    resolution: {integrity: sha512-7ouzvbPXU7TxaBGrTXIk4bd9UGEDdxDX54fSZc+xwe/7ytT4HKQf6E24Gqnm3A8d7AcWB0N4EFiKhVr3eJrxWA==}

  '@opentiny/vue-picker-column@3.21.0':
    resolution: {integrity: sha512-d9YWf5a6m39OMImisc68SCdNxt0rwRBpyZuZQnJe2PNLYKHt0tFBQ92swyuXyG6W6cTebqdsnpqftkoEABwXbQ==}

  '@opentiny/vue-picker@3.21.0':
    resolution: {integrity: sha512-a+NlYljIBHF+NESTqJWLphX9D4x/mgW1YyU6E4+YrwbAUHmVlAx95OUBBldbbztToyhZfJiW6rKZu3qzB1N0kw==}

  '@opentiny/vue-pop-upload@3.21.0':
    resolution: {integrity: sha512-IYyNoB8H2fBm86qJ+TajSQbhTYXMok6UJ0+MClhBVAT1GTr3kaKKgisuefT8KWk3LsgCD9+Pn2kVgFprWSQ4Aw==}

  '@opentiny/vue-popconfirm@3.21.0':
    resolution: {integrity: sha512-Mlf/luwPqkr64xrdPA9+xIQ64UllAYplma4TPoHulIpN9XMCi6Zx65lD/BVPuGdr6JkBS3fvmjTqwffftXjClA==}

  '@opentiny/vue-popeditor@3.21.0':
    resolution: {integrity: sha512-dseube1WwX5hL9ODSK5pi4RrSMSe4TphjtNy0O8xHNxDHAn9dkfbrtPvGp5Z0kD8hVXlms8cfHJYXGE1cEaqDw==}

  '@opentiny/vue-popover@3.21.0':
    resolution: {integrity: sha512-0aJ/1WUzLJbwwMbIbMU+wbUz31QkOHHAYFpn68wIW8LFo6TxNa2xTGPv8oukvs2WgkR3hkYznlMKmbgFpPvnTA==}

  '@opentiny/vue-popup@3.21.0':
    resolution: {integrity: sha512-qM47yyNIED0G5Zn9H9hSRA+g8H4EE3JoBgbsUr5rV4IHidiIhmmYOTnQd7ZLd/Uoeg3cKjpTbv0jaefH5FsY3Q==}

  '@opentiny/vue-progress@3.21.0':
    resolution: {integrity: sha512-0PlSGQYjKBfe64NOONHJozhTya35X/Uctzmbh/r2l8bNIOul4n/D4kRi/+h0I7o9ocHAeugb7Te1rWPwAsVqyw==}

  '@opentiny/vue-pull-refresh@3.21.0':
    resolution: {integrity: sha512-Ablc1FE+RhqPwIKJja9wAfQRmQu035c/8+ZFflfuPkxeyH7C9xG9MwWMjTmjRFE70CLL53h34oseH7esb1Xe/A==}

  '@opentiny/vue-qr-code@3.21.0':
    resolution: {integrity: sha512-gESmFUccJaTatC1lzsxOvm4u7E713XDgdRYxIbjCJ+e2XemYDypnvSRcUDZngvX8CUQkQHoQr65v+YfXOq5Jsg==}

  '@opentiny/vue-quarter-panel@3.21.0':
    resolution: {integrity: sha512-Bwbb5m7XhDMu6BvC88pFhCmI6U5pomd+HYhbeLoqKt/t65ccSwtCKWiaKnzFyGnZduafdqXjmknNKT9VfeSW/A==}

  '@opentiny/vue-query-builder@3.21.0':
    resolution: {integrity: sha512-vk5ekgeS2NtzwdZs0eQBaMYmCRbeIWOvVjr2J8EvZOxI8BRB8mzKIWnJkNwUGU+JRZ0FsKcCm07uiMw0eriIzQ==}

  '@opentiny/vue-radio-button@3.21.0':
    resolution: {integrity: sha512-pDvyes187LIiPD8lb3iIXfSM7y5af4QScnDdjjA23Ak8qhsoSGySNjwF6xq945Jebv3Tc+NQfGFmRDJj4dissw==}

  '@opentiny/vue-radio-group@3.21.0':
    resolution: {integrity: sha512-V9lRhKR1K2ZUrSRqxPe6im/OmnNJrkqIoRFBe2axmpd+urm0dxlrgauIa6n4YTv3srhTJ8H6B3xzfJogaUQjrA==}

  '@opentiny/vue-radio@3.21.0':
    resolution: {integrity: sha512-sy2YuJjIi6VxH+rNQrrm7ZPATmZsWZux16YGGZ6hlU1kOoLne0RKlyUvlQOXW6ozwIUI1sUHrENq0skjzElrqw==}

  '@opentiny/vue-rate@3.21.0':
    resolution: {integrity: sha512-Oc9hFSMq7gpm7a4xNmzxW7tXgI1OkaA81YwRWS/Mb5mbanfxN7iGT9ey3dAcjVMTKkdW4pmE8TgulUCP3qG0kg==}

  '@opentiny/vue-record@3.21.0':
    resolution: {integrity: sha512-mKUhHjaY2h915ld/ZxBp2XaXk6p1+/9CfJydss5wyVM+3rJWiBiHFiiLQJtH9wA/6BIaM1QNZGqrtuXPvrS2Yw==}

  '@opentiny/vue-recycle-scroller@3.21.0':
    resolution: {integrity: sha512-nQgdtb6lVZ4j5uQV7TSUcHZM4Yz4QJjvyMt/0x+PK3iVxTjQR87ovtjOFY8pC7Q4Q5Cv2CWLuwULwERtWW8Nmw==}

  '@opentiny/vue-renderless@3.21.1':
    resolution: {integrity: sha512-nJlgEF+lmo7LwkX1IReUnEVR3VCQgwdIi7BQV9/pPAr1B00OkNFEWoRJbLxtYQ1jCDQ2FM0dE2mKOcvf5V4czA==}

  '@opentiny/vue-river@3.21.0':
    resolution: {integrity: sha512-5aRG5pkrp5fnv5mg0LNWvfKYZVnahXhpVc/WICT+dKe+mV47yYFRammSmII0PF/ghOerBeUHNqQ+09enehIkLA==}

  '@opentiny/vue-roles@3.21.0':
    resolution: {integrity: sha512-i5IVnCTXvKcC6cKpEnzkpleDS0ryyKNpBEf+yF1O6xC7JXMbaKZzUe8BPmVFk8SJSK7y5UHDbR+j71QgeOeNWA==}

  '@opentiny/vue-row@3.21.0':
    resolution: {integrity: sha512-iWGcs32RUaadjY3SXHKH9vN+UlmhH1UYFv/kboSCY8pwRJTXH4Md7CnNsdT1uxdRL4hdmY/zx8kAUx5i9GNqZw==}

  '@opentiny/vue-scroll-text@3.21.0':
    resolution: {integrity: sha512-4LJ+9KOSXF3R5H4nll8Qxw+iDFYmuofzUkgt+ZzDHhSvqKzqkrMYnOZLTNDA8BOkF0rceOhAl1R9MKrircN4eA==}

  '@opentiny/vue-scrollbar@3.21.0':
    resolution: {integrity: sha512-sgCBcotb6O6TibiLPrf45OLz0zHdXpNh0tNRxu8i/VNkxfEyqbBh00U8cFhGC8yrfse/Hj3eVOAcGrGeA08e+g==}

  '@opentiny/vue-search@3.21.0':
    resolution: {integrity: sha512-2ZUVXOeVoyyhnFDsUhHnfvrewLks59s81vEeG7zWsR/NBkUMUk9rki03qROKHqWU5cx2xM+O76Z/vkwRy8E4cQ==}

  '@opentiny/vue-select-dropdown@3.21.0':
    resolution: {integrity: sha512-85ybdnDtnZkuIuv1R6GB22N++YgXqpe6zUHGl5gzKAVaCY8lYgBRs0LHBx6zGr1sYnm8ntL0pnt1FEwbibS22A==}

  '@opentiny/vue-select-mobile@3.21.0':
    resolution: {integrity: sha512-4KHsg6fqTb/eMg0zuklKrftDqiNM4DwzjeMFoXR0jN2VzXz9wkAIP47H/MHCg45P3CDytnDlfmDab0g1nYzqDA==}

  '@opentiny/vue-select-view@3.21.0':
    resolution: {integrity: sha512-TET25oT9koqK98XIUKoT53t86HMrQgSp7QUaMxJ4Rq0ND/IyFE9ewtr8K9hbabAz95R2Snoc1stCb2y3OTnQ0w==}

  '@opentiny/vue-select@3.21.0':
    resolution: {integrity: sha512-t5f2ceDUpIx0ntDLooyazb16ERlz9alvlwzhh8cOiwaXQpMCBgnDIGRkOMkHpWJXvMp+mbSEHHIMV0we1XGYFQ==}

  '@opentiny/vue-selected-box@3.21.0':
    resolution: {integrity: sha512-gxioltUdBX3dk5Rjb1cq94lN0ohlQY7aOXd5ptbP9m8WDHW5zfDVrJfMqbKK+yjJlHJU/TMrwdqv0UfuQAL9yA==}

  '@opentiny/vue-signature@3.21.0':
    resolution: {integrity: sha512-ohIlYz8+7tWKZQdzDLwCb966l4QRJMENQqotoLg8m7oZNkQaS4UK10DDtFxiU+GsWfC6g2iUNkLxZFJMcq0DzQ==}

  '@opentiny/vue-skeleton-item@3.21.0':
    resolution: {integrity: sha512-UTds2avxuY1X9jsaSM0ZInQEkMBpXhObJNzI8x7Lj/qqiUqBPWJZcWitK2/cnF90ySh42f432/jGJnzfBpH84A==}

  '@opentiny/vue-skeleton@3.21.0':
    resolution: {integrity: sha512-vU4V/5F7KSqSbA1UjASH2GKahY4GW3LOI/yxb/fjn0d3rpA/g0qkTHZcccfvkvfYOAObVwv13AqiAY/u7XxWjQ==}

  '@opentiny/vue-slider-button-group@3.21.0':
    resolution: {integrity: sha512-8RI+k3pfwUbIONxQwypYG76QgQrmzXvFTYDKqTGD/CxUeNnb8+crbi7OlSFJ48oh/0293tRFYkJwIqLUWqXiYA==}

  '@opentiny/vue-slider-button@3.21.0':
    resolution: {integrity: sha512-i5cwGLup3ktqWe76Os9EEkztVYzAyOq73Gpz9PsAi81bpk950OvncO32+7J3Mvr64qBRN+cjFnlzixiJiYgN/Q==}

  '@opentiny/vue-slider@3.21.0':
    resolution: {integrity: sha512-DFtfUigjqrGguePF2g+HRciRYQFkMASaY37Sdg3hfa8YNgXGVb+4OYsQS9uVVnfvkHjyVq3D90HuiiGmbNIDdA==}

  '@opentiny/vue-split@3.21.0':
    resolution: {integrity: sha512-iT7NCXLad/m4Qn+0poJdW4zG5xl3Wlbe5ThZ0SdqsKphD41VSLRkU56SfAs0+wFMOdW8A0pjvcIlJE8beF5Iaw==}

  '@opentiny/vue-standard-list-item@3.21.0':
    resolution: {integrity: sha512-f+KNKzvxwGGW1hAjCBuTpWjMBAqZnBUyvrQtoNNXWns5NPdww4cIdoSxJIPsYEihGS/ZWhYHoGHCTQyKPlPYOw==}

  '@opentiny/vue-statistic@3.21.0':
    resolution: {integrity: sha512-xudztfkVHMKAjoNtuqpugsjQsDP4fEWJxpGM9Jb3NOtLE0pXYqmRg0ItwqSlcgnoUYy597rFNLPsMOFFXRJmdw==}

  '@opentiny/vue-steps@3.21.0':
    resolution: {integrity: sha512-tlo7Q3Zn8Ny9OGgeAdg14klO3dp7sVNEhRk3/u71sKVZir0mKwbQgH/FH3YohrnV1w3e8EH2yqKG4v9AL6xd1Q==}

  '@opentiny/vue-sticky@3.21.0':
    resolution: {integrity: sha512-OLWRwenDaQE69Fs8diIQWIMjHKPuAMa5ZARquHwfXhAMNLUQBtz/rIem/EOruer8iGJIY66vydOKhash4qGVRg==}

  '@opentiny/vue-switch@3.21.0':
    resolution: {integrity: sha512-i7ow74fE6wbJd/nrmHrcDBBy4CKDmaLoNtkO7WEAi+wa7tyM+K7RrGNd3y+tYmoImwPjugMGX2DUNeAS3t0Iuw==}

  '@opentiny/vue-tab-item@3.21.0':
    resolution: {integrity: sha512-ltzbnweBCD0YF0vhqdw22MewpIXW0REL8lQxeUgDROVvrmiD5Q3Mq9Bv30LLq+z+ru/Jc1okyH6Wy3pI87VJqw==}

  '@opentiny/vue-tabbar-item@3.21.0':
    resolution: {integrity: sha512-ekukaSaaRKdEWfeROzl3uq0DsB8fXJF6lBJKpprb+jtUlz8ZfseZ2B4a7czO4Wkk70YzvFsZH0RRsfjH9cwRuA==}

  '@opentiny/vue-tabbar@3.21.0':
    resolution: {integrity: sha512-97JyE1lCVvlaYEnubEo99Nv96LJgdqQ+yBawBeiUBXC7SJ8Lnr3NuO9SlvImx/2ct9QMDgMYnRVkDWD3xcKM0A==}

  '@opentiny/vue-table@3.21.0':
    resolution: {integrity: sha512-TaUmG47D8XAVsx4t9jZj9Sgaq+HFR3UisohjZRQt6SY2PmR7TWy8DgOU2pHRiBltMiswx5PhxKArMNBq63DerQ==}

  '@opentiny/vue-tabs@3.21.0':
    resolution: {integrity: sha512-Y0oZFVsl9uid3KTONSHHnmU9BkCB1mmDwWFmHTwu6+l7HNSHliK0gL3DtIwypSRR9SYZd4OwYLYzBG8kq1JhDw==}

  '@opentiny/vue-tag-group@3.21.0':
    resolution: {integrity: sha512-wXHjkjkmqfUfvt+qAZXWMu7ZtVuqrb+yJP1rD++VYRj1wnJz8GexrFgoZZZwFZVMyOwdSgu7bqR2z9TZMp+7Ug==}

  '@opentiny/vue-tag@3.21.0':
    resolution: {integrity: sha512-DMxzMw1Co+CxZRX9/95Gks0DKmGjNgyeMBYiU1367iW7ibknkmzjw2sjMRf9fcpNTsEI3PWFfhy3jvYU74xJpA==}

  '@opentiny/vue-text-popup@3.21.0':
    resolution: {integrity: sha512-dFXOnodfTTGMgWx2/bzgVru3S+fWVcFUKqUXIDX7iHjDt6ckEbBE6+RV3ls0D2I9cNLJqFKv202SaANqs/0gtQ==}

  '@opentiny/vue-theme-mobile@3.21.0':
    resolution: {integrity: sha512-Zsy31Usl5eOBeD/SeSZqjqfNd8nOanBeFvyGDKfF0HSA0rbhKJCGBiDmBm2l0XTxyPIqke1kNG/b5XR8J1cT4g==}

  '@opentiny/vue-theme@3.21.2':
    resolution: {integrity: sha512-T6gbaMAy6iBs8IKrOXaSZ8rqdb9fxRSSQtNEi1wAMXyOICyndSrHM3xtMCMVYQ6yV09vWRGcDhC05oxNC+kROg==}

  '@opentiny/vue-time-line-new@3.21.0':
    resolution: {integrity: sha512-90LPzWRFd79yip5F6HJ1zocXgd3E7f2OJ5HcQ+My66mGHqTV9XJQzsOkM4h/Y75e034kptikVDlusa0aLdruHA==}

  '@opentiny/vue-time-line@3.21.0':
    resolution: {integrity: sha512-xYp+NFJvSnD2dQEGDYGiSNUWxgtRt+Gz1B5EchSdDEpltI4fRr0PvmUaoxC1PTGVxuISTlTsegsxiX8q+mVE0A==}

  '@opentiny/vue-time-panel@3.21.0':
    resolution: {integrity: sha512-stZ2cp1rvMpK4NNT6R0aT0mjmCnC1wW6Iof97/XPnfVovy8urTnXeKa5YmUyv7mNGNYn/fKlLyxRkWQu4GxNAA==}

  '@opentiny/vue-time-picker-mobile@3.21.0':
    resolution: {integrity: sha512-1U3I60CU8ARG0SeVU1dlivtUyDZ1EI8a/8E1+8RkJE2Dz0zpTcHilFL/fROwZ0Mrw2DlPrsIu/8HNKTErUIBfA==}

  '@opentiny/vue-time-picker@3.21.0':
    resolution: {integrity: sha512-tsMfKLqIkvKyE8BIw7UHLQsXLCD+0tHj/eYfasx9G7axA4LvHiC3aMNf1fBJHb270JJGNa5/tHAehdvigKEJbg==}

  '@opentiny/vue-time-range@3.21.0':
    resolution: {integrity: sha512-GN/cF+aCrsbGi7VE6hwaXupA1qKImIWzKEbVB3ZbYOJSOzJxa3L1kpRYNB/iSQee+9l0Z4BPHy9hwkTe6ESSeg==}

  '@opentiny/vue-time-select@3.21.0':
    resolution: {integrity: sha512-PapdHGZNS03E2UoySrEWzwvO2jnfJeV9lcy7uhIIMy9zYt+NhF0gP2N1GjVZQ+eFioc75OS4Wwl07DS4VqK7Uw==}

  '@opentiny/vue-time-spinner@3.21.0':
    resolution: {integrity: sha512-f7A6H32IDV1ScAxVsiAaCkhEloTTGuRuMpAn375HQ+uqkxKfRziLeHLE+pCmzyt1a35BXbhg6EOP3OAxIPGitA==}

  '@opentiny/vue-time@3.21.0':
    resolution: {integrity: sha512-vQtAw31SOQkUY3NudkWSzQOH97sIpFu812pQIC9Tek7URuNIdPcNe/OldXSzTV6/wvs3MIlRxFVe/rwLupUzIw==}

  '@opentiny/vue-timeline-item@3.21.0':
    resolution: {integrity: sha512-sWK/Yf+FToxQGY0wc5cViGj9QaVyfhGXzEIqO8GUEqTQO7KBRN/Sfqk72ddUyPJ8NlcOfmdxbTUVuVbIN/Xggg==}

  '@opentiny/vue-toast@3.21.0':
    resolution: {integrity: sha512-Ui4m+oulvPVdXj9ELDfIh4t1tql1A2w8k4F3ZnlKyi1hj3QTSOHb6egfkVIK0ZM56J/mjMBTbFWVKYJkaJsSOA==}

  '@opentiny/vue-toggle-menu@3.21.0':
    resolution: {integrity: sha512-xwTdN2963I+k5kyhsO4CHFuluAGuacfK31oKqFtdLXjlIufgnm39et6kKn8piu+omw6QeefQAZkFzedftC6z+A==}

  '@opentiny/vue-tooltip@3.21.0':
    resolution: {integrity: sha512-lJ1t23xQI+vfY4aod5wmfFLKt1HrKxNd6f5/nIH4UlkY7Pvx9Q2GOzs79qoC2oTiUp95WOk/2Z5vteG+fpnrRA==}

  '@opentiny/vue-top-box@3.21.0':
    resolution: {integrity: sha512-QFroQ0N+6pCBb4g3zb1eVgSV2/ziqRW1MENThxtexALhjU3a0CmuHuE46CypPmRLaUrLdal0WPS9y82dnyGcHA==}

  '@opentiny/vue-transfer-panel@3.21.0':
    resolution: {integrity: sha512-5qn4OVpE6+avTYdk2CRkT7Ec4f9ih2fyJf/aVqPPbftLsCvQNycxoS4ir/ISZKOcN3HQifyglDD5IPN9cudjUA==}

  '@opentiny/vue-transfer@3.21.0':
    resolution: {integrity: sha512-OW6EJR3fDl5EDTlwqXfLJpbD5k6ncSx2/S2vSwZ3TK8/hN9XMrATAYk9p07b9OcS9WywPSKgbfCJp3/S6id+Sw==}

  '@opentiny/vue-tree-menu@3.21.0':
    resolution: {integrity: sha512-FTE3Sheb9Lym/Th8fw7IWN4PeKFIoUbqFrG4jL2g/ar6cxhQmG3xCorxj4v73uBFh8DzNM4CHtgVh7YZncYuSQ==}

  '@opentiny/vue-tree-select@3.21.0':
    resolution: {integrity: sha512-fkrVOhetctzgYK+2QZIDlAQ7Ff73SdOWobRB5g6owq+oQMbJluWn+fN6tKv7Bf/rHHfQRxGY2Erdrih2K7TeXA==}

  '@opentiny/vue-tree@3.21.0':
    resolution: {integrity: sha512-o/w0a6C24Nkb9AGL4vfUWVDHe8x8OCEXSoJWbTX+Qd2uLIOzfogI+S5du7ZfReRJ3teZ1h013T9YKmAaZvBu1Q==}

  '@opentiny/vue-upload-dragger@3.21.0':
    resolution: {integrity: sha512-y0X3tNPoo/x2lmcO/lR416T9zqkjlia2qZFUaBOx/BJGM2QWVZR96+QartFRsnW0EQ88pPufaiNdtzH+CYUd7g==}

  '@opentiny/vue-upload-list@3.21.0':
    resolution: {integrity: sha512-ByWGwEd9GRadQDePYWJyNA27kauh/LJtLHNq+g4rmsXDpBAjzbU8NDfeRpGEVcRwiJKD8SRlgKF88rCrY1888g==}

  '@opentiny/vue-upload@3.21.0':
    resolution: {integrity: sha512-1QBDV87LqHLbc7sthNtNOVnxFNm2Uv175g0D0lyA0YOhGF+mt2IuZixq+VQEvQ5OPIO5NjbOfjM4ud82dKVYPg==}

  '@opentiny/vue-user-account@3.21.0':
    resolution: {integrity: sha512-IVi6fgPbCenwTwUFV4tyiAT1WF7pQuT8TaI+ras5YM3a0mcNtl5sChwpMXiqjQCS5AvJv/9G+tldrgar+4Vc9Q==}

  '@opentiny/vue-user-contact@3.21.0':
    resolution: {integrity: sha512-N3Dw15cCBefx6mJ7V7OgOXPfma8lZ3q7HBijJTA2GfHGQl4Ed5I+5WvZ+sdekW+/TrGYyGmMtOqwdkRTLFHVSg==}

  '@opentiny/vue-user-head-group@3.21.0':
    resolution: {integrity: sha512-qkjAHSisW8mulN5F+Q/FXA/K0ULQTulKqKgq1nkbVv5gFlvUiU/1MEQMyZsaVPiVpxiVKkD2jfZc7PdfCXp08g==}

  '@opentiny/vue-user-head@3.21.0':
    resolution: {integrity: sha512-L2tP3O4Eqy6CniEwzAgvj5GmsKC9A7B+/16UXwh2a9EOEDhRBdp5csi9Cq5kgIj2SafDf7zHQ5/lZYU3R8avDw==}

  '@opentiny/vue-user-link@3.21.0':
    resolution: {integrity: sha512-Jr4wX5e+/UNtAjQp0X2LyUjngrPIG7Oz/3iNrHKi/jmZDONrtjsbv5Dneh+86U463eJt7XQEOxzb/vsRLyZ3gA==}

  '@opentiny/vue-user@3.21.0':
    resolution: {integrity: sha512-pcCIhtcPDmBN4B4EE7Xhj9RuKyk0CHJm4F53WXjYG3md/995HKip8qpUhS33fjzyHhb+mFlvxZQQIftvaXSKKQ==}

  '@opentiny/vue-virtual-scroll-box@3.21.0':
    resolution: {integrity: sha512-jnpnVs7BGhtni9cW/4WtQHGKQGG7hkTD689z9dZamz0xVAFcZDMUvyL/hHJyPXYnyMdnvIHSN85oK/7UU71m3g==}

  '@opentiny/vue-virtual-tree@3.21.0':
    resolution: {integrity: sha512-yzrqz88rLe17TOTkDNcnNlwIVMmtOVSdqqvYz/v4lDcCiPJL9YFhpfO205O5HrAEGzuRbASZtwn4/tqrfKbwMg==}

  '@opentiny/vue-watermark@3.21.0':
    resolution: {integrity: sha512-cULNruRVcuj1m1PdAlh7onKNrMTi5JKdBL4uZMw5rfBcJhYtnCbfq5sKdAGVNmcHdKEKur03Zsl02VjhDPglrQ==}

  '@opentiny/vue-wheel@3.21.0':
    resolution: {integrity: sha512-R66in8ZnhUT7PUblV9ilyKivqao483zrXI6gd9Ng3NjZ+1cB+NvOwT3wxST/fiixPT644W3tuB5dG7eQz+Y8ow==}

  '@opentiny/vue-wizard@3.21.0':
    resolution: {integrity: sha512-qWh4qmk4aXVrcxxP6BKJ6I3a9LKC/a7xOLOV+tzFJ7TlRxjcPU5mqA1sgoOUHo/BR6ZOh83cPVqo3JvCGtkRMA==}

  '@opentiny/vue-year-range@3.21.0':
    resolution: {integrity: sha512-cNonDWsPdk7ZDeOe/ljfjBrsLCNT5jAllbzeQUkS0fL/GGNmPR5sTZPnFEqg+lYyxC4saYsED1O6ZJWjiNfoDw==}

  '@opentiny/vue-year-table@3.21.0':
    resolution: {integrity: sha512-B9GShF8Izk66RmB6Da4/dWk5aErJe11EOoHbl1Lr8SApCAAb8xDWMhHv99v3JjwWIjaTWMNO204NJJPWf/Ivuw==}

  '@opentiny/vue@3.21.2':
    resolution: {integrity: sha512-WKi0M1/LUo/ZMGVT45N8IihWzetGmMiyckWuEmFxwyt9KEk3mGXZjn91GLzZElnFXkbe0Zp+nApMEX1EM1CRBQ==}

  '@parcel/watcher-android-arm64@2.5.1':
    resolution: {integrity: sha512-KF8+j9nNbUN8vzOFDpRMsaKBHZ/mcjEjMToVMJOhTozkDonQFFrRcfdLWn6yWKCmJKmdVxSgHiYvTCef4/qcBA==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [android]

  '@parcel/watcher-darwin-arm64@2.5.1':
    resolution: {integrity: sha512-eAzPv5osDmZyBhou8PoF4i6RQXAfeKL9tjb3QzYuccXFMQU0ruIc/POh30ePnaOyD1UXdlKguHBmsTs53tVoPw==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [darwin]

  '@parcel/watcher-darwin-x64@2.5.1':
    resolution: {integrity: sha512-1ZXDthrnNmwv10A0/3AJNZ9JGlzrF82i3gNQcWOzd7nJ8aj+ILyW1MTxVk35Db0u91oD5Nlk9MBiujMlwmeXZg==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [darwin]

  '@parcel/watcher-freebsd-x64@2.5.1':
    resolution: {integrity: sha512-SI4eljM7Flp9yPuKi8W0ird8TI/JK6CSxju3NojVI6BjHsTyK7zxA9urjVjEKJ5MBYC+bLmMcbAWlZ+rFkLpJQ==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [freebsd]

  '@parcel/watcher-linux-arm-glibc@2.5.1':
    resolution: {integrity: sha512-RCdZlEyTs8geyBkkcnPWvtXLY44BCeZKmGYRtSgtwwnHR4dxfHRG3gR99XdMEdQ7KeiDdasJwwvNSF5jKtDwdA==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm]
    os: [linux]
    libc: [glibc]

  '@parcel/watcher-linux-arm-musl@2.5.1':
    resolution: {integrity: sha512-6E+m/Mm1t1yhB8X412stiKFG3XykmgdIOqhjWj+VL8oHkKABfu/gjFj8DvLrYVHSBNC+/u5PeNrujiSQ1zwd1Q==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm]
    os: [linux]
    libc: [musl]

  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    resolution: {integrity: sha512-LrGp+f02yU3BN9A+DGuY3v3bmnFUggAITBGriZHUREfNEzZh/GO06FF5u2kx8x+GBEUYfyTGamol4j3m9ANe8w==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@parcel/watcher-linux-arm64-musl@2.5.1':
    resolution: {integrity: sha512-cFOjABi92pMYRXS7AcQv9/M1YuKRw8SZniCDw0ssQb/noPkRzA+HBDkwmyOJYp5wXcsTrhxO0zq1U11cK9jsFg==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@parcel/watcher-linux-x64-glibc@2.5.1':
    resolution: {integrity: sha512-GcESn8NZySmfwlTsIur+49yDqSny2IhPeZfXunQi48DMugKeZ7uy1FX83pO0X22sHntJ4Ub+9k34XQCX+oHt2A==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@parcel/watcher-linux-x64-musl@2.5.1':
    resolution: {integrity: sha512-n0E2EQbatQ3bXhcH2D1XIAANAcTZkQICBPVaxMeaCVBtOpBZpWJuf7LwyWPSBDITb7In8mqQgJ7gH8CILCURXg==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@parcel/watcher-win32-arm64@2.5.1':
    resolution: {integrity: sha512-RFzklRvmc3PkjKjry3hLF9wD7ppR4AKcWNzH7kXR7GUe0Igb3Nz8fyPwtZCSquGrhU5HhUNDr/mKBqj7tqA2Vw==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [win32]

  '@parcel/watcher-win32-ia32@2.5.1':
    resolution: {integrity: sha512-c2KkcVN+NJmuA7CGlaGD1qJh1cLfDnQsHjE89E60vUEMlqduHGCdCLJCID5geFVM0dOtA3ZiIO8BoEQmzQVfpQ==}
    engines: {node: '>= 10.0.0'}
    cpu: [ia32]
    os: [win32]

  '@parcel/watcher-win32-x64@2.5.1':
    resolution: {integrity: sha512-9lHBdJITeNR++EvSQVUcaZoWupyHfXe1jZvGZ06O/5MflPcuPLtEphScIBL+AiCWBO46tDSHzWyD0uDmmZqsgA==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [win32]

  '@parcel/watcher@2.5.1':
    resolution: {integrity: sha512-dfUnCxiN9H4ap84DvD2ubjw+3vUNpstxa0TneY/Paat8a3R4uQZDLSvWjmznAY/DoahqTHl9V46HF/Zs3F29pg==}
    engines: {node: '>= 10.0.0'}

  '@rollup/pluginutils@5.1.4':
    resolution: {integrity: sha512-USm05zrsFxYLPdWWq+K3STlWiT/3ELn3RcV5hJMghpeAIhxfsUIg6mt12CBJBInWMV4VneoV7SfGv8xIwo2qNQ==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  '@rollup/rollup-android-arm-eabi@4.34.6':
    resolution: {integrity: sha512-+GcCXtOQoWuC7hhX1P00LqjjIiS/iOouHXhMdiDSnq/1DGTox4SpUvO52Xm+div6+106r+TcvOeo/cxvyEyTgg==}
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.34.6':
    resolution: {integrity: sha512-E8+2qCIjciYUnCa1AiVF1BkRgqIGW9KzJeesQqVfyRITGQN+dFuoivO0hnro1DjT74wXLRZ7QF8MIbz+luGaJA==}
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.34.6':
    resolution: {integrity: sha512-z9Ib+OzqN3DZEjX7PDQMHEhtF+t6Mi2z/ueChQPLS/qUMKY7Ybn5A2ggFoKRNRh1q1T03YTQfBTQCJZiepESAg==}
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.34.6':
    resolution: {integrity: sha512-PShKVY4u0FDAR7jskyFIYVyHEPCPnIQY8s5OcXkdU8mz3Y7eXDJPdyM/ZWjkYdR2m0izD9HHWA8sGcXn+Qrsyg==}
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-freebsd-arm64@4.34.6':
    resolution: {integrity: sha512-YSwyOqlDAdKqs0iKuqvRHLN4SrD2TiswfoLfvYXseKbL47ht1grQpq46MSiQAx6rQEN8o8URtpXARCpqabqxGQ==}
    cpu: [arm64]
    os: [freebsd]

  '@rollup/rollup-freebsd-x64@4.34.6':
    resolution: {integrity: sha512-HEP4CgPAY1RxXwwL5sPFv6BBM3tVeLnshF03HMhJYCNc6kvSqBgTMmsEjb72RkZBAWIqiPUyF1JpEBv5XT9wKQ==}
    cpu: [x64]
    os: [freebsd]

  '@rollup/rollup-linux-arm-gnueabihf@4.34.6':
    resolution: {integrity: sha512-88fSzjC5xeH9S2Vg3rPgXJULkHcLYMkh8faix8DX4h4TIAL65ekwuQMA/g2CXq8W+NJC43V6fUpYZNjaX3+IIg==}
    cpu: [arm]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm-musleabihf@4.34.6':
    resolution: {integrity: sha512-wM4ztnutBqYFyvNeR7Av+reWI/enK9tDOTKNF+6Kk2Q96k9bwhDDOlnCUNRPvromlVXo04riSliMBs/Z7RteEg==}
    cpu: [arm]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-arm64-gnu@4.34.6':
    resolution: {integrity: sha512-9RyprECbRa9zEjXLtvvshhw4CMrRa3K+0wcp3KME0zmBe1ILmvcVHnypZ/aIDXpRyfhSYSuN4EPdCCj5Du8FIA==}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm64-musl@4.34.6':
    resolution: {integrity: sha512-qTmklhCTyaJSB05S+iSovfo++EwnIEZxHkzv5dep4qoszUMX5Ca4WM4zAVUMbfdviLgCSQOu5oU8YoGk1s6M9Q==}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-loongarch64-gnu@4.34.6':
    resolution: {integrity: sha512-4Qmkaps9yqmpjY5pvpkfOerYgKNUGzQpFxV6rnS7c/JfYbDSU0y6WpbbredB5cCpLFGJEqYX40WUmxMkwhWCjw==}
    cpu: [loong64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-powerpc64le-gnu@4.34.6':
    resolution: {integrity: sha512-Zsrtux3PuaxuBTX/zHdLaFmcofWGzaWW1scwLU3ZbW/X+hSsFbz9wDIp6XvnT7pzYRl9MezWqEqKy7ssmDEnuQ==}
    cpu: [ppc64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-riscv64-gnu@4.34.6':
    resolution: {integrity: sha512-aK+Zp+CRM55iPrlyKiU3/zyhgzWBxLVrw2mwiQSYJRobCURb781+XstzvA8Gkjg/hbdQFuDw44aUOxVQFycrAg==}
    cpu: [riscv64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-s390x-gnu@4.34.6':
    resolution: {integrity: sha512-WoKLVrY9ogmaYPXwTH326+ErlCIgMmsoRSx6bO+l68YgJnlOXhygDYSZe/qbUJCSiCiZAQ+tKm88NcWuUXqOzw==}
    cpu: [s390x]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-gnu@4.34.6':
    resolution: {integrity: sha512-Sht4aFvmA4ToHd2vFzwMFaQCiYm2lDFho5rPcvPBT5pCdC+GwHG6CMch4GQfmWTQ1SwRKS0dhDYb54khSrjDWw==}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-musl@4.34.6':
    resolution: {integrity: sha512-zmmpOQh8vXc2QITsnCiODCDGXFC8LMi64+/oPpPx5qz3pqv0s6x46ps4xoycfUiVZps5PFn1gksZzo4RGTKT+A==}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-win32-arm64-msvc@4.34.6':
    resolution: {integrity: sha512-3/q1qUsO/tLqGBaD4uXsB6coVGB3usxw3qyeVb59aArCgedSF66MPdgRStUd7vbZOsko/CgVaY5fo2vkvPLWiA==}
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.34.6':
    resolution: {integrity: sha512-oLHxuyywc6efdKVTxvc0135zPrRdtYVjtVD5GUm55I3ODxhU/PwkQFD97z16Xzxa1Fz0AEe4W/2hzRtd+IfpOA==}
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.34.6':
    resolution: {integrity: sha512-0PVwmgzZ8+TZ9oGBmdZoQVXflbvuwzN/HRclujpl4N/q3i+y0lqLw8n1bXA8ru3sApDjlmONaNAuYr38y1Kr9w==}
    cpu: [x64]
    os: [win32]

  '@trysound/sax@0.2.0':
    resolution: {integrity: sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA==}
    engines: {node: '>=10.13.0'}

  '@types/echarts@4.9.22':
    resolution: {integrity: sha512-7Fo6XdWpoi8jxkwP7BARUOM7riq8bMhmsCtSG8gzUcJmFhLo387tihoBYS/y5j7jl3PENT5RxeWZdN9RiwO7HQ==}

  '@types/estree@1.0.6':
    resolution: {integrity: sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==}

  '@types/gensync@1.0.4':
    resolution: {integrity: sha512-C3YYeRQWp2fmq9OryX+FoDy8nXS6scQ7dPptD8LnFDAUNcKWJjXQKDNJD3HVm+kOUsXhTOkpi69vI4EuAr95bA==}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}

  '@types/node@22.13.1':
    resolution: {integrity: sha512-jK8uzQlrvXqEU91UxiK5J7pKHyzgnI1Qnl0QDHIgVGuolJhRb9EEl28Cj9b3rGR8B2lhFCtvIm5os8lFnO/1Ew==}

  '@types/vue@2.0.0':
    resolution: {integrity: sha512-WDElkBv/o4lVwu6wYHB06AXs4Xo2fwDjJUpvPRc1QQdzkUSiGFjrYuSCy8raxLE5FObgKq8ND7R5gSZTFLK60w==}
    deprecated: This is a stub types definition for vuejs (https://github.com/vuejs/vue). vuejs provides its own type definitions, so you don't need @types/vue installed!

  '@types/zrender@4.0.6':
    resolution: {integrity: sha512-1jZ9bJn2BsfmYFPBHtl5o3uV+ILejAtGrDcYSpT4qaVKEI/0YY+arw3XHU04Ebd8Nca3SQ7uNcLaqiL+tTFVMg==}

  '@typescript-eslint/eslint-plugin@4.33.0':
    resolution: {integrity: sha512-aINiAxGVdOl1eJyVjaWn/YcVAq4Gi/Yo35qHGCnqbWVz61g39D0h23veY/MA0rFFGfxK7TySg2uwDeNv+JgVpg==}
    engines: {node: ^10.12.0 || >=12.0.0}
    peerDependencies:
      '@typescript-eslint/parser': ^4.0.0
      eslint: ^5.0.0 || ^6.0.0 || ^7.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/experimental-utils@4.33.0':
    resolution: {integrity: sha512-zeQjOoES5JFjTnAhI5QY7ZviczMzDptls15GFsI6jyUOq0kOf9+WonkhtlIhh0RgHRnqj5gdNxW5j1EvAyYg6Q==}
    engines: {node: ^10.12.0 || >=12.0.0}
    peerDependencies:
      eslint: '*'

  '@typescript-eslint/parser@4.33.0':
    resolution: {integrity: sha512-ZohdsbXadjGBSK0/r+d87X0SBmKzOq4/S5nzK6SBgJspFo9/CUDJ7hjayuze+JK7CZQLDMroqytp7pOcFKTxZA==}
    engines: {node: ^10.12.0 || >=12.0.0}
    peerDependencies:
      eslint: ^5.0.0 || ^6.0.0 || ^7.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/scope-manager@4.33.0':
    resolution: {integrity: sha512-5IfJHpgTsTZuONKbODctL4kKuQje/bzBRkwHE8UOZ4f89Zeddg+EGZs8PD8NcN4LdM3ygHWYB3ukPAYjvl/qbQ==}
    engines: {node: ^8.10.0 || ^10.13.0 || >=11.10.1}

  '@typescript-eslint/types@4.33.0':
    resolution: {integrity: sha512-zKp7CjQzLQImXEpLt2BUw1tvOMPfNoTAfb8l51evhYbOEEzdWyQNmHWWGPR6hwKJDAi+1VXSBmnhL9kyVTTOuQ==}
    engines: {node: ^8.10.0 || ^10.13.0 || >=11.10.1}

  '@typescript-eslint/typescript-estree@4.33.0':
    resolution: {integrity: sha512-rkWRY1MPFzjwnEVHsxGemDzqqddw2QbTJlICPD9p9I9LfsO8fdmfQPOX3uKfUaGRDFJbfrtm/sXhVXN4E+bzCA==}
    engines: {node: ^10.12.0 || >=12.0.0}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/visitor-keys@4.33.0':
    resolution: {integrity: sha512-uqi/2aSz9g2ftcHWf8uLPJA70rUv6yuMW5Bohw+bwcuzaxQIHaKFZCKGoGXIrc9vkTJ3+0txM73K0Hq3d5wgIg==}
    engines: {node: ^8.10.0 || ^10.13.0 || >=11.10.1}

  '@varlet/axle@0.7.1':
    resolution: {integrity: sha512-9WSIagplJPNJBBsurUYWGyNthX+wkRsoKQCEbe1fnCx904O6hq/5ymPIS6X6T4VTYsBsnGAAt05///RxO2R3TQ==}
    peerDependencies:
      vue: ^3.2.0

  '@varlet/shared@2.20.3':
    resolution: {integrity: sha512-MgLDRYfrGgKqu8Vg2kfRVRGRZE2d8CH0f75vph5h4AG+Clm+A7FgUZLaVTriMWuS6wXrrrceSgX5kNmb/lFShQ==}

  '@vexip-ui/hooks@2.7.0':
    resolution: {integrity: sha512-Ec/0U8COaFautr4GdjjnM2KYjxKB3CEcTay7c1EPicm6pWQLV1hFexNf8LIa8j8vmE/xN86zSPTtGchdAl+3KA==}
    peerDependencies:
      vue: ^3.2.25

  '@vexip-ui/utils@2.16.1':
    resolution: {integrity: sha512-P0xHHu7j1fXS+DLkVp6ipJNAM2QonymVsBUQai1Ym7/JPyZgHMeEOCMzAfJWwv/qLDfRk5XXJXDjRQDz/qlF7A==}

  '@vitejs/plugin-vue-jsx@4.1.1':
    resolution: {integrity: sha512-uMJqv/7u1zz/9NbWAD3XdjaY20tKTf17XVfQ9zq4wY1BjsB/PjpJPMe2xiG39QpP4ZdhYNhm4Hvo66uJrykNLA==}
    engines: {node: ^18.0.0 || >=20.0.0}
    peerDependencies:
      vite: ^5.0.0 || ^6.0.0
      vue: ^3.0.0

  '@vitejs/plugin-vue@5.2.1':
    resolution: {integrity: sha512-cxh314tzaWwOLqVes2gnnCtvBDcM1UMdn+iFR+UjAn411dPT3tOmqrJjbMd7koZpMAmBM/GqeV4n9ge7JSiJJQ==}
    engines: {node: ^18.0.0 || >=20.0.0}
    peerDependencies:
      vite: ^5.0.0 || ^6.0.0
      vue: ^3.2.25

  '@volar/language-core@2.4.11':
    resolution: {integrity: sha512-lN2C1+ByfW9/JRPpqScuZt/4OrUUse57GLI6TbLgTIqBVemdl1wNcZ1qYGEo2+Gw8coYLgCy7SuKqn6IrQcQgg==}

  '@volar/source-map@2.4.11':
    resolution: {integrity: sha512-ZQpmafIGvaZMn/8iuvCFGrW3smeqkq/IIh9F1SdSx9aUl0J4Iurzd6/FhmjNO5g2ejF3rT45dKskgXWiofqlZQ==}

  '@volar/typescript@2.4.11':
    resolution: {integrity: sha512-2DT+Tdh88Spp5PyPbqhyoYavYCPDsqbHLFwcUI9K1NlY1YgUJvujGdrqUp0zWxnW7KWNTr3xSpMuv2WnaTKDAw==}

  '@vue/babel-helper-vue-transform-on@1.2.5':
    resolution: {integrity: sha512-lOz4t39ZdmU4DJAa2hwPYmKc8EsuGa2U0L9KaZaOJUt0UwQNjNA3AZTq6uEivhOKhhG1Wvy96SvYBoFmCg3uuw==}

  '@vue/babel-plugin-jsx@1.2.5':
    resolution: {integrity: sha512-zTrNmOd4939H9KsRIGmmzn3q2zvv1mjxkYZHgqHZgDrXz5B1Q3WyGEjO2f+JrmKghvl1JIRcvo63LgM1kH5zFg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    peerDependenciesMeta:
      '@babel/core':
        optional: true

  '@vue/babel-plugin-resolve-type@1.2.5':
    resolution: {integrity: sha512-U/ibkQrf5sx0XXRnUZD1mo5F7PkpKyTbfXM3a3rC4YnUz6crHEz9Jg09jzzL6QYlXNto/9CePdOg/c87O4Nlfg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@vue/compiler-core@3.5.13':
    resolution: {integrity: sha512-oOdAkwqUfW1WqpwSYJce06wvt6HljgY3fGeM9NcVA1HaYOij3mZG9Rkysn0OHuyUAGMbEbARIpsG+LPVlBJ5/Q==}

  '@vue/compiler-dom@3.5.13':
    resolution: {integrity: sha512-ZOJ46sMOKUjO3e94wPdCzQ6P1Lx/vhp2RSvfaab88Ajexs0AHeV0uasYhi99WPaogmBlRHNRuly8xV75cNTMDA==}

  '@vue/compiler-sfc@3.5.13':
    resolution: {integrity: sha512-6VdaljMpD82w6c2749Zhf5T9u5uLBWKnVue6XWxprDobftnletJ8+oel7sexFfM3qIxNmVE7LSFGTpv6obNyaQ==}

  '@vue/compiler-ssr@3.5.13':
    resolution: {integrity: sha512-wMH6vrYHxQl/IybKJagqbquvxpWCuVYpoUJfCqFZwa/JY1GdATAQ+TgVtgrwwMZ0D07QhA99rs/EAAWfvG6KpA==}

  '@vue/compiler-vue2@2.7.16':
    resolution: {integrity: sha512-qYC3Psj9S/mfu9uVi5WvNZIzq+xnXMhOwbTFKKDD7b1lhpnn71jXSFdTQ+WsIEk0ONCd7VV2IMm7ONl6tbQ86A==}

  '@vue/devtools-api@6.6.4':
    resolution: {integrity: sha512-sGhTPMuXqZ1rVOk32RylztWkfXTRhuS7vgAKv0zjqk8gbsHkJ7xfFf+jbySxt7tWObEJwyKaHMikV/WGDiQm8g==}

  '@vue/language-core@2.2.0':
    resolution: {integrity: sha512-O1ZZFaaBGkKbsRfnVH1ifOK1/1BUkyK+3SQsfnh6PmMmD4qJcTU8godCeA96jjDRTL6zgnK7YzCHfaUlH2r0Mw==}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@vue/reactivity@3.5.13':
    resolution: {integrity: sha512-NaCwtw8o48B9I6L1zl2p41OHo/2Z4wqYGGIK1Khu5T7yxrn+ATOixn/Udn2m+6kZKB/J7cuT9DbWWhRxqixACg==}

  '@vue/runtime-core@3.5.13':
    resolution: {integrity: sha512-Fj4YRQ3Az0WTZw1sFe+QDb0aXCerigEpw418pw1HBUKFtnQHWzwojaukAs2X/c9DQz4MQ4bsXTGlcpGxU/RCIw==}

  '@vue/runtime-dom@3.5.13':
    resolution: {integrity: sha512-dLaj94s93NYLqjLiyFzVs9X6dWhTdAlEAciC3Moq7gzAc13VJUdCnjjRurNM6uTLFATRHexHCTu/Xp3eW6yoog==}

  '@vue/server-renderer@3.5.13':
    resolution: {integrity: sha512-wAi4IRJV/2SAW3htkTlB+dHeRmpTiVIK1OGLWV1yeStVSebSQQOwGwIq0D3ZIoBj2C2qpgz5+vX9iEBkTdk5YA==}
    peerDependencies:
      vue: 3.5.13

  '@vue/shared@3.5.13':
    resolution: {integrity: sha512-/hnE/qP5ZoGpol0a5mDi45bOd7t3tjYJBjsgCsivow7D48cJeV5l05RD82lPqi7gRiphZM37rnhW1l6ZoCNNnQ==}

  '@vxe-ui/core@4.0.29':
    resolution: {integrity: sha512-F6ZJ7+x48qBDx7ME/GdmRN5OECha9wnNy0IIA8AAWHrjhTFY5yBBE+t2ULO6wgwkFB2E74CtYF/2/nprpGrdlA==}
    peerDependencies:
      vue: ^3.2.0

  '@vxe-ui/plugin-export-xlsx@4.0.13':
    resolution: {integrity: sha512-cE6ugWo221WAmDWgaMAwQ3eQgRKrhE8RlsJBK9VeDK9iewPkkJdnLrV0BPsGCasvGeNNjT9+zTPy75hYfjfIJQ==}

  '@vxe-ui/plugin-menu@4.0.7':
    resolution: {integrity: sha512-jJq2F+/7pVAU1Zw9VuwonHm4doaW5iCn+jH6MGSf4f30x4/gAsIL4Fc+H+F2sJ0n3DDgkWhdzxpLVy/o2R6rxg==}

  '@vxe-ui/plugin-render-wangeditor@4.0.3':
    resolution: {integrity: sha512-tokxxiDlTDLpWSZ+LpaGosYCVC/0PCJtxGdw14Fl4KkFztyGI+mmfy8SEm+sevKTRWpDzsi90kQbLbEYXSUgiw==}

  acorn-jsx@5.3.2:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@7.4.1:
    resolution: {integrity: sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  acorn@8.14.0:
    resolution: {integrity: sha512-cl669nCJTZBsL97OF4kUQm5g5hC2uihk0NxY3WENAC0TYdILVkAyHymAntgxGkl7K+t0cXIrH5siy5S4XkFycA==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}

  ajv@8.17.1:
    resolution: {integrity: sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==}

  alien-signals@0.4.14:
    resolution: {integrity: sha512-itUAVzhczTmP2U5yX67xVpsbbOiquusbWVyA9N+sy6+r6YVbFkahXvNCeEPWEOMhwDYwbVbGHFkVL03N9I5g+Q==}

  ansi-colors@4.1.3:
    resolution: {integrity: sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw==}
    engines: {node: '>=6'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-styles@3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}

  argparse@1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}

  array-union@2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==}
    engines: {node: '>=8'}

  astral-regex@2.0.0:
    resolution: {integrity: sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==}
    engines: {node: '>=8'}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  axios@1.7.9:
    resolution: {integrity: sha512-LhLcE7Hbiryz8oMDdDptSrWowmB4Bl6RCt6sIJKpRB4XtVf0iEgewX3au/pJqm+Py1kCASkb/FFKjxQaLtxJvw==}

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  binary-extensions@2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}

  boolbase@1.0.0:
    resolution: {integrity: sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==}

  brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}

  brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}

  braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}

  browserslist@4.24.4:
    resolution: {integrity: sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  call-bind-apply-helpers@1.0.1:
    resolution: {integrity: sha512-BhYE+WDaywFg2TBWYNXAE+8B1ATnThNBqXHP5nQu0jWJdVvY2hvkpyB3qOmtmDePiS5/BDQ8wASEWGMWRG148g==}
    engines: {node: '>= 0.4'}

  call-bound@1.0.3:
    resolution: {integrity: sha512-YTd+6wGlNlPxSuri7Y6X8tY2dmm12UMH66RpKMhiX6rsk5wXXnYgbUcOt8kiS31/AjfoTOvCsE+w8nZQLQnzHA==}
    engines: {node: '>= 0.4'}

  callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  camelcase@5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}

  caniuse-lite@1.0.30001699:
    resolution: {integrity: sha512-b+uH5BakXZ9Do9iK+CkDmctUSEqZl+SP056vc5usa0PL+ev5OHw003rZXcnjNDv3L8P5j6rwT6C0BPKSikW08w==}

  chalk@2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  chokidar@3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}

  chokidar@4.0.3:
    resolution: {integrity: sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==}
    engines: {node: '>= 14.16.0'}

  cliui@6.0.0:
    resolution: {integrity: sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ==}

  color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  color-string@1.9.1:
    resolution: {integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==}

  color@4.2.3:
    resolution: {integrity: sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==}
    engines: {node: '>=12.5.0'}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  commander@7.2.0:
    resolution: {integrity: sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==}
    engines: {node: '>= 10'}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  confbox@0.1.8:
    resolution: {integrity: sha512-RMtmw0iFkeR4YV+fUOSucriAQNb9g8zFR52MWCtl+cCZOFRNL6zeB395vPzFhEjjn4fMxXudmELnl/KF/WrK6w==}

  convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}

  copy-anything@2.0.6:
    resolution: {integrity: sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==}

  cropperjs@1.5.7:
    resolution: {integrity: sha512-sGj+G/ofKh+f6A4BtXLJwtcKJgMUsXYVUubfTo9grERiDGXncttefmue/fyQFvn8wfdyoD1KhDRYLfjkJFl0yw==}

  cross-spawn@7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==}
    engines: {node: '>= 8'}

  crypto-js@4.2.0:
    resolution: {integrity: sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==}

  css-select@5.1.0:
    resolution: {integrity: sha512-nwoRF1rvRRnnCqqY7updORDsuqKzqYJ28+oSMaJMMgOauh3fvwHqMS7EZpIPqK8GL+g9mKxF1vP/ZjSeNjEVHg==}

  css-tree@2.2.1:
    resolution: {integrity: sha512-OA0mILzGc1kCOCSJerOeqDxDQ4HOh+G8NbOJFOTgOCzpw7fCBubk0fEyxp8AgOL/jvLgYA/uV0cMbe43ElF1JA==}
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0, npm: '>=7.0.0'}

  css-tree@2.3.1:
    resolution: {integrity: sha512-6Fv1DV/TYw//QF5IzQdqsNDjx/wc8TrMBZsqjL9eW01tWb7R7k/mq+/VXfJCl7SoD5emsJop9cOByJZfs8hYIw==}
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0}

  css-what@6.1.0:
    resolution: {integrity: sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==}
    engines: {node: '>= 6'}

  cssfilter@0.0.10:
    resolution: {integrity: sha512-FAaLDaplstoRsDR8XGYH51znUN0UY7nMc6Z9/fvE8EXGwvJE9hu7W2vHwx1+bd6gCYnln9nLbzxFTrcO9YQDZw==}

  csso@5.0.5:
    resolution: {integrity: sha512-0LrrStPOdJj+SPCCrGhzryycLjwcgUSHBtxNA8aIDxf0GLsRh1cKYhB00Gd1lDOS4yGH69+SNn13+TWbVHETFQ==}
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0, npm: '>=7.0.0'}

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  dayjs@1.11.13:
    resolution: {integrity: sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==}

  de-indent@1.0.2:
    resolution: {integrity: sha512-e/1zu3xH5MQryN2zdVaF0OrdNLUbvWxzMbi+iNA6Bky7l1RoP8a2fIbRocyHclXt/arDrrR6lL3TqFD9pMQTsg==}

  debug@4.4.0:
    resolution: {integrity: sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decamelize@1.2.0:
    resolution: {integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==}
    engines: {node: '>=0.10.0'}

  deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}

  deepmerge@4.3.1:
    resolution: {integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==}
    engines: {node: '>=0.10.0'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  detect-libc@1.0.3:
    resolution: {integrity: sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==}
    engines: {node: '>=0.10'}
    hasBin: true

  dijkstrajs@1.0.3:
    resolution: {integrity: sha512-qiSlmBq9+BCdCA/L46dw8Uy93mloxsPSbwnm5yrKn2vMPiy8KyAskTF6zuV/j5BMsmOGZDPs7KjU+mjb670kfA==}

  dir-glob@3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}

  doctrine@3.0.0:
    resolution: {integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==}
    engines: {node: '>=6.0.0'}

  dom-serializer@2.0.0:
    resolution: {integrity: sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==}

  dom-zindex@1.0.6:
    resolution: {integrity: sha512-FKWIhiU96bi3xpP9ewRMgANsoVmMUBnMnmpCT6dPMZOunVYJQmJhSRruoI0XSPoHeIif3kyEuiHbFrOJwEJaEA==}

  domelementtype@2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==}

  domhandler@5.0.3:
    resolution: {integrity: sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==}
    engines: {node: '>= 4'}

  domutils@3.2.2:
    resolution: {integrity: sha512-6kZKyUajlDuqlHKVX1w7gyslj9MPIXzIFiz/rGu35uC1wMi+kMhQwGhl4lt9unC9Vb9INnY9Z3/ZA3+FhASLaw==}

  dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}

  dx-ui-var@0.0.1-beta.4:
    resolution: {integrity: sha512-3MtTazJxlp03oNhoXtHpON5NzVoIK1TWifPCi5CwXosXHemFHXMCyijMXIuYX43Cxsl0Z3V6wCkv1woDshI1ug==}

  echarts-liquidfill@3.1.0:
    resolution: {integrity: sha512-5Dlqs/jTsdTUAsd+K5LPLLTgrbbNORUSBQyk8PSy1Mg2zgHDWm83FmvA4s0ooNepCJojFYRITTQ4GU1UUSKYLw==}
    peerDependencies:
      echarts: ^5.0.1

  echarts-wordcloud@2.0.0:
    resolution: {integrity: sha512-K7l6pTklqdW7ZWzT/1CS0KhBSINr/cd7c5N1fVMzZMwLQHEwT7x+nivK7g5hkVh7WNcAv4Dn6/ZS5zMKRozC1g==}
    peerDependencies:
      echarts: ^5.0.1

  echarts@5.4.1:
    resolution: {integrity: sha512-9ltS3M2JB0w2EhcYjCdmtrJ+6haZcW6acBolMGIuf01Hql1yrIV01L1aRj7jsaaIULJslEP9Z3vKlEmnJaWJVQ==}

  electron-to-chromium@1.5.97:
    resolution: {integrity: sha512-HKLtaH02augM7ZOdYRuO19rWDeY+QSJ1VxnXFa/XDFLf07HvM90pALIJFgrO+UVaajI3+aJMMpojoUTLZyQ7JQ==}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  encode-utf8@1.0.3:
    resolution: {integrity: sha512-ucAnuBEhUK4boH2HjVYG5Q2mQyPorvv0u/ocS+zhdw0S8AlHYY+GOFhP1Gio5z4icpP2ivFSvhtFjQi8+T9ppw==}

  enquirer@2.4.1:
    resolution: {integrity: sha512-rRqJg/6gd538VHvR3PSrdRBb/1Vy2YfzHqzvbhGIQpDRKIa4FgV/54b5Q1xYSxOOwKvjXweS26E0Q+nAMwp2pQ==}
    engines: {node: '>=8.6'}

  entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}

  errno@0.1.8:
    resolution: {integrity: sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==}
    hasBin: true

  es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es-module-lexer@1.6.0:
    resolution: {integrity: sha512-qqnD1yMU6tk/jnaMosogGySTZP8YtUgAffA9nMN+E/rjxcfRQ6IEk7IiozUjgxKoFHBGjTLnrHB/YC45r/59EQ==}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}

  esbuild@0.21.5:
    resolution: {integrity: sha512-mg3OPMV4hXywwpoDxu3Qda5xCKQi+vCTZq8S9J/EpkhB2HzKXq4SNFZE3+NK93JYxc8VMSep+lOUSC/RVKaBqw==}
    engines: {node: '>=12'}
    hasBin: true

  escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}

  escape-string-regexp@5.0.0:
    resolution: {integrity: sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==}
    engines: {node: '>=12'}

  eslint-scope@5.1.1:
    resolution: {integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==}
    engines: {node: '>=8.0.0'}

  eslint-utils@2.1.0:
    resolution: {integrity: sha512-w94dQYoauyvlDc43XnGB8lU3Zt713vNChgt4EWwhXAP2XkBvndfxF0AgIqKOOasjPIPzj9JqgwkwbCYD0/V3Zg==}
    engines: {node: '>=6'}

  eslint-utils@3.0.0:
    resolution: {integrity: sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA==}
    engines: {node: ^10.0.0 || ^12.0.0 || >= 14.0.0}
    peerDependencies:
      eslint: '>=5'

  eslint-visitor-keys@1.3.0:
    resolution: {integrity: sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==}
    engines: {node: '>=4'}

  eslint-visitor-keys@2.1.0:
    resolution: {integrity: sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==}
    engines: {node: '>=10'}

  eslint@7.32.0:
    resolution: {integrity: sha512-VHZ8gX+EDfz+97jGcgyGCyRia/dPOd6Xh9yPv8Bl1+SoaIwD+a/vlrOmGRUyOYu7MwUhc7CxqeaDZU13S4+EpA==}
    engines: {node: ^10.12.0 || >=12.0.0}
    deprecated: This version is no longer supported. Please see https://eslint.org/version-support for other options.
    hasBin: true

  espree@7.3.1:
    resolution: {integrity: sha512-v3JCNCE64umkFpmkFGqzVKsOT0tN1Zr+ueqLZfpV1Ob8e+CEgPWa+OxCoGH3tnhimMKIaBm4m/vaRpJ/krRz2g==}
    engines: {node: ^10.12.0 || >=12.0.0}

  esprima@4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true

  esquery@1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}

  estraverse@4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  estree-walker@2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==}

  estree-walker@3.0.3:
    resolution: {integrity: sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g==}

  esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  eventemitter3@5.0.1:
    resolution: {integrity: sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  fast-diff@1.3.0:
    resolution: {integrity: sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==}

  fast-glob@3.3.3:
    resolution: {integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}

  fast-uri@3.0.6:
    resolution: {integrity: sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw==}

  fastq@1.19.0:
    resolution: {integrity: sha512-7SFSRCNjBQIZH/xZR3iy5iQYR8aGBE0h3VG6/cwlbrpdciNYBMotQav8c1XI3HjHH+NikUpP53nPdlZSdWmFzA==}

  file-entry-cache@6.0.1:
    resolution: {integrity: sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==}
    engines: {node: ^10.12.0 || >=12.0.0}

  fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}

  find-up@4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==}
    engines: {node: '>=8'}

  flat-cache@3.2.0:
    resolution: {integrity: sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==}
    engines: {node: ^10.12.0 || >=12.0.0}

  flatted@3.3.2:
    resolution: {integrity: sha512-AiwGJM8YcNOaobumgtng+6NHuOqC3A7MixFeDafM3X9cIUM+xUXoS5Vfgf+OihAYe20fxqNM9yPBXJzRtZ/4eA==}

  follow-redirects@1.15.9:
    resolution: {integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  form-data@4.0.1:
    resolution: {integrity: sha512-tzN8e4TX8+kkxGPK8D5u0FNmjPUjw3lwC9lSLxxoB/+GtsJG91CO8bSWy73APlgAZzZbXEYZJuxjkHH2w+Ezhw==}
    engines: {node: '>= 6'}

  fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  functional-red-black-tree@1.0.1:
    resolution: {integrity: sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g==}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-intrinsic@1.2.7:
    resolution: {integrity: sha512-VW6Pxhsrk0KAOqs3WEd0klDiF/+V7gQOpAvY1jVU/LHmaD/kQO4523aiJuikX/QAKYiW6x8Jh+RJej1almdtCA==}
    engines: {node: '>= 0.4'}

  get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    deprecated: Glob versions prior to v9 are no longer supported

  globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  globals@13.24.0:
    resolution: {integrity: sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==}
    engines: {node: '>=8'}

  globby@11.1.0:
    resolution: {integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==}
    engines: {node: '>=10'}

  gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  grid-layout-plus@1.0.6:
    resolution: {integrity: sha512-LyU2TWNMN4yXy2Y16sWWgghi35711onjkehbIyB8XCN/lSxNK7xoV0iphZ9LWZOw6lAM1+ngF56VVllviM8f4g==}
    peerDependencies:
      vue: ^3.0.0

  has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  he@1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true

  html-tags@3.3.1:
    resolution: {integrity: sha512-ztqyC3kLto0e9WbNp0aeP+M3kTt+nbaIveGmUxAtZa+8iFgKLUOD4YKM5j+f3QD89bra7UeumolZHKuOXnTmeQ==}
    engines: {node: '>=8'}

  iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}

  ignore@4.0.6:
    resolution: {integrity: sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==}
    engines: {node: '>= 4'}

  ignore@5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==}
    engines: {node: '>= 4'}

  image-size@0.5.5:
    resolution: {integrity: sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  immutable@5.0.3:
    resolution: {integrity: sha512-P8IdPQHq3lA1xVeBRi5VPqUm5HDgKnx0Ru51wZz5mjxHr5n3RWhjIpOFU7ybkUxfB+5IToy+OLaHYDBIWsv+uw==}

  import-fresh@3.3.1:
    resolution: {integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==}
    engines: {node: '>=6'}

  imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}

  inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  interactjs@1.10.27:
    resolution: {integrity: sha512-y/8RcCftGAF24gSp76X2JS3XpHiUvDQyhF8i7ujemBz77hwiHDuJzftHx7thY8cxGogwGiPJ+o97kWB6eAXnsA==}

  is-arrayish@0.3.2:
    resolution: {integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==}

  is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}

  is-core-module@2.16.1:
    resolution: {integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==}
    engines: {node: '>= 0.4'}

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  is-what@3.14.1:
    resolution: {integrity: sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA==}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  js-tokens@9.0.1:
    resolution: {integrity: sha512-mxa9E9ITFOt0ban3j6L5MpjwegGz6lBQmM1IJkWeBZGcMxto50+eWdjC/52xDbS2vy0k7vIMK0Fe2wfL9OQSpQ==}

  js-yaml@3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==}
    hasBin: true

  jsesc@3.1.0:
    resolution: {integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==}
    engines: {node: '>=6'}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  json-schema-traverse@1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  keyv@4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==}

  less@4.2.2:
    resolution: {integrity: sha512-tkuLHQlvWUTeQ3doAqnHbNn8T6WX1KA8yvbKG9x4VtKtIjHsVKQZCH11zRgAfbDAXC2UNIg/K9BYAAcEzUIrNg==}
    engines: {node: '>=6'}
    hasBin: true

  levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}

  local-pkg@0.4.3:
    resolution: {integrity: sha512-SFppqq5p42fe2qcZQqqEOiVRXl+WCP1MdT6k7BDEW1j++sp5fIY+/fdRQitvKgB5BrBcmrs5m/L0v2FrU5MY1g==}
    engines: {node: '>=14'}

  local-pkg@0.5.1:
    resolution: {integrity: sha512-9rrA30MRRP3gBD3HTGnC6cDFpaE1kVDWxWgqWJUN0RvDNAo+Nz/9GxB+nHOH0ifbVFy0hSA1V6vFDvnx54lTEQ==}
    engines: {node: '>=14'}

  local-pkg@1.0.0:
    resolution: {integrity: sha512-bbgPw/wmroJsil/GgL4qjDzs5YLTBMQ99weRsok1XCDccQeehbHA/I1oRvk2NPtr7KGZgT/Y5tPRnAtMqeG2Kg==}
    engines: {node: '>=14'}

  locate-path@5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==}
    engines: {node: '>=8'}

  lodash-es@4.17.21:
    resolution: {integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==}

  lodash.clonedeep@4.5.0:
    resolution: {integrity: sha512-H5ZhCF25riFd9uB5UCkVKo61m3S/xZk1x4wA6yp/L3RFP6Z/eHH1ymQcGLo7J3GMPfm0V/7m1tryHuGVxpqEBQ==}

  lodash.isequal@4.5.0:
    resolution: {integrity: sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ==}
    deprecated: This package is deprecated. Use require('node:util').isDeepStrictEqual instead.

  lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}

  lodash.truncate@4.4.2:
    resolution: {integrity: sha512-jttmRe7bRse52OsWIMDLaXxWqRAmtIUccAQ3garviCqJjafXOfNMO0yMfNpdD6zbGaTU0P5Nz7e7gAT6cKmJRw==}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}

  magic-string@0.30.17:
    resolution: {integrity: sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==}

  make-dir@2.1.0:
    resolution: {integrity: sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==}
    engines: {node: '>=6'}

  math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  mdn-data@2.0.28:
    resolution: {integrity: sha512-aylIc7Z9y4yzHYAJNuESG3hfhC+0Ibp/MAMiaOZgNv4pmEdFyfZhhhny4MNiAfWdBQ1RQ2mfDWmM1x8SvGyp8g==}

  mdn-data@2.0.30:
    resolution: {integrity: sha512-GaqWWShW4kv/G9IEucWScBx9G1/vsFZZJUO+tD26M8J8z3Kw5RDQjaoZe03YAClgeS/SWPOcb4nkFBTEi5DUEA==}

  merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true

  mind-elixir@3.3.2:
    resolution: {integrity: sha512-SHHospQXT7ARaNMMnaZLFzBsOela9tc8rgSYHPhAPrV8Jxh6MCo1X8qQxJAvuqIVvN8uSGnXf+Po4nhzzSmWWQ==}

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}

  mitt@3.0.1:
    resolution: {integrity: sha512-vKivATfr97l2/QBCYAkXYDbrIWPM2IIKEl7YPhjCvKlG3kE2gm+uBo6nEXK3M5/Ffh/FLpKExzOQ3JJoJGFKBw==}

  mlly@1.7.4:
    resolution: {integrity: sha512-qmdSIPC4bDJXgZTCR7XosJiNKySV7O215tsPtDN9iEO/7q/76b/ijtgRu/+epFXSJhijtTCCGp3DWS549P3xKw==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  muggle-string@0.4.1:
    resolution: {integrity: sha512-VNTrAak/KhO2i8dqqnqnAHOa3cYBwXEZe9h+D5h/1ZqFSTEFHdM65lR7RoIqq3tBBYavsOXV84NoHXZ0AkPyqQ==}

  nanoid@3.3.8:
    resolution: {integrity: sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}

  needle@3.3.1:
    resolution: {integrity: sha512-6k0YULvhpw+RoLNiQCRKOl09Rv1dPLr8hHnVjHqdolKwDrdNyk+Hmrthi4lIGPPz3r39dLx0hsF5s40sZ3Us4Q==}
    engines: {node: '>= 4.4.x'}
    hasBin: true

  node-addon-api@7.1.1:
    resolution: {integrity: sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==}

  node-releases@2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==}

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  nprogress@0.2.0:
    resolution: {integrity: sha512-I19aIingLgR1fmhftnbWWO3dXc0hSxqHQHQb3H8m+K3TnEn/iSeTZZOyvKXWqQESMwuUVnatlCnZdLBZZt2VSA==}

  nth-check@2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==}

  object-inspect@1.13.4:
    resolution: {integrity: sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==}
    engines: {node: '>= 0.4'}

  once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}

  optionator@0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==}
    engines: {node: '>= 0.8.0'}

  p-limit@2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==}
    engines: {node: '>=6'}

  p-locate@4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==}
    engines: {node: '>=8'}

  p-try@2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}

  parchment@3.0.0:
    resolution: {integrity: sha512-HUrJFQ/StvgmXRcQ1ftY6VEZUq3jA2t9ncFN4F84J/vN0/FPpQF+8FKXb3l6fLces6q0uOHj6NJn+2xvZnxO6A==}

  parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}

  parse-node-version@1.0.1:
    resolution: {integrity: sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA==}
    engines: {node: '>= 0.10'}

  path-browserify@1.0.1:
    resolution: {integrity: sha512-b7uo2UCUOYZcnF/3ID0lulOJi/bafxa1xPe7ZPsammBSpjSWQkjNxlt635YGS2MiR9GjvuXCtz2emr3jbsz98g==}

  path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}

  pathe@2.0.2:
    resolution: {integrity: sha512-15Ztpk+nov8DR524R4BF7uEuzESgzUEAV4Ah7CUMNGXdE5ELuvxElxGXndBl32vMSsWa1jpNf22Z+Er3sKwq+w==}

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  picomatch@4.0.2:
    resolution: {integrity: sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==}
    engines: {node: '>=12'}

  pify@4.0.1:
    resolution: {integrity: sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==}
    engines: {node: '>=6'}

  pinia-plugin-persistedstate@3.2.3:
    resolution: {integrity: sha512-Cm819WBj/s5K5DGw55EwbXDtx+EZzM0YR5AZbq9XE3u0xvXwvX2JnWoFpWIcdzISBHqy9H1UiSIUmXyXqWsQRQ==}
    peerDependencies:
      pinia: ^2.0.0

  pinia@2.3.1:
    resolution: {integrity: sha512-khUlZSwt9xXCaTbbxFYBKDc/bWAGWJjOgvxETwkTN7KRm66EeT1ZdZj6i2ceh9sP2Pzqsbc704r2yngBrxBVug==}
    peerDependencies:
      typescript: '>=4.4.4'
      vue: ^2.7.0 || ^3.5.11
    peerDependenciesMeta:
      typescript:
        optional: true

  pkg-types@1.3.1:
    resolution: {integrity: sha512-/Jm5M4RvtBFVkKWRu2BLUTNP8/M2a+UwuAX+ae4770q1qVGtfjG+WTCupoZixokjmHiry8uI+dlY8KXYV5HVVQ==}

  pngjs@5.0.0:
    resolution: {integrity: sha512-40QW5YalBNfQo5yRYmiw7Yz6TKKVr3h6970B2YE+3fQpsWcrbj1PzJgxeJ19DRQjhMbKPIuMY8rFaXc8moolVw==}
    engines: {node: '>=10.13.0'}

  postcss@8.5.2:
    resolution: {integrity: sha512-MjOadfU3Ys9KYoX0AdkBlFEF1Vx37uCCeN4ZHnmwm9FfpbsGWMZeBLMmmpY+6Ocqod7mkdZ0DT31OlbsFrLlkA==}
    engines: {node: ^10 || ^12 || >=14}

  prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}

  progress@2.0.3:
    resolution: {integrity: sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==}
    engines: {node: '>=0.4.0'}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  prr@1.0.1:
    resolution: {integrity: sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw==}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  qrcode@1.5.1:
    resolution: {integrity: sha512-nS8NJ1Z3md8uTjKtP+SGGhfqmTCs5flU/xR623oI0JX+Wepz9R8UrRVCTBTJm3qGw3rH6jJ6MUHjkDx15cxSSg==}
    engines: {node: '>=10.13.0'}
    hasBin: true

  qs@6.14.0:
    resolution: {integrity: sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==}
    engines: {node: '>=0.6'}

  queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  quill-delta@5.1.0:
    resolution: {integrity: sha512-X74oCeRI4/p0ucjb5Ma8adTXd9Scumz367kkMK5V/IatcX6A0vlgLgKbzXWy5nZmCGeNJm2oQX0d2Eqj+ZIlCA==}
    engines: {node: '>= 12.0.0'}

  quill@2.0.3:
    resolution: {integrity: sha512-xEYQBqfYx/sfb33VJiKnSJp8ehloavImQ2A6564GAbqG55PGw1dAWUn1MUbQB62t0azawUS2CZZhWCjO8gRvTw==}
    engines: {npm: '>=8.2.3'}

  readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}

  readdirp@4.1.1:
    resolution: {integrity: sha512-h80JrZu/MHUZCyHu5ciuoI0+WxsCxzxJTILn6Fs8rxSnFPh+UVHYfeIxK1nVGugMqkfC4vJcBOYbkfkwYK0+gw==}
    engines: {node: '>= 14.18.0'}

  regexpp@3.2.0:
    resolution: {integrity: sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg==}
    engines: {node: '>=8'}

  require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}

  require-from-string@2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==}
    engines: {node: '>=0.10.0'}

  require-main-filename@2.0.0:
    resolution: {integrity: sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==}

  resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  resolve@1.22.10:
    resolution: {integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==}
    engines: {node: '>= 0.4'}
    hasBin: true

  reusify@1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rimraf@3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rollup@4.34.6:
    resolution: {integrity: sha512-wc2cBWqJgkU3Iz5oztRkQbfVkbxoz5EhnCGOrnJvnLnQ7O0WhQUYyv18qQI79O8L7DdHrrlJNeCHd4VGpnaXKQ==}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  sass@1.84.0:
    resolution: {integrity: sha512-XDAbhEPJRxi7H0SxrnOpiXFQoUJHwkR2u3Zc4el+fK/Tt5Hpzw5kkQ59qVDfvdaUq6gCrEZIbySFBM2T9DNKHg==}
    engines: {node: '>=14.0.0'}
    hasBin: true

  sax@1.4.1:
    resolution: {integrity: sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==}

  scule@1.3.0:
    resolution: {integrity: sha512-6FtHJEvt+pVMIB9IBY+IcCJ6Z5f1iQnytgyfKMhDKgmzYG+TeH/wx1y3l27rshSbLiSanrR9ffZDrEsmjlQF2g==}

  semver@5.7.2:
    resolution: {integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==}
    hasBin: true

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.7.1:
    resolution: {integrity: sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==}
    engines: {node: '>=10'}
    hasBin: true

  set-blocking@2.0.0:
    resolution: {integrity: sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  shepherd.js@11.1.1:
    resolution: {integrity: sha512-7nVEgLTZUu5qQCKTlzQeKL1AQd2rG9Y9iqzZUgGvCFwMUZZhfwtZ6eEyMWMYw0zl8qKjSrjgzxFOe+SpfO43aA==}
    engines: {node: 16.* || >= 18}

  side-channel-list@1.0.0:
    resolution: {integrity: sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==}
    engines: {node: '>= 0.4'}

  side-channel-map@1.0.1:
    resolution: {integrity: sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==}
    engines: {node: '>= 0.4'}

  side-channel-weakmap@1.0.2:
    resolution: {integrity: sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==}
    engines: {node: '>= 0.4'}

  side-channel@1.1.0:
    resolution: {integrity: sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==}
    engines: {node: '>= 0.4'}

  simple-swizzle@0.2.2:
    resolution: {integrity: sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==}

  slash@3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}

  slice-ansi@4.0.0:
    resolution: {integrity: sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==}
    engines: {node: '>=10'}

  source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  sprintf-js@1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==}

  streamsaver@2.0.6:
    resolution: {integrity: sha512-LK4e7TfCV8HzuM0PKXuVUfKyCB1FtT9L0EGxsFk5Up8njj0bXK8pJM9+Wq2Nya7/jslmCQwRK39LFm55h7NBTw==}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}

  strip-literal@2.1.1:
    resolution: {integrity: sha512-631UJ6O00eNGfMiWG78ck80dfBab8X6IVFB51jZK5Icd7XAs60Z5y7QdSd/wGIklnWvRbUNloVzhOKKmutxQ6Q==}

  supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  svg-tags@1.0.0:
    resolution: {integrity: sha512-ovssysQTa+luh7A5Weu3Rta6FJlFBBbInjOh722LIt6klpU2/HtdUbszju/G4devcvk8PGt7FCLv5wftu3THUA==}

  svgo@3.3.2:
    resolution: {integrity: sha512-OoohrmuUlBs8B8o6MB2Aevn+pRIH9zDALSR+6hhqVfa6fRwG/Qw9VUMSMW9VNg2CFc/MTIfabtdOVl9ODIJjpw==}
    engines: {node: '>=14.0.0'}
    hasBin: true

  table@6.9.0:
    resolution: {integrity: sha512-9kY+CygyYM6j02t5YFHbNz2FN5QmYGv9zAjVp4lCDjlCw7amdckXlEt/bjMhUIfj4ThGRE4gCUH5+yGnNuPo5A==}
    engines: {node: '>=10.0.0'}

  tailwind-merge@1.14.0:
    resolution: {integrity: sha512-3mFKyCo/MBcgyOTlrY8T7odzZFx+w+qKSMAmdFzRvqBfLlSigU6TZnlFHK0lkMwj9Bj8OYU+9yW9lmGuS0QEnQ==}

  tar-mini@0.2.0:
    resolution: {integrity: sha512-+qfUHz700DWnRutdUsxRRVZ38G1Qr27OetwaMYTdg8hcPxf46U0S1Zf76dQMWRBmusOt2ZCK5kbIaiLkoGO7WQ==}

  text-table@0.2.0:
    resolution: {integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  tslib@1.14.1:
    resolution: {integrity: sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==}

  tslib@2.3.0:
    resolution: {integrity: sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg==}

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  tsutils@3.21.0:
    resolution: {integrity: sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA==}
    engines: {node: '>= 6'}
    peerDependencies:
      typescript: '>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta'

  type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}

  type-fest@0.20.2:
    resolution: {integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==}
    engines: {node: '>=10'}

  typescript@5.7.3:
    resolution: {integrity: sha512-84MVSjMEHP+FQRPy3pX9sTVV/INIex71s9TL2Gm5FG/WG1SqXeKyZ0k7/blY/4FdOzI12CBy1vGc4og/eus0fw==}
    engines: {node: '>=14.17'}
    hasBin: true

  ufo@1.5.4:
    resolution: {integrity: sha512-UsUk3byDzKd04EyoZ7U4DOlxQaD14JUKQl6/P7wiX4FNvUfm3XL246n9W5AmqwW5RSFJ27NAuM0iLscAOYUiGQ==}

  undici-types@6.20.0:
    resolution: {integrity: sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg==}

  unimport@3.14.6:
    resolution: {integrity: sha512-CYvbDaTT04Rh8bmD8jz3WPmHYZRG/NnvYVzwD6V1YAlvvKROlAeNDUBhkBGzNav2RKaeuXvlWYaa1V4Lfi/O0g==}

  unplugin-auto-import@0.17.8:
    resolution: {integrity: sha512-CHryj6HzJ+n4ASjzwHruD8arhbdl+UXvhuAIlHDs15Y/IMecG3wrf7FVg4pVH/DIysbq/n0phIjNHAjl7TG7Iw==}
    engines: {node: '>=14'}
    peerDependencies:
      '@nuxt/kit': ^3.2.2
      '@vueuse/core': '*'
    peerDependenciesMeta:
      '@nuxt/kit':
        optional: true
      '@vueuse/core':
        optional: true

  unplugin-vue-components@0.26.0:
    resolution: {integrity: sha512-s7IdPDlnOvPamjunVxw8kNgKNK8A5KM1YpK5j/p97jEKTjlPNrA0nZBiSfAKKlK1gWZuyWXlKL5dk3EDw874LQ==}
    engines: {node: '>=14'}
    peerDependencies:
      '@babel/parser': ^7.15.8
      '@nuxt/kit': ^3.2.2
      vue: 2 || 3
    peerDependenciesMeta:
      '@babel/parser':
        optional: true
      '@nuxt/kit':
        optional: true

  unplugin-vue-components@0.27.5:
    resolution: {integrity: sha512-m9j4goBeNwXyNN8oZHHxvIIYiG8FQ9UfmKWeNllpDvhU7btKNNELGPt+o3mckQKuPwrE7e0PvCsx+IWuDSD9Vg==}
    engines: {node: '>=14'}
    peerDependencies:
      '@babel/parser': ^7.15.8
      '@nuxt/kit': ^3.2.2
      vue: 2 || 3
    peerDependenciesMeta:
      '@babel/parser':
        optional: true
      '@nuxt/kit':
        optional: true

  unplugin@1.16.1:
    resolution: {integrity: sha512-4/u/j4FrCKdi17jaxuJA0jClGxB1AvU2hw/IuayPc4ay1XGaJs/rbb4v5WKwAjNifjmXK9PIFyuPiaK8azyR9w==}
    engines: {node: '>=14.0.0'}

  update-browserslist-db@1.1.2:
    resolution: {integrity: sha512-PPypAm5qvlD7XMZC3BujecnaOxwhrtoFR+Dqkk5Aa/6DssiH0ibKoketaj9w8LP7Bont1rYeoV5plxD7RTEPRg==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}

  v8-compile-cache@2.4.0:
    resolution: {integrity: sha512-ocyWc3bAHBB/guyqJQVI5o4BZkPhznPYUG2ea80Gond/BgNWpap8TOmLSeeQG7bnh2KMISxskdADG59j7zruhw==}

  vite-plugin-compression2@1.3.3:
    resolution: {integrity: sha512-Mb+xi/C5b68awtF4fNwRBPtoZiyUHU3I0SaBOAGlerlR31kusq1si6qG31lsjJH8T7QNg/p3IJY2HY9O9SvsfQ==}
    peerDependencies:
      vite: ^2.0.0||^3.0.0||^4.0.0||^5.0.0 ||^6.0.0

  vite-plugin-lazy-import@1.0.7:
    resolution: {integrity: sha512-mE6oAObOb4wqso4AoUGi9cLjdR+4vay1RCaKJvziBuFPlziZl7J0aw2hsqRTokLVRx3bli0a0VyjMOwsNDv58A==}

  vite-svg-loader@5.1.0:
    resolution: {integrity: sha512-M/wqwtOEjgb956/+m5ZrYT/Iq6Hax0OakWbokj8+9PXOnB7b/4AxESHieEtnNEy7ZpjsjYW1/5nK8fATQMmRxw==}
    peerDependencies:
      vue: '>=3.2.13'

  vite@5.4.14:
    resolution: {integrity: sha512-EK5cY7Q1D8JNhSaPKVK4pwBFvaTmZxEnoKXLG/U9gmdDcihQGNzFlgIvaxezFR4glP1LsuiedwMBqCXH3wZccA==}
    engines: {node: ^18.0.0 || >=20.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || >=20.0.0
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      sass-embedded: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true

  vscode-uri@3.1.0:
    resolution: {integrity: sha512-/BpdSx+yCQGnCvecbyXdxHDkuk55/G3xwnC0GqY4gmQ3j+A+g8kzzgB4Nk/SINjqn6+waqw3EgbVF2QKExkRxQ==}

  vue-demi@0.14.10:
    resolution: {integrity: sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg==}
    engines: {node: '>=12'}
    hasBin: true
    peerDependencies:
      '@vue/composition-api': ^1.0.0-rc.1
      vue: ^3.0.0-0 || ^2.6.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true

  vue-router@4.5.0:
    resolution: {integrity: sha512-HDuk+PuH5monfNuY+ct49mNmkCRK4xJAV9Ts4z9UFc4rzdDnxQLyCMGGc8pKhZhHTVzfanpNwB/lwqevcBwI4w==}
    peerDependencies:
      vue: ^3.2.0

  vue-tsc@2.2.0:
    resolution: {integrity: sha512-gtmM1sUuJ8aSb0KoAFmK9yMxb8TxjewmxqTJ1aKphD5Cbu0rULFY6+UQT51zW7SpUcenfPUuflKyVwyx9Qdnxg==}
    hasBin: true
    peerDependencies:
      typescript: '>=5.0.0'

  vue@3.5.13:
    resolution: {integrity: sha512-wmeiSMxkZCSc+PM2w2VRsOYAZC8GdipNFRTsLSfodVqI9mbejKeXEGr8SckuLnrQPGe3oJN5c3K0vpoU9q/wCQ==}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  vxe-pc-ui@4.3.50:
    resolution: {integrity: sha512-oo7jc80KqVDzekouMDGB3JKsFxHaMdxXtdO9PVmGpaKfwheBpEO6G6vfnYyZ8ShTJq8DhNimc3koidd6rYK4xA==}

  vxe-table@4.9.19:
    resolution: {integrity: sha512-Uaa6H6l6WRKxH3lgh/i2GLkBXxGQDd9smG2TqdoAoCPudpXoFmqA8Vgpl1YAps7GTV4ThVNOIK8t3aL+u9ALsA==}

  webpack-virtual-modules@0.6.2:
    resolution: {integrity: sha512-66/V2i5hQanC51vBQKPH4aI8NMAcBW59FVBs+rC7eGHupMyfn34q7rZIE+ETlJ+XTevqfUhVVBgSUNSW2flEUQ==}

  which-module@2.0.1:
    resolution: {integrity: sha512-iBdZ57RDvnOR9AGBhML2vFZf7h8vmBjhoaZqODJBFWHVtKkDmKuHai3cx5PgVMrX5YDNp27AofYbAwctSS+vhQ==}

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}

  workerpool@9.2.0:
    resolution: {integrity: sha512-PKZqBOCo6CYkVOwAxWxQaSF2Fvb5Iv2fCeTP7buyWI2GiynWr46NcXSgK/idoV6e60dgCBfgYc+Un3HMvmqP8w==}

  wrap-ansi@6.2.0:
    resolution: {integrity: sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==}
    engines: {node: '>=8'}

  wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  xe-utils@3.7.0:
    resolution: {integrity: sha512-2pndXCEivB7+xWCdCDth/LJ5ngAAstUOoHTGBQauwTqc03M3Cl1tYbNhPUqi4Lcj+UNZnnc8fANbFWjb6TMx6A==}

  xss@1.0.14:
    resolution: {integrity: sha512-og7TEJhXvn1a7kzZGQ7ETjdQVS2UfZyTlsEdDOqvQF7GoxNfY+0YLCzBy1kPdsDDx4QuNAonQPddpsn6Xl/7sw==}
    engines: {node: '>= 0.10.0'}
    hasBin: true

  y18n@4.0.3:
    resolution: {integrity: sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ==}

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yargs-parser@18.1.3:
    resolution: {integrity: sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==}
    engines: {node: '>=6'}

  yargs@15.4.1:
    resolution: {integrity: sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A==}
    engines: {node: '>=8'}

  zrender@5.4.1:
    resolution: {integrity: sha512-M4Z05BHWtajY2241EmMPHglDQAJ1UyHQcYsxDNzD9XLSkPDqMq4bB28v9Pb4mvHnVQ0GxyTklZ/69xCFP6RXBA==}

snapshots:

  '@alova/scene-vue@1.6.2': {}

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@antfu/utils@0.7.10': {}

  '@babel/code-frame@7.12.11':
    dependencies:
      '@babel/highlight': 7.25.9

  '@babel/code-frame@7.26.2':
    dependencies:
      '@babel/helper-validator-identifier': 7.25.9
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.26.8': {}

  '@babel/core@7.26.8':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.26.8
      '@babel/helper-compilation-targets': 7.26.5
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.8)
      '@babel/helpers': 7.26.7
      '@babel/parser': 7.26.8
      '@babel/template': 7.26.8
      '@babel/traverse': 7.26.8
      '@babel/types': 7.26.8
      '@types/gensync': 1.0.4
      convert-source-map: 2.0.0
      debug: 4.4.0
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.26.8':
    dependencies:
      '@babel/parser': 7.26.8
      '@babel/types': 7.26.8
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.1.0

  '@babel/helper-annotate-as-pure@7.25.9':
    dependencies:
      '@babel/types': 7.26.8

  '@babel/helper-compilation-targets@7.26.5':
    dependencies:
      '@babel/compat-data': 7.26.8
      '@babel/helper-validator-option': 7.25.9
      browserslist: 4.24.4
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-create-class-features-plugin@7.25.9(@babel/core@7.26.8)':
    dependencies:
      '@babel/core': 7.26.8
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-member-expression-to-functions': 7.25.9
      '@babel/helper-optimise-call-expression': 7.25.9
      '@babel/helper-replace-supers': 7.26.5(@babel/core@7.26.8)
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
      '@babel/traverse': 7.26.8
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-member-expression-to-functions@7.25.9':
    dependencies:
      '@babel/traverse': 7.26.8
      '@babel/types': 7.26.8
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-imports@7.25.9':
    dependencies:
      '@babel/traverse': 7.26.8
      '@babel/types': 7.26.8
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.8)':
    dependencies:
      '@babel/core': 7.26.8
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9
      '@babel/traverse': 7.26.8
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-optimise-call-expression@7.25.9':
    dependencies:
      '@babel/types': 7.26.8

  '@babel/helper-plugin-utils@7.26.5': {}

  '@babel/helper-replace-supers@7.26.5(@babel/core@7.26.8)':
    dependencies:
      '@babel/core': 7.26.8
      '@babel/helper-member-expression-to-functions': 7.25.9
      '@babel/helper-optimise-call-expression': 7.25.9
      '@babel/traverse': 7.26.8
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    dependencies:
      '@babel/traverse': 7.26.8
      '@babel/types': 7.26.8
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-string-parser@7.25.9': {}

  '@babel/helper-validator-identifier@7.25.9': {}

  '@babel/helper-validator-option@7.25.9': {}

  '@babel/helpers@7.26.7':
    dependencies:
      '@babel/template': 7.26.8
      '@babel/types': 7.26.8

  '@babel/highlight@7.25.9':
    dependencies:
      '@babel/helper-validator-identifier': 7.25.9
      chalk: 2.4.2
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/parser@7.26.8':
    dependencies:
      '@babel/types': 7.26.8

  '@babel/plugin-syntax-jsx@7.25.9(@babel/core@7.26.8)':
    dependencies:
      '@babel/core': 7.26.8
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-typescript@7.25.9(@babel/core@7.26.8)':
    dependencies:
      '@babel/core': 7.26.8
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-typescript@7.26.8(@babel/core@7.26.8)':
    dependencies:
      '@babel/core': 7.26.8
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-create-class-features-plugin': 7.25.9(@babel/core@7.26.8)
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
      '@babel/plugin-syntax-typescript': 7.25.9(@babel/core@7.26.8)
    transitivePeerDependencies:
      - supports-color

  '@babel/template@7.26.8':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/parser': 7.26.8
      '@babel/types': 7.26.8

  '@babel/traverse@7.26.8':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.26.8
      '@babel/parser': 7.26.8
      '@babel/template': 7.26.8
      '@babel/types': 7.26.8
      debug: 4.4.0
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.26.8':
    dependencies:
      '@babel/helper-string-parser': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9

  '@better-scroll/core@2.5.0':
    dependencies:
      '@better-scroll/shared-utils': 2.5.1

  '@better-scroll/shared-utils@2.5.1': {}

  '@better-scroll/wheel@2.5.0':
    dependencies:
      '@better-scroll/core': 2.5.0

  '@esbuild/aix-ppc64@0.21.5':
    optional: true

  '@esbuild/android-arm64@0.21.5':
    optional: true

  '@esbuild/android-arm@0.21.5':
    optional: true

  '@esbuild/android-x64@0.21.5':
    optional: true

  '@esbuild/darwin-arm64@0.21.5':
    optional: true

  '@esbuild/darwin-x64@0.21.5':
    optional: true

  '@esbuild/freebsd-arm64@0.21.5':
    optional: true

  '@esbuild/freebsd-x64@0.21.5':
    optional: true

  '@esbuild/linux-arm64@0.21.5':
    optional: true

  '@esbuild/linux-arm@0.21.5':
    optional: true

  '@esbuild/linux-ia32@0.21.5':
    optional: true

  '@esbuild/linux-loong64@0.21.5':
    optional: true

  '@esbuild/linux-mips64el@0.21.5':
    optional: true

  '@esbuild/linux-ppc64@0.21.5':
    optional: true

  '@esbuild/linux-riscv64@0.21.5':
    optional: true

  '@esbuild/linux-s390x@0.21.5':
    optional: true

  '@esbuild/linux-x64@0.21.5':
    optional: true

  '@esbuild/netbsd-x64@0.21.5':
    optional: true

  '@esbuild/openbsd-x64@0.21.5':
    optional: true

  '@esbuild/sunos-x64@0.21.5':
    optional: true

  '@esbuild/win32-arm64@0.21.5':
    optional: true

  '@esbuild/win32-ia32@0.21.5':
    optional: true

  '@esbuild/win32-x64@0.21.5':
    optional: true

  '@eslint/eslintrc@0.4.3':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.0
      espree: 7.3.1
      globals: 13.24.0
      ignore: 4.0.6
      import-fresh: 3.3.1
      js-yaml: 3.14.1
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@floating-ui/core@1.6.9':
    dependencies:
      '@floating-ui/utils': 0.2.9

  '@floating-ui/dom@1.6.13':
    dependencies:
      '@floating-ui/core': 1.6.9
      '@floating-ui/utils': 0.2.9

  '@floating-ui/utils@0.2.9': {}

  '@humanwhocodes/config-array@0.5.0':
    dependencies:
      '@humanwhocodes/object-schema': 1.2.1
      debug: 4.4.0
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@humanwhocodes/object-schema@1.2.1': {}

  '@interactjs/types@1.10.27': {}

  '@jridgewell/gen-mapping@0.3.8':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@juggle/resize-observer@3.4.0': {}

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.0

  '@opentiny/fluent-editor@3.25.2':
    dependencies:
      lodash-es: 4.17.21
      quill: 2.0.3

  '@opentiny/huicharts@1.0.1': {}

  '@opentiny/unplugin-tiny-vue@0.0.2(@babel/parser@7.26.8)(rollup@4.34.6)(vite@5.4.14(@types/node@22.13.1)(less@4.2.2)(sass@1.84.0))(vue@3.5.13(typescript@5.7.3))':
    dependencies:
      unplugin-vue-components: 0.26.0(@babel/parser@7.26.8)(rollup@4.34.6)(vue@3.5.13(typescript@5.7.3))
      vite: 5.4.14(@types/node@22.13.1)(less@4.2.2)(sass@1.84.0)
    transitivePeerDependencies:
      - '@babel/parser'
      - '@nuxt/kit'
      - rollup
      - supports-color
      - vue

  '@opentiny/utils@1.0.0':
    dependencies:
      xss: 1.0.14

  '@opentiny/vue-action-menu@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-dropdown': 3.21.1
      '@opentiny/vue-dropdown-item': 3.21.0
      '@opentiny/vue-dropdown-menu': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-action-sheet@3.21.0':
    dependencies:
      '@better-scroll/core': 2.5.0
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-drawer': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-alert@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-amount@3.21.0':
    dependencies:
      '@opentiny/vue-action-sheet': 3.21.0
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-currency': 3.21.0
      '@opentiny/vue-date-picker': 3.21.0
      '@opentiny/vue-date-picker-mobile-first': 3.21.0
      '@opentiny/vue-filter-box': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-popover': 3.21.0
      '@opentiny/vue-radio': 3.21.0
      '@opentiny/vue-radio-group': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-anchor@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-directive': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-area@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-option': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-select': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-async-flowchart@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-flowchart': 3.21.0
      '@opentiny/vue-loading': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-autocomplete@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-scrollbar': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-avatar@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-badge@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-base-select@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-checkbox': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-filter-box': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-option': 3.21.0
      '@opentiny/vue-recycle-scroller': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-scrollbar': 3.21.0
      '@opentiny/vue-select-dropdown': 3.21.0
      '@opentiny/vue-tag': 3.21.0
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-tooltip': 3.21.0
      '@opentiny/vue-tree': 3.21.0

  '@opentiny/vue-breadcrumb-item@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-breadcrumb@3.21.0':
    dependencies:
      '@opentiny/vue-breadcrumb-item': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-bulletin-board@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-tab-item': 3.21.0
      '@opentiny/vue-tabs': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-button-group@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-directive': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-popover': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-button@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-calendar-bar@3.21.0':
    dependencies:
      '@opentiny/vue-cascader-select': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-calendar-view@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-date-picker': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-radio-button': 3.21.0
      '@opentiny/vue-radio-group': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-slider-button': 3.21.0
      '@opentiny/vue-slider-button-group': 3.21.0
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-tooltip': 3.21.0

  '@opentiny/vue-calendar@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-popover': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-tooltip': 3.21.0

  '@opentiny/vue-card-group@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-card-template@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-card@3.21.0':
    dependencies:
      '@opentiny/vue-checkbox': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-dropdown': 3.21.1
      '@opentiny/vue-dropdown-item': 3.21.0
      '@opentiny/vue-dropdown-menu': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-radio': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-carousel-item@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-carousel@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-cascader-menu@3.21.0':
    dependencies:
      '@opentiny/vue-cascader-node': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-scrollbar': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-cascader-mobile@3.21.0':
    dependencies:
      '@opentiny/vue-action-sheet': 3.21.0
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-exception': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-cascader-node@3.21.0':
    dependencies:
      '@opentiny/vue-checkbox': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-radio': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-cascader-panel@3.21.0':
    dependencies:
      '@opentiny/vue-cascader-menu': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-cascader-select@3.21.0':
    dependencies:
      '@opentiny/vue-action-sheet': 3.21.0
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-cascader-view@3.21.0':
    dependencies:
      '@opentiny/vue-action-sheet': 3.21.0
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-checkbox': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-exception': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-cascader@3.21.0':
    dependencies:
      '@opentiny/vue-cascader-mobile': 3.21.0
      '@opentiny/vue-cascader-panel': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-directive': 3.21.0
      '@opentiny/vue-filter-box': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-scrollbar': 3.21.0
      '@opentiny/vue-tag': 3.21.0
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-tooltip': 3.21.0

  '@opentiny/vue-cell@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-checkbox-button@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-checkbox-group@3.21.0':
    dependencies:
      '@opentiny/vue-checkbox': 3.21.0
      '@opentiny/vue-checkbox-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-checkbox@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-directive': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-col@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-collapse-item@3.21.0':
    dependencies:
      '@opentiny/vue-collapse-transition': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-collapse-transition@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-collapse@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-color-picker@3.21.0':
    dependencies:
      '@opentiny/vue-color-select-panel': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-color-select-panel@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-collapse': 3.21.0
      '@opentiny/vue-collapse-item': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-option': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-select': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-column-list-group@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-column-list-item@3.21.0':
    dependencies:
      '@opentiny/vue-checkbox': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-dropdown': 3.21.1
      '@opentiny/vue-dropdown-item': 3.21.0
      '@opentiny/vue-dropdown-menu': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-radio': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-common@3.21.0':
    dependencies:
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      tailwind-merge: 1.14.0

  '@opentiny/vue-company@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-option': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-select': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-config-provider@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-container@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-country@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-option': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-select': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-crop@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      cropperjs: 1.5.7

  '@opentiny/vue-currency@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-option': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-select': 3.21.0
      '@opentiny/vue-select-mobile': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-date-panel@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-date-table': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-month-table': 3.21.0
      '@opentiny/vue-popup': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-time': 3.21.0
      '@opentiny/vue-year-table': 3.21.0

  '@opentiny/vue-date-picker-mobile-first@3.21.0':
    dependencies:
      '@opentiny/vue-action-sheet': 3.21.0
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-recycle-scroller': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-time-picker-mobile': 3.21.0

  '@opentiny/vue-date-picker@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-mini-picker': 3.21.0
      '@opentiny/vue-picker': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-date-range@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-date-table': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-time': 3.21.0

  '@opentiny/vue-date-table@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-dept@3.21.0':
    dependencies:
      '@opentiny/vue-col': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-dialog-box': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-option': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-row': 3.21.0
      '@opentiny/vue-select': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-dialog-box@3.21.0':
    dependencies:
      '@opentiny/vue-action-sheet': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-dialog-select@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-dialog-box': 3.21.0
      '@opentiny/vue-grid': 3.21.2
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-pager': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-selected-box': 3.21.0
      '@opentiny/vue-split': 3.21.0
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-tree': 3.21.0

  '@opentiny/vue-directive@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-tooltip': 3.21.0

  '@opentiny/vue-divider@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-drawer@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-tooltip': 3.21.0

  '@opentiny/vue-drop-roles@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-option': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-select': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-drop-times@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-option': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-select': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-dropdown-item@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-directive': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-popup': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-dropdown-menu@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-dropdown-item': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0
      '@opentiny/vue-tooltip': 3.21.0

  '@opentiny/vue-dropdown@3.21.1':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-button-group': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-dropdown-menu': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-dynamic-scroller-item@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-dynamic-scroller@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-recycle-scroller': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-espace@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-exception@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-fall-menu@3.21.0':
    dependencies:
      '@opentiny/vue-col': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-row': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-file-upload@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-dialog-box': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-modal': 3.21.0
      '@opentiny/vue-popover': 3.21.0
      '@opentiny/vue-progress': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-switch': 3.21.0
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0
      '@opentiny/vue-tooltip': 3.21.0
      '@opentiny/vue-upload': 3.21.0
      '@opentiny/vue-upload-list': 3.21.0
      streamsaver: 2.0.6

  '@opentiny/vue-filter-bar@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-filter-box@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-tooltip': 3.21.0

  '@opentiny/vue-filter-panel@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-filter-box': 3.21.0
      '@opentiny/vue-popover': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-filter@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-filter-bar': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-float-button@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-floatbar@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-floating-button@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-flowchart@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-popover': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-fluent-editor@3.21.0':
    dependencies:
      '@opentiny/fluent-editor': 3.25.2
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-image-viewer': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-form-item@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-tooltip': 3.21.0

  '@opentiny/vue-form@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0
      '@opentiny/vue-tooltip': 3.21.0

  '@opentiny/vue-fullscreen@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-grid-column@3.21.0':
    dependencies:
      '@opentiny/vue-grid': 3.21.2

  '@opentiny/vue-grid-manager@3.21.0':
    dependencies:
      '@opentiny/vue-grid': 3.21.2

  '@opentiny/vue-grid-select@3.21.0':
    dependencies:
      '@opentiny/vue-base-select': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-grid': 3.21.2
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-grid-toolbar@3.21.0':
    dependencies:
      '@opentiny/vue-alert': 3.21.0
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-checkbox': 3.21.0
      '@opentiny/vue-checkbox-group': 3.21.0
      '@opentiny/vue-col': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-dialog-box': 3.21.0
      '@opentiny/vue-dropdown': 3.21.1
      '@opentiny/vue-dropdown-item': 3.21.0
      '@opentiny/vue-dropdown-menu': 3.21.0
      '@opentiny/vue-grid': 3.21.2
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-layout': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-modal': 3.21.0
      '@opentiny/vue-option': 3.21.0
      '@opentiny/vue-popover': 3.21.0
      '@opentiny/vue-radio': 3.21.0
      '@opentiny/vue-radio-group': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-row': 3.21.0
      '@opentiny/vue-search': 3.21.0
      '@opentiny/vue-select': 3.21.0
      '@opentiny/vue-split': 3.21.0
      '@opentiny/vue-tab-item': 3.21.0
      '@opentiny/vue-tabs': 3.21.0
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-tooltip': 3.21.0
      '@opentiny/vue-tree': 3.21.0

  '@opentiny/vue-grid@3.21.2':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-directive': 3.21.0
      '@opentiny/vue-dropdown': 3.21.1
      '@opentiny/vue-dropdown-item': 3.21.0
      '@opentiny/vue-dropdown-menu': 3.21.0
      '@opentiny/vue-exception': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-loading': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-modal': 3.21.0
      '@opentiny/vue-pager': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-tag': 3.21.0
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-tooltip': 3.21.0

  '@opentiny/vue-guide@3.21.0':
    dependencies:
      '@floating-ui/dom': 1.6.13
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      shepherd.js: 11.1.1

  '@opentiny/vue-hrapprover@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-dept': 3.21.0
      '@opentiny/vue-loading': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-modal': 3.21.0
      '@opentiny/vue-radio': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-huicharts-amap@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0

  '@opentiny/vue-huicharts-bar@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0

  '@opentiny/vue-huicharts-bmap@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0

  '@opentiny/vue-huicharts-boxplot@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0

  '@opentiny/vue-huicharts-candle@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0
      '@opentiny/vue-locale': 3.21.0

  '@opentiny/vue-huicharts-core@3.21.0':
    dependencies:
      '@opentiny/huicharts': 1.0.1
      '@opentiny/vue-theme': 3.21.2
      echarts: 5.4.1

  '@opentiny/vue-huicharts-funnel@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0

  '@opentiny/vue-huicharts-gauge@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0

  '@opentiny/vue-huicharts-graph@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0

  '@opentiny/vue-huicharts-heatmap@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0

  '@opentiny/vue-huicharts-histogram@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0

  '@opentiny/vue-huicharts-line@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0

  '@opentiny/vue-huicharts-liquidfill@3.21.0(echarts@5.4.1)':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0
      echarts-liquidfill: 3.1.0(echarts@5.4.1)
    transitivePeerDependencies:
      - echarts

  '@opentiny/vue-huicharts-map@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0

  '@opentiny/vue-huicharts-pie@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0
      '@opentiny/vue-locale': 3.21.0

  '@opentiny/vue-huicharts-process@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0

  '@opentiny/vue-huicharts-radar@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0

  '@opentiny/vue-huicharts-ring@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0
      '@opentiny/vue-locale': 3.21.0

  '@opentiny/vue-huicharts-sankey@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0

  '@opentiny/vue-huicharts-scatter@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0

  '@opentiny/vue-huicharts-sunburst@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0

  '@opentiny/vue-huicharts-tree@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0

  '@opentiny/vue-huicharts-waterfall@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0
      '@opentiny/vue-locale': 3.21.0

  '@opentiny/vue-huicharts-wordcloud@3.21.0(echarts@5.4.1)':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0
      echarts-wordcloud: 2.0.0(echarts@5.4.1)
    transitivePeerDependencies:
      - echarts

  '@opentiny/vue-huicharts@3.21.0(echarts@5.4.1)':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-huicharts-amap': 3.21.0
      '@opentiny/vue-huicharts-bar': 3.21.0
      '@opentiny/vue-huicharts-bmap': 3.21.0
      '@opentiny/vue-huicharts-boxplot': 3.21.0
      '@opentiny/vue-huicharts-candle': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0
      '@opentiny/vue-huicharts-funnel': 3.21.0
      '@opentiny/vue-huicharts-gauge': 3.21.0
      '@opentiny/vue-huicharts-graph': 3.21.0
      '@opentiny/vue-huicharts-heatmap': 3.21.0
      '@opentiny/vue-huicharts-histogram': 3.21.0
      '@opentiny/vue-huicharts-line': 3.21.0
      '@opentiny/vue-huicharts-liquidfill': 3.21.0(echarts@5.4.1)
      '@opentiny/vue-huicharts-map': 3.21.0
      '@opentiny/vue-huicharts-pie': 3.21.0
      '@opentiny/vue-huicharts-radar': 3.21.0
      '@opentiny/vue-huicharts-ring': 3.21.0
      '@opentiny/vue-huicharts-sankey': 3.21.0
      '@opentiny/vue-huicharts-scatter': 3.21.0
      '@opentiny/vue-huicharts-sunburst': 3.21.0
      '@opentiny/vue-huicharts-tree': 3.21.0
      '@opentiny/vue-huicharts-waterfall': 3.21.0
      '@opentiny/vue-huicharts-wordcloud': 3.21.0(echarts@5.4.1)
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-theme': 3.21.2
    transitivePeerDependencies:
      - echarts

  '@opentiny/vue-icon@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-image-viewer@3.21.0':
    dependencies:
      '@opentiny/vue-action-sheet': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-dropdown': 3.21.1
      '@opentiny/vue-dropdown-item': 3.21.0
      '@opentiny/vue-dropdown-menu': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-image@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-image-viewer': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-index-bar-anchor@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-index-bar@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-input@3.21.0':
    dependencies:
      '@opentiny/vue-action-sheet': 3.21.0
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-dialog-box': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0
      '@opentiny/vue-tooltip': 3.21.0

  '@opentiny/vue-ip-address@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-label@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-layout@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-link-menu@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-dialog-box': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-tree': 3.21.0

  '@opentiny/vue-link@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-list@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-load-list@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-loading@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-locale@3.21.0':
    dependencies:
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-locales@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-popover': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-logon-user@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-logout@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-mask@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-menu@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-exception': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-message@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-modal': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-milestone@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-tooltip': 3.21.0

  '@opentiny/vue-mind-map@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      mind-elixir: 3.3.2

  '@opentiny/vue-mini-picker@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-picker-column': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-modal@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-checkbox': 3.21.0
      '@opentiny/vue-checkbox-group': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-popconfirm': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-month-range@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-month-table': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-month-table@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-multi-select-item@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-multi-select@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-mask': 3.21.0
      '@opentiny/vue-multi-select-item': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme-mobile': 3.21.0
      '@opentiny/vue-wheel': 3.21.0

  '@opentiny/vue-nav-bar@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-nav-menu@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-tooltip': 3.21.0

  '@opentiny/vue-notify@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-numeric@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-filter-panel': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-radio': 3.21.0
      '@opentiny/vue-radio-group': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-tag': 3.21.0
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-option-group@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-option@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-directive': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-pager-item@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-pager@3.21.0':
    dependencies:
      '@opentiny/vue-base-select': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-loading': 3.21.0
      '@opentiny/vue-pager-item': 3.21.0
      '@opentiny/vue-popover': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-panel@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-picker-column@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-picker@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-date-panel': 3.21.0
      '@opentiny/vue-date-picker-mobile-first': 3.21.0
      '@opentiny/vue-date-range': 3.21.0
      '@opentiny/vue-filter-box': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-month-range': 3.21.0
      '@opentiny/vue-quarter-panel': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-time': 3.21.0
      '@opentiny/vue-time-panel': 3.21.0
      '@opentiny/vue-time-picker-mobile': 3.21.0
      '@opentiny/vue-time-range': 3.21.0
      '@opentiny/vue-tooltip': 3.21.0
      '@opentiny/vue-year-range': 3.21.0

  '@opentiny/vue-pop-upload@3.21.0':
    dependencies:
      '@opentiny/vue-alert': 3.21.0
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-file-upload': 3.21.0
      '@opentiny/vue-modal': 3.21.0
      '@opentiny/vue-popconfirm': 3.21.0
      '@opentiny/vue-progress': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-popconfirm@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-popover': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-popeditor@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-dialog-box': 3.21.0
      '@opentiny/vue-grid': 3.21.2
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-pager': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-selected-box': 3.21.0
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-tree': 3.21.0

  '@opentiny/vue-popover@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-popup@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-progress@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-pull-refresh@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-loading': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-qr-code@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      qrcode: 1.5.1

  '@opentiny/vue-quarter-panel@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-year-table': 3.21.0

  '@opentiny/vue-query-builder@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-checkbox': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-date-picker': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-numeric': 3.21.0
      '@opentiny/vue-option': 3.21.0
      '@opentiny/vue-option-group': 3.21.0
      '@opentiny/vue-radio': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-select': 3.21.0
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-time-picker': 3.21.0

  '@opentiny/vue-radio-button@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-tooltip': 3.21.0

  '@opentiny/vue-radio-group@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-radio': 3.21.0
      '@opentiny/vue-radio-button': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-radio@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-rate@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-record@3.21.0':
    dependencies:
      '@opentiny/vue-action-sheet': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-recycle-scroller@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-renderless@3.21.1':
    dependencies:
      '@opentiny/utils': 1.0.0
      color: 4.2.3

  '@opentiny/vue-river@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      echarts: 5.4.1

  '@opentiny/vue-roles@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-popover': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-row@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-scroll-text@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-scrollbar@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-search@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-select-dropdown@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-search': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-select-mobile@3.21.0':
    dependencies:
      '@opentiny/vue-action-sheet': 3.21.0
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-cell': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-exception': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-select-view@3.21.0':
    dependencies:
      '@opentiny/vue-action-sheet': 3.21.0
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-exception': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-select@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-filter-box': 3.21.0
      '@opentiny/vue-grid': 3.21.2
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-option': 3.21.0
      '@opentiny/vue-recycle-scroller': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-scrollbar': 3.21.0
      '@opentiny/vue-select-dropdown': 3.21.0
      '@opentiny/vue-tag': 3.21.0
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-tooltip': 3.21.0
      '@opentiny/vue-tree': 3.21.0

  '@opentiny/vue-selected-box@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-popover': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-signature@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-modal': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-skeleton-item@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-skeleton@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-skeleton-item': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-slider-button-group@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-slider-button': 3.21.0

  '@opentiny/vue-slider-button@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-slider@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-split@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-standard-list-item@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-dropdown': 3.21.1
      '@opentiny/vue-dropdown-item': 3.21.0
      '@opentiny/vue-dropdown-menu': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-tag-group': 3.21.0
      '@opentiny/vue-tooltip': 3.21.0
      '@opentiny/vue-user-head': 3.21.0

  '@opentiny/vue-statistic@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-steps@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-directive': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-popover': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-sticky@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-switch@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-tab-item@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-tabbar-item@3.21.0':
    dependencies:
      '@opentiny/vue-badge': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-tabbar@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-tabbar-item': 3.21.0
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-table@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-tabs@3.21.0':
    dependencies:
      '@opentiny/vue-carousel': 3.21.0
      '@opentiny/vue-carousel-item': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-dropdown': 3.21.1
      '@opentiny/vue-dropdown-item': 3.21.0
      '@opentiny/vue-dropdown-menu': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-locale': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0
      '@opentiny/vue-tooltip': 3.21.0

  '@opentiny/vue-tag-group@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-popover': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-tag': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-tag@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-text-popup@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-theme-mobile@3.21.0': {}

  '@opentiny/vue-theme@3.21.2': {}

  '@opentiny/vue-time-line-new@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-time-line@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0
      '@opentiny/vue-timeline-item': 3.21.0

  '@opentiny/vue-time-panel@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-scrollbar': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-time-picker-mobile@3.21.0':
    dependencies:
      '@opentiny/vue-cascader-select': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-time-picker@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-picker': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-time-range@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-time-spinner': 3.21.0

  '@opentiny/vue-time-select@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-picker': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-time-spinner@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-scrollbar': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-time@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-time-spinner': 3.21.0

  '@opentiny/vue-timeline-item@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-toast@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-toggle-menu@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-tree': 3.21.0

  '@opentiny/vue-tooltip@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-top-box@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-transfer-panel@3.21.0':
    dependencies:
      '@opentiny/vue-checkbox': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-pager': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-transfer@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-transfer-panel': 3.21.0

  '@opentiny/vue-tree-menu@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-tree': 3.21.0

  '@opentiny/vue-tree-select@3.21.0':
    dependencies:
      '@opentiny/vue-base-select': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-tree': 3.21.0

  '@opentiny/vue-tree@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-checkbox': 3.21.0
      '@opentiny/vue-collapse-transition': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-directive': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-popover': 3.21.0
      '@opentiny/vue-radio': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-switch': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-upload-dragger@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-upload-list@3.21.0':
    dependencies:
      '@opentiny/vue-action-sheet': 3.21.0
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-image-viewer': 3.21.0
      '@opentiny/vue-modal': 3.21.0
      '@opentiny/vue-progress': 3.21.0
      '@opentiny/vue-record': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-tooltip': 3.21.0

  '@opentiny/vue-upload@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-modal': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-tooltip': 3.21.0
      '@opentiny/vue-upload-dragger': 3.21.0

  '@opentiny/vue-user-account@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-logout': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-user-contact': 3.21.0

  '@opentiny/vue-user-contact@3.21.0':
    dependencies:
      '@opentiny/vue-card-template': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-espace': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-popover': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-user-head': 3.21.0

  '@opentiny/vue-user-head-group@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-user-head': 3.21.0

  '@opentiny/vue-user-head@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-user-link@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-loading': 3.21.0
      '@opentiny/vue-popover': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-user@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-option': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-select': 3.21.0
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-virtual-scroll-box@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1

  '@opentiny/vue-virtual-tree@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-tree': 3.21.0
      '@opentiny/vue-virtual-scroll-box': 3.21.0

  '@opentiny/vue-watermark@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue-wheel@3.21.0':
    dependencies:
      '@better-scroll/core': 2.5.0
      '@better-scroll/wheel': 2.5.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme-mobile': 3.21.0

  '@opentiny/vue-wizard@3.21.0':
    dependencies:
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2
      '@opentiny/vue-user-contact': 3.21.0

  '@opentiny/vue-year-range@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-icon': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-year-table': 3.21.0

  '@opentiny/vue-year-table@3.21.0':
    dependencies:
      '@opentiny/vue-common': 3.21.0
      '@opentiny/vue-renderless': 3.21.1
      '@opentiny/vue-theme': 3.21.2

  '@opentiny/vue@3.21.2(echarts@5.4.1)':
    dependencies:
      '@opentiny/vue-action-menu': 3.21.0
      '@opentiny/vue-action-sheet': 3.21.0
      '@opentiny/vue-alert': 3.21.0
      '@opentiny/vue-amount': 3.21.0
      '@opentiny/vue-anchor': 3.21.0
      '@opentiny/vue-area': 3.21.0
      '@opentiny/vue-async-flowchart': 3.21.0
      '@opentiny/vue-autocomplete': 3.21.0
      '@opentiny/vue-avatar': 3.21.0
      '@opentiny/vue-badge': 3.21.0
      '@opentiny/vue-base-select': 3.21.0
      '@opentiny/vue-breadcrumb': 3.21.0
      '@opentiny/vue-breadcrumb-item': 3.21.0
      '@opentiny/vue-bulletin-board': 3.21.0
      '@opentiny/vue-button': 3.21.0
      '@opentiny/vue-button-group': 3.21.0
      '@opentiny/vue-calendar': 3.21.0
      '@opentiny/vue-calendar-bar': 3.21.0
      '@opentiny/vue-calendar-view': 3.21.0
      '@opentiny/vue-card': 3.21.0
      '@opentiny/vue-card-group': 3.21.0
      '@opentiny/vue-card-template': 3.21.0
      '@opentiny/vue-carousel': 3.21.0
      '@opentiny/vue-carousel-item': 3.21.0
      '@opentiny/vue-cascader': 3.21.0
      '@opentiny/vue-cascader-menu': 3.21.0
      '@opentiny/vue-cascader-mobile': 3.21.0
      '@opentiny/vue-cascader-node': 3.21.0
      '@opentiny/vue-cascader-panel': 3.21.0
      '@opentiny/vue-cascader-select': 3.21.0
      '@opentiny/vue-cascader-view': 3.21.0
      '@opentiny/vue-cell': 3.21.0
      '@opentiny/vue-checkbox': 3.21.0
      '@opentiny/vue-checkbox-button': 3.21.0
      '@opentiny/vue-checkbox-group': 3.21.0
      '@opentiny/vue-col': 3.21.0
      '@opentiny/vue-collapse': 3.21.0
      '@opentiny/vue-collapse-item': 3.21.0
      '@opentiny/vue-collapse-transition': 3.21.0
      '@opentiny/vue-color-picker': 3.21.0
      '@opentiny/vue-color-select-panel': 3.21.0
      '@opentiny/vue-column-list-group': 3.21.0
      '@opentiny/vue-column-list-item': 3.21.0
      '@opentiny/vue-company': 3.21.0
      '@opentiny/vue-config-provider': 3.21.0
      '@opentiny/vue-container': 3.21.0
      '@opentiny/vue-country': 3.21.0
      '@opentiny/vue-crop': 3.21.0
      '@opentiny/vue-currency': 3.21.0
      '@opentiny/vue-date-panel': 3.21.0
      '@opentiny/vue-date-picker': 3.21.0
      '@opentiny/vue-date-picker-mobile-first': 3.21.0
      '@opentiny/vue-date-range': 3.21.0
      '@opentiny/vue-date-table': 3.21.0
      '@opentiny/vue-dept': 3.21.0
      '@opentiny/vue-dialog-box': 3.21.0
      '@opentiny/vue-dialog-select': 3.21.0
      '@opentiny/vue-divider': 3.21.0
      '@opentiny/vue-drawer': 3.21.0
      '@opentiny/vue-drop-roles': 3.21.0
      '@opentiny/vue-drop-times': 3.21.0
      '@opentiny/vue-dropdown': 3.21.1
      '@opentiny/vue-dropdown-item': 3.21.0
      '@opentiny/vue-dropdown-menu': 3.21.0
      '@opentiny/vue-dynamic-scroller': 3.21.0
      '@opentiny/vue-dynamic-scroller-item': 3.21.0
      '@opentiny/vue-espace': 3.21.0
      '@opentiny/vue-exception': 3.21.0
      '@opentiny/vue-fall-menu': 3.21.0
      '@opentiny/vue-file-upload': 3.21.0
      '@opentiny/vue-filter': 3.21.0
      '@opentiny/vue-filter-bar': 3.21.0
      '@opentiny/vue-filter-box': 3.21.0
      '@opentiny/vue-filter-panel': 3.21.0
      '@opentiny/vue-float-button': 3.21.0
      '@opentiny/vue-floatbar': 3.21.0
      '@opentiny/vue-floating-button': 3.21.0
      '@opentiny/vue-flowchart': 3.21.0
      '@opentiny/vue-fluent-editor': 3.21.0
      '@opentiny/vue-form': 3.21.0
      '@opentiny/vue-form-item': 3.21.0
      '@opentiny/vue-fullscreen': 3.21.0
      '@opentiny/vue-grid': 3.21.2
      '@opentiny/vue-grid-column': 3.21.0
      '@opentiny/vue-grid-manager': 3.21.0
      '@opentiny/vue-grid-select': 3.21.0
      '@opentiny/vue-grid-toolbar': 3.21.0
      '@opentiny/vue-guide': 3.21.0
      '@opentiny/vue-hrapprover': 3.21.0
      '@opentiny/vue-huicharts': 3.21.0(echarts@5.4.1)
      '@opentiny/vue-huicharts-amap': 3.21.0
      '@opentiny/vue-huicharts-bar': 3.21.0
      '@opentiny/vue-huicharts-bmap': 3.21.0
      '@opentiny/vue-huicharts-boxplot': 3.21.0
      '@opentiny/vue-huicharts-candle': 3.21.0
      '@opentiny/vue-huicharts-core': 3.21.0
      '@opentiny/vue-huicharts-funnel': 3.21.0
      '@opentiny/vue-huicharts-gauge': 3.21.0
      '@opentiny/vue-huicharts-graph': 3.21.0
      '@opentiny/vue-huicharts-heatmap': 3.21.0
      '@opentiny/vue-huicharts-histogram': 3.21.0
      '@opentiny/vue-huicharts-line': 3.21.0
      '@opentiny/vue-huicharts-liquidfill': 3.21.0(echarts@5.4.1)
      '@opentiny/vue-huicharts-map': 3.21.0
      '@opentiny/vue-huicharts-pie': 3.21.0
      '@opentiny/vue-huicharts-process': 3.21.0
      '@opentiny/vue-huicharts-radar': 3.21.0
      '@opentiny/vue-huicharts-ring': 3.21.0
      '@opentiny/vue-huicharts-sankey': 3.21.0
      '@opentiny/vue-huicharts-scatter': 3.21.0
      '@opentiny/vue-huicharts-sunburst': 3.21.0
      '@opentiny/vue-huicharts-tree': 3.21.0
      '@opentiny/vue-huicharts-waterfall': 3.21.0
      '@opentiny/vue-huicharts-wordcloud': 3.21.0(echarts@5.4.1)
      '@opentiny/vue-image': 3.21.0
      '@opentiny/vue-image-viewer': 3.21.0
      '@opentiny/vue-index-bar': 3.21.0
      '@opentiny/vue-index-bar-anchor': 3.21.0
      '@opentiny/vue-input': 3.21.0
      '@opentiny/vue-ip-address': 3.21.0
      '@opentiny/vue-label': 3.21.0
      '@opentiny/vue-layout': 3.21.0
      '@opentiny/vue-link': 3.21.0
      '@opentiny/vue-link-menu': 3.21.0
      '@opentiny/vue-list': 3.21.0
      '@opentiny/vue-load-list': 3.21.0
      '@opentiny/vue-loading': 3.21.0
      '@opentiny/vue-locales': 3.21.0
      '@opentiny/vue-logon-user': 3.21.0
      '@opentiny/vue-logout': 3.21.0
      '@opentiny/vue-mask': 3.21.0
      '@opentiny/vue-menu': 3.21.0
      '@opentiny/vue-message': 3.21.0
      '@opentiny/vue-milestone': 3.21.0
      '@opentiny/vue-mind-map': 3.21.0
      '@opentiny/vue-mini-picker': 3.21.0
      '@opentiny/vue-modal': 3.21.0
      '@opentiny/vue-month-range': 3.21.0
      '@opentiny/vue-month-table': 3.21.0
      '@opentiny/vue-multi-select': 3.21.0
      '@opentiny/vue-multi-select-item': 3.21.0
      '@opentiny/vue-nav-bar': 3.21.0
      '@opentiny/vue-nav-menu': 3.21.0
      '@opentiny/vue-notify': 3.21.0
      '@opentiny/vue-numeric': 3.21.0
      '@opentiny/vue-option': 3.21.0
      '@opentiny/vue-option-group': 3.21.0
      '@opentiny/vue-pager': 3.21.0
      '@opentiny/vue-pager-item': 3.21.0
      '@opentiny/vue-panel': 3.21.0
      '@opentiny/vue-picker': 3.21.0
      '@opentiny/vue-picker-column': 3.21.0
      '@opentiny/vue-pop-upload': 3.21.0
      '@opentiny/vue-popconfirm': 3.21.0
      '@opentiny/vue-popeditor': 3.21.0
      '@opentiny/vue-popover': 3.21.0
      '@opentiny/vue-popup': 3.21.0
      '@opentiny/vue-progress': 3.21.0
      '@opentiny/vue-pull-refresh': 3.21.0
      '@opentiny/vue-qr-code': 3.21.0
      '@opentiny/vue-quarter-panel': 3.21.0
      '@opentiny/vue-query-builder': 3.21.0
      '@opentiny/vue-radio': 3.21.0
      '@opentiny/vue-radio-button': 3.21.0
      '@opentiny/vue-radio-group': 3.21.0
      '@opentiny/vue-rate': 3.21.0
      '@opentiny/vue-record': 3.21.0
      '@opentiny/vue-recycle-scroller': 3.21.0
      '@opentiny/vue-river': 3.21.0
      '@opentiny/vue-roles': 3.21.0
      '@opentiny/vue-row': 3.21.0
      '@opentiny/vue-scroll-text': 3.21.0
      '@opentiny/vue-scrollbar': 3.21.0
      '@opentiny/vue-search': 3.21.0
      '@opentiny/vue-select': 3.21.0
      '@opentiny/vue-select-dropdown': 3.21.0
      '@opentiny/vue-select-mobile': 3.21.0
      '@opentiny/vue-select-view': 3.21.0
      '@opentiny/vue-selected-box': 3.21.0
      '@opentiny/vue-signature': 3.21.0
      '@opentiny/vue-skeleton': 3.21.0
      '@opentiny/vue-skeleton-item': 3.21.0
      '@opentiny/vue-slider': 3.21.0
      '@opentiny/vue-slider-button': 3.21.0
      '@opentiny/vue-slider-button-group': 3.21.0
      '@opentiny/vue-split': 3.21.0
      '@opentiny/vue-standard-list-item': 3.21.0
      '@opentiny/vue-statistic': 3.21.0
      '@opentiny/vue-steps': 3.21.0
      '@opentiny/vue-sticky': 3.21.0
      '@opentiny/vue-switch': 3.21.0
      '@opentiny/vue-tab-item': 3.21.0
      '@opentiny/vue-tabbar': 3.21.0
      '@opentiny/vue-tabbar-item': 3.21.0
      '@opentiny/vue-table': 3.21.0
      '@opentiny/vue-tabs': 3.21.0
      '@opentiny/vue-tag': 3.21.0
      '@opentiny/vue-tag-group': 3.21.0
      '@opentiny/vue-text-popup': 3.21.0
      '@opentiny/vue-time': 3.21.0
      '@opentiny/vue-time-line': 3.21.0
      '@opentiny/vue-time-line-new': 3.21.0
      '@opentiny/vue-time-panel': 3.21.0
      '@opentiny/vue-time-picker': 3.21.0
      '@opentiny/vue-time-picker-mobile': 3.21.0
      '@opentiny/vue-time-range': 3.21.0
      '@opentiny/vue-time-select': 3.21.0
      '@opentiny/vue-time-spinner': 3.21.0
      '@opentiny/vue-timeline-item': 3.21.0
      '@opentiny/vue-toast': 3.21.0
      '@opentiny/vue-toggle-menu': 3.21.0
      '@opentiny/vue-tooltip': 3.21.0
      '@opentiny/vue-top-box': 3.21.0
      '@opentiny/vue-transfer': 3.21.0
      '@opentiny/vue-transfer-panel': 3.21.0
      '@opentiny/vue-tree': 3.21.0
      '@opentiny/vue-tree-menu': 3.21.0
      '@opentiny/vue-tree-select': 3.21.0
      '@opentiny/vue-upload': 3.21.0
      '@opentiny/vue-upload-dragger': 3.21.0
      '@opentiny/vue-upload-list': 3.21.0
      '@opentiny/vue-user': 3.21.0
      '@opentiny/vue-user-account': 3.21.0
      '@opentiny/vue-user-contact': 3.21.0
      '@opentiny/vue-user-head': 3.21.0
      '@opentiny/vue-user-head-group': 3.21.0
      '@opentiny/vue-user-link': 3.21.0
      '@opentiny/vue-virtual-scroll-box': 3.21.0
      '@opentiny/vue-virtual-tree': 3.21.0
      '@opentiny/vue-watermark': 3.21.0
      '@opentiny/vue-wheel': 3.21.0
      '@opentiny/vue-wizard': 3.21.0
      '@opentiny/vue-year-range': 3.21.0
      '@opentiny/vue-year-table': 3.21.0
    transitivePeerDependencies:
      - echarts

  '@parcel/watcher-android-arm64@2.5.1':
    optional: true

  '@parcel/watcher-darwin-arm64@2.5.1':
    optional: true

  '@parcel/watcher-darwin-x64@2.5.1':
    optional: true

  '@parcel/watcher-freebsd-x64@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm-musl@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm64-musl@2.5.1':
    optional: true

  '@parcel/watcher-linux-x64-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-x64-musl@2.5.1':
    optional: true

  '@parcel/watcher-win32-arm64@2.5.1':
    optional: true

  '@parcel/watcher-win32-ia32@2.5.1':
    optional: true

  '@parcel/watcher-win32-x64@2.5.1':
    optional: true

  '@parcel/watcher@2.5.1':
    dependencies:
      detect-libc: 1.0.3
      is-glob: 4.0.3
      micromatch: 4.0.8
      node-addon-api: 7.1.1
    optionalDependencies:
      '@parcel/watcher-android-arm64': 2.5.1
      '@parcel/watcher-darwin-arm64': 2.5.1
      '@parcel/watcher-darwin-x64': 2.5.1
      '@parcel/watcher-freebsd-x64': 2.5.1
      '@parcel/watcher-linux-arm-glibc': 2.5.1
      '@parcel/watcher-linux-arm-musl': 2.5.1
      '@parcel/watcher-linux-arm64-glibc': 2.5.1
      '@parcel/watcher-linux-arm64-musl': 2.5.1
      '@parcel/watcher-linux-x64-glibc': 2.5.1
      '@parcel/watcher-linux-x64-musl': 2.5.1
      '@parcel/watcher-win32-arm64': 2.5.1
      '@parcel/watcher-win32-ia32': 2.5.1
      '@parcel/watcher-win32-x64': 2.5.1
    optional: true

  '@rollup/pluginutils@5.1.4(rollup@4.34.6)':
    dependencies:
      '@types/estree': 1.0.6
      estree-walker: 2.0.2
      picomatch: 4.0.2
    optionalDependencies:
      rollup: 4.34.6

  '@rollup/rollup-android-arm-eabi@4.34.6':
    optional: true

  '@rollup/rollup-android-arm64@4.34.6':
    optional: true

  '@rollup/rollup-darwin-arm64@4.34.6':
    optional: true

  '@rollup/rollup-darwin-x64@4.34.6':
    optional: true

  '@rollup/rollup-freebsd-arm64@4.34.6':
    optional: true

  '@rollup/rollup-freebsd-x64@4.34.6':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.34.6':
    optional: true

  '@rollup/rollup-linux-arm-musleabihf@4.34.6':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.34.6':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.34.6':
    optional: true

  '@rollup/rollup-linux-loongarch64-gnu@4.34.6':
    optional: true

  '@rollup/rollup-linux-powerpc64le-gnu@4.34.6':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.34.6':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.34.6':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.34.6':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.34.6':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.34.6':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.34.6':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.34.6':
    optional: true

  '@trysound/sax@0.2.0': {}

  '@types/echarts@4.9.22':
    dependencies:
      '@types/zrender': 4.0.6

  '@types/estree@1.0.6': {}

  '@types/gensync@1.0.4': {}

  '@types/json-schema@7.0.15': {}

  '@types/node@22.13.1':
    dependencies:
      undici-types: 6.20.0

  '@types/vue@2.0.0(typescript@5.7.3)':
    dependencies:
      vue: 3.5.13(typescript@5.7.3)
    transitivePeerDependencies:
      - typescript

  '@types/zrender@4.0.6': {}

  '@typescript-eslint/eslint-plugin@4.33.0(@typescript-eslint/parser@4.33.0(eslint@7.32.0)(typescript@5.7.3))(eslint@7.32.0)(typescript@5.7.3)':
    dependencies:
      '@typescript-eslint/experimental-utils': 4.33.0(eslint@7.32.0)(typescript@5.7.3)
      '@typescript-eslint/parser': 4.33.0(eslint@7.32.0)(typescript@5.7.3)
      '@typescript-eslint/scope-manager': 4.33.0
      debug: 4.4.0
      eslint: 7.32.0
      functional-red-black-tree: 1.0.1
      ignore: 5.3.2
      regexpp: 3.2.0
      semver: 7.7.1
      tsutils: 3.21.0(typescript@5.7.3)
    optionalDependencies:
      typescript: 5.7.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/experimental-utils@4.33.0(eslint@7.32.0)(typescript@5.7.3)':
    dependencies:
      '@types/json-schema': 7.0.15
      '@typescript-eslint/scope-manager': 4.33.0
      '@typescript-eslint/types': 4.33.0
      '@typescript-eslint/typescript-estree': 4.33.0(typescript@5.7.3)
      eslint: 7.32.0
      eslint-scope: 5.1.1
      eslint-utils: 3.0.0(eslint@7.32.0)
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@typescript-eslint/parser@4.33.0(eslint@7.32.0)(typescript@5.7.3)':
    dependencies:
      '@typescript-eslint/scope-manager': 4.33.0
      '@typescript-eslint/types': 4.33.0
      '@typescript-eslint/typescript-estree': 4.33.0(typescript@5.7.3)
      debug: 4.4.0
      eslint: 7.32.0
    optionalDependencies:
      typescript: 5.7.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@4.33.0':
    dependencies:
      '@typescript-eslint/types': 4.33.0
      '@typescript-eslint/visitor-keys': 4.33.0

  '@typescript-eslint/types@4.33.0': {}

  '@typescript-eslint/typescript-estree@4.33.0(typescript@5.7.3)':
    dependencies:
      '@typescript-eslint/types': 4.33.0
      '@typescript-eslint/visitor-keys': 4.33.0
      debug: 4.4.0
      globby: 11.1.0
      is-glob: 4.0.3
      semver: 7.7.1
      tsutils: 3.21.0(typescript@5.7.3)
    optionalDependencies:
      typescript: 5.7.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/visitor-keys@4.33.0':
    dependencies:
      '@typescript-eslint/types': 4.33.0
      eslint-visitor-keys: 2.1.0

  '@varlet/axle@0.7.1(vue@3.5.13(typescript@5.7.3))':
    dependencies:
      '@varlet/shared': 2.20.3
      axios: 1.7.9
      crypto-js: 4.2.0
      lodash: 4.17.21
      minimatch: 9.0.5
      qs: 6.14.0
      vue: 3.5.13(typescript@5.7.3)
    transitivePeerDependencies:
      - debug

  '@varlet/shared@2.20.3': {}

  '@vexip-ui/hooks@2.7.0(vue@3.5.13(typescript@5.7.3))':
    dependencies:
      '@floating-ui/dom': 1.6.13
      '@juggle/resize-observer': 3.4.0
      '@vexip-ui/utils': 2.16.1
      vue: 3.5.13(typescript@5.7.3)

  '@vexip-ui/utils@2.16.1': {}

  '@vitejs/plugin-vue-jsx@4.1.1(vite@5.4.14(@types/node@22.13.1)(less@4.2.2)(sass@1.84.0))(vue@3.5.13(typescript@5.7.3))':
    dependencies:
      '@babel/core': 7.26.8
      '@babel/plugin-transform-typescript': 7.26.8(@babel/core@7.26.8)
      '@vue/babel-plugin-jsx': 1.2.5(@babel/core@7.26.8)
      vite: 5.4.14(@types/node@22.13.1)(less@4.2.2)(sass@1.84.0)
      vue: 3.5.13(typescript@5.7.3)
    transitivePeerDependencies:
      - supports-color

  '@vitejs/plugin-vue@5.2.1(vite@5.4.14(@types/node@22.13.1)(less@4.2.2)(sass@1.84.0))(vue@3.5.13(typescript@5.7.3))':
    dependencies:
      vite: 5.4.14(@types/node@22.13.1)(less@4.2.2)(sass@1.84.0)
      vue: 3.5.13(typescript@5.7.3)

  '@volar/language-core@2.4.11':
    dependencies:
      '@volar/source-map': 2.4.11

  '@volar/source-map@2.4.11': {}

  '@volar/typescript@2.4.11':
    dependencies:
      '@volar/language-core': 2.4.11
      path-browserify: 1.0.1
      vscode-uri: 3.1.0

  '@vue/babel-helper-vue-transform-on@1.2.5': {}

  '@vue/babel-plugin-jsx@1.2.5(@babel/core@7.26.8)':
    dependencies:
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/plugin-syntax-jsx': 7.25.9(@babel/core@7.26.8)
      '@babel/template': 7.26.8
      '@babel/traverse': 7.26.8
      '@babel/types': 7.26.8
      '@vue/babel-helper-vue-transform-on': 1.2.5
      '@vue/babel-plugin-resolve-type': 1.2.5(@babel/core@7.26.8)
      html-tags: 3.3.1
      svg-tags: 1.0.0
    optionalDependencies:
      '@babel/core': 7.26.8
    transitivePeerDependencies:
      - supports-color

  '@vue/babel-plugin-resolve-type@1.2.5(@babel/core@7.26.8)':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/core': 7.26.8
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/parser': 7.26.8
      '@vue/compiler-sfc': 3.5.13
    transitivePeerDependencies:
      - supports-color

  '@vue/compiler-core@3.5.13':
    dependencies:
      '@babel/parser': 7.26.8
      '@vue/shared': 3.5.13
      entities: 4.5.0
      estree-walker: 2.0.2
      source-map-js: 1.2.1

  '@vue/compiler-dom@3.5.13':
    dependencies:
      '@vue/compiler-core': 3.5.13
      '@vue/shared': 3.5.13

  '@vue/compiler-sfc@3.5.13':
    dependencies:
      '@babel/parser': 7.26.8
      '@vue/compiler-core': 3.5.13
      '@vue/compiler-dom': 3.5.13
      '@vue/compiler-ssr': 3.5.13
      '@vue/shared': 3.5.13
      estree-walker: 2.0.2
      magic-string: 0.30.17
      postcss: 8.5.2
      source-map-js: 1.2.1

  '@vue/compiler-ssr@3.5.13':
    dependencies:
      '@vue/compiler-dom': 3.5.13
      '@vue/shared': 3.5.13

  '@vue/compiler-vue2@2.7.16':
    dependencies:
      de-indent: 1.0.2
      he: 1.2.0

  '@vue/devtools-api@6.6.4': {}

  '@vue/language-core@2.2.0(typescript@5.7.3)':
    dependencies:
      '@volar/language-core': 2.4.11
      '@vue/compiler-dom': 3.5.13
      '@vue/compiler-vue2': 2.7.16
      '@vue/shared': 3.5.13
      alien-signals: 0.4.14
      minimatch: 9.0.5
      muggle-string: 0.4.1
      path-browserify: 1.0.1
    optionalDependencies:
      typescript: 5.7.3

  '@vue/reactivity@3.5.13':
    dependencies:
      '@vue/shared': 3.5.13

  '@vue/runtime-core@3.5.13':
    dependencies:
      '@vue/reactivity': 3.5.13
      '@vue/shared': 3.5.13

  '@vue/runtime-dom@3.5.13':
    dependencies:
      '@vue/reactivity': 3.5.13
      '@vue/runtime-core': 3.5.13
      '@vue/shared': 3.5.13
      csstype: 3.1.3

  '@vue/server-renderer@3.5.13(vue@3.5.13(typescript@5.7.3))':
    dependencies:
      '@vue/compiler-ssr': 3.5.13
      '@vue/shared': 3.5.13
      vue: 3.5.13(typescript@5.7.3)

  '@vue/shared@3.5.13': {}

  '@vxe-ui/core@4.0.29(vue@3.5.13(typescript@5.7.3))':
    dependencies:
      dom-zindex: 1.0.6
      vue: 3.5.13(typescript@5.7.3)
      xe-utils: 3.7.0

  '@vxe-ui/plugin-export-xlsx@4.0.13': {}

  '@vxe-ui/plugin-menu@4.0.7': {}

  '@vxe-ui/plugin-render-wangeditor@4.0.3': {}

  acorn-jsx@5.3.2(acorn@7.4.1):
    dependencies:
      acorn: 7.4.1

  acorn@7.4.1: {}

  acorn@8.14.0: {}

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.17.1:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  alien-signals@0.4.14: {}

  ansi-colors@4.1.3: {}

  ansi-regex@5.0.1: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  argparse@1.0.10:
    dependencies:
      sprintf-js: 1.0.3

  array-union@2.1.0: {}

  astral-regex@2.0.0: {}

  asynckit@0.4.0: {}

  axios@1.7.9:
    dependencies:
      follow-redirects: 1.15.9
      form-data: 4.0.1
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  balanced-match@1.0.2: {}

  binary-extensions@2.3.0: {}

  boolbase@1.0.0: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browserslist@4.24.4:
    dependencies:
      caniuse-lite: 1.0.30001699
      electron-to-chromium: 1.5.97
      node-releases: 2.0.19
      update-browserslist-db: 1.1.2(browserslist@4.24.4)

  call-bind-apply-helpers@1.0.1:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bound@1.0.3:
    dependencies:
      call-bind-apply-helpers: 1.0.1
      get-intrinsic: 1.2.7

  callsites@3.1.0: {}

  camelcase@5.3.1: {}

  caniuse-lite@1.0.30001699: {}

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chokidar@4.0.3:
    dependencies:
      readdirp: 4.1.1

  cliui@6.0.0:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 6.2.0

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  color-string@1.9.1:
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2

  color@4.2.3:
    dependencies:
      color-convert: 2.0.1
      color-string: 1.9.1

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@2.20.3: {}

  commander@7.2.0: {}

  concat-map@0.0.1: {}

  confbox@0.1.8: {}

  convert-source-map@2.0.0: {}

  copy-anything@2.0.6:
    dependencies:
      is-what: 3.14.1

  cropperjs@1.5.7: {}

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  crypto-js@4.2.0: {}

  css-select@5.1.0:
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 5.0.3
      domutils: 3.2.2
      nth-check: 2.1.1

  css-tree@2.2.1:
    dependencies:
      mdn-data: 2.0.28
      source-map-js: 1.2.1

  css-tree@2.3.1:
    dependencies:
      mdn-data: 2.0.30
      source-map-js: 1.2.1

  css-what@6.1.0: {}

  cssfilter@0.0.10: {}

  csso@5.0.5:
    dependencies:
      css-tree: 2.2.1

  csstype@3.1.3: {}

  dayjs@1.11.13: {}

  de-indent@1.0.2: {}

  debug@4.4.0:
    dependencies:
      ms: 2.1.3

  decamelize@1.2.0: {}

  deep-is@0.1.4: {}

  deepmerge@4.3.1: {}

  delayed-stream@1.0.0: {}

  detect-libc@1.0.3:
    optional: true

  dijkstrajs@1.0.3: {}

  dir-glob@3.0.1:
    dependencies:
      path-type: 4.0.0

  doctrine@3.0.0:
    dependencies:
      esutils: 2.0.3

  dom-serializer@2.0.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      entities: 4.5.0

  dom-zindex@1.0.6: {}

  domelementtype@2.3.0: {}

  domhandler@5.0.3:
    dependencies:
      domelementtype: 2.3.0

  domutils@3.2.2:
    dependencies:
      dom-serializer: 2.0.0
      domelementtype: 2.3.0
      domhandler: 5.0.3

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  dx-ui-var@0.0.1-beta.4: {}

  echarts-liquidfill@3.1.0(echarts@5.4.1):
    dependencies:
      echarts: 5.4.1

  echarts-wordcloud@2.0.0(echarts@5.4.1):
    dependencies:
      echarts: 5.4.1

  echarts@5.4.1:
    dependencies:
      tslib: 2.3.0
      zrender: 5.4.1

  electron-to-chromium@1.5.97: {}

  emoji-regex@8.0.0: {}

  encode-utf8@1.0.3: {}

  enquirer@2.4.1:
    dependencies:
      ansi-colors: 4.1.3
      strip-ansi: 6.0.1

  entities@4.5.0: {}

  errno@0.1.8:
    dependencies:
      prr: 1.0.1
    optional: true

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-module-lexer@1.6.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  esbuild@0.21.5:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.21.5
      '@esbuild/android-arm': 0.21.5
      '@esbuild/android-arm64': 0.21.5
      '@esbuild/android-x64': 0.21.5
      '@esbuild/darwin-arm64': 0.21.5
      '@esbuild/darwin-x64': 0.21.5
      '@esbuild/freebsd-arm64': 0.21.5
      '@esbuild/freebsd-x64': 0.21.5
      '@esbuild/linux-arm': 0.21.5
      '@esbuild/linux-arm64': 0.21.5
      '@esbuild/linux-ia32': 0.21.5
      '@esbuild/linux-loong64': 0.21.5
      '@esbuild/linux-mips64el': 0.21.5
      '@esbuild/linux-ppc64': 0.21.5
      '@esbuild/linux-riscv64': 0.21.5
      '@esbuild/linux-s390x': 0.21.5
      '@esbuild/linux-x64': 0.21.5
      '@esbuild/netbsd-x64': 0.21.5
      '@esbuild/openbsd-x64': 0.21.5
      '@esbuild/sunos-x64': 0.21.5
      '@esbuild/win32-arm64': 0.21.5
      '@esbuild/win32-ia32': 0.21.5
      '@esbuild/win32-x64': 0.21.5

  escalade@3.2.0: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@4.0.0: {}

  escape-string-regexp@5.0.0: {}

  eslint-scope@5.1.1:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  eslint-utils@2.1.0:
    dependencies:
      eslint-visitor-keys: 1.3.0

  eslint-utils@3.0.0(eslint@7.32.0):
    dependencies:
      eslint: 7.32.0
      eslint-visitor-keys: 2.1.0

  eslint-visitor-keys@1.3.0: {}

  eslint-visitor-keys@2.1.0: {}

  eslint@7.32.0:
    dependencies:
      '@babel/code-frame': 7.12.11
      '@eslint/eslintrc': 0.4.3
      '@humanwhocodes/config-array': 0.5.0
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.0
      doctrine: 3.0.0
      enquirer: 2.4.1
      escape-string-regexp: 4.0.0
      eslint-scope: 5.1.1
      eslint-utils: 2.1.0
      eslint-visitor-keys: 2.1.0
      espree: 7.3.1
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      functional-red-black-tree: 1.0.1
      glob-parent: 5.1.2
      globals: 13.24.0
      ignore: 4.0.6
      import-fresh: 3.3.1
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      js-yaml: 3.14.1
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
      progress: 2.0.3
      regexpp: 3.2.0
      semver: 7.7.1
      strip-ansi: 6.0.1
      strip-json-comments: 3.1.1
      table: 6.9.0
      text-table: 0.2.0
      v8-compile-cache: 2.4.0
    transitivePeerDependencies:
      - supports-color

  espree@7.3.1:
    dependencies:
      acorn: 7.4.1
      acorn-jsx: 5.3.2(acorn@7.4.1)
      eslint-visitor-keys: 1.3.0

  esprima@4.0.1: {}

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@4.3.0: {}

  estraverse@5.3.0: {}

  estree-walker@2.0.2: {}

  estree-walker@3.0.3:
    dependencies:
      '@types/estree': 1.0.6

  esutils@2.0.3: {}

  eventemitter3@5.0.1: {}

  fast-deep-equal@3.1.3: {}

  fast-diff@1.3.0: {}

  fast-glob@3.3.3:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-uri@3.0.6: {}

  fastq@1.19.0:
    dependencies:
      reusify: 1.0.4

  file-entry-cache@6.0.1:
    dependencies:
      flat-cache: 3.2.0

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  flat-cache@3.2.0:
    dependencies:
      flatted: 3.3.2
      keyv: 4.5.4
      rimraf: 3.0.2

  flatted@3.3.2: {}

  follow-redirects@1.15.9: {}

  form-data@4.0.1:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  functional-red-black-tree@1.0.1: {}

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-intrinsic@1.2.7:
    dependencies:
      call-bind-apply-helpers: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  globals@11.12.0: {}

  globals@13.24.0:
    dependencies:
      type-fest: 0.20.2

  globby@11.1.0:
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.3
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 3.0.0

  gopd@1.2.0: {}

  graceful-fs@4.2.11:
    optional: true

  grid-layout-plus@1.0.6(vue@3.5.13(typescript@5.7.3)):
    dependencies:
      '@vexip-ui/hooks': 2.7.0(vue@3.5.13(typescript@5.7.3))
      '@vexip-ui/utils': 2.16.1
      interactjs: 1.10.27
      vue: 3.5.13(typescript@5.7.3)

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-symbols@1.1.0: {}

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  he@1.2.0: {}

  html-tags@3.3.1: {}

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2
    optional: true

  ignore@4.0.6: {}

  ignore@5.3.2: {}

  image-size@0.5.5:
    optional: true

  immutable@5.0.3: {}

  import-fresh@3.3.1:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  imurmurhash@0.1.4: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  interactjs@1.10.27:
    dependencies:
      '@interactjs/types': 1.10.27

  is-arrayish@0.3.2: {}

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-extglob@2.1.1: {}

  is-fullwidth-code-point@3.0.0: {}

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-number@7.0.0: {}

  is-what@3.14.1: {}

  isexe@2.0.0: {}

  js-tokens@4.0.0: {}

  js-tokens@9.0.1: {}

  js-yaml@3.14.1:
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1

  jsesc@3.1.0: {}

  json-buffer@3.0.1: {}

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json5@2.2.3: {}

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  less@4.2.2:
    dependencies:
      copy-anything: 2.0.6
      parse-node-version: 1.0.1
      tslib: 2.8.1
    optionalDependencies:
      errno: 0.1.8
      graceful-fs: 4.2.11
      image-size: 0.5.5
      make-dir: 2.1.0
      mime: 1.6.0
      needle: 3.3.1
      source-map: 0.6.1

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  local-pkg@0.4.3: {}

  local-pkg@0.5.1:
    dependencies:
      mlly: 1.7.4
      pkg-types: 1.3.1

  local-pkg@1.0.0:
    dependencies:
      mlly: 1.7.4
      pkg-types: 1.3.1

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  lodash-es@4.17.21: {}

  lodash.clonedeep@4.5.0: {}

  lodash.isequal@4.5.0: {}

  lodash.merge@4.6.2: {}

  lodash.truncate@4.4.2: {}

  lodash@4.17.21: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  magic-string@0.30.17:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.0

  make-dir@2.1.0:
    dependencies:
      pify: 4.0.1
      semver: 5.7.2
    optional: true

  math-intrinsics@1.1.0: {}

  mdn-data@2.0.28: {}

  mdn-data@2.0.30: {}

  merge2@1.4.1: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.6.0:
    optional: true

  mind-elixir@3.3.2: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.1

  mitt@3.0.1: {}

  mlly@1.7.4:
    dependencies:
      acorn: 8.14.0
      pathe: 2.0.2
      pkg-types: 1.3.1
      ufo: 1.5.4

  ms@2.1.3: {}

  muggle-string@0.4.1: {}

  nanoid@3.3.8: {}

  natural-compare@1.4.0: {}

  needle@3.3.1:
    dependencies:
      iconv-lite: 0.6.3
      sax: 1.4.1
    optional: true

  node-addon-api@7.1.1:
    optional: true

  node-releases@2.0.19: {}

  normalize-path@3.0.0: {}

  nprogress@0.2.0: {}

  nth-check@2.1.1:
    dependencies:
      boolbase: 1.0.0

  object-inspect@1.13.4: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-try@2.2.0: {}

  parchment@3.0.0: {}

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-node-version@1.0.1: {}

  path-browserify@1.0.1: {}

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  path-type@4.0.0: {}

  pathe@2.0.2: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  picomatch@4.0.2: {}

  pify@4.0.1:
    optional: true

  pinia-plugin-persistedstate@3.2.3(pinia@2.3.1(typescript@5.7.3)(vue@3.5.13(typescript@5.7.3))):
    dependencies:
      pinia: 2.3.1(typescript@5.7.3)(vue@3.5.13(typescript@5.7.3))

  pinia@2.3.1(typescript@5.7.3)(vue@3.5.13(typescript@5.7.3)):
    dependencies:
      '@vue/devtools-api': 6.6.4
      vue: 3.5.13(typescript@5.7.3)
      vue-demi: 0.14.10(vue@3.5.13(typescript@5.7.3))
    optionalDependencies:
      typescript: 5.7.3
    transitivePeerDependencies:
      - '@vue/composition-api'

  pkg-types@1.3.1:
    dependencies:
      confbox: 0.1.8
      mlly: 1.7.4
      pathe: 2.0.2

  pngjs@5.0.0: {}

  postcss@8.5.2:
    dependencies:
      nanoid: 3.3.8
      picocolors: 1.1.1
      source-map-js: 1.2.1

  prelude-ls@1.2.1: {}

  progress@2.0.3: {}

  proxy-from-env@1.1.0: {}

  prr@1.0.1:
    optional: true

  punycode@2.3.1: {}

  qrcode@1.5.1:
    dependencies:
      dijkstrajs: 1.0.3
      encode-utf8: 1.0.3
      pngjs: 5.0.0
      yargs: 15.4.1

  qs@6.14.0:
    dependencies:
      side-channel: 1.1.0

  queue-microtask@1.2.3: {}

  quill-delta@5.1.0:
    dependencies:
      fast-diff: 1.3.0
      lodash.clonedeep: 4.5.0
      lodash.isequal: 4.5.0

  quill@2.0.3:
    dependencies:
      eventemitter3: 5.0.1
      lodash-es: 4.17.21
      parchment: 3.0.0
      quill-delta: 5.1.0

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  readdirp@4.1.1: {}

  regexpp@3.2.0: {}

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  require-main-filename@2.0.0: {}

  resolve-from@4.0.0: {}

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  reusify@1.0.4: {}

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3

  rollup@4.34.6:
    dependencies:
      '@types/estree': 1.0.6
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.34.6
      '@rollup/rollup-android-arm64': 4.34.6
      '@rollup/rollup-darwin-arm64': 4.34.6
      '@rollup/rollup-darwin-x64': 4.34.6
      '@rollup/rollup-freebsd-arm64': 4.34.6
      '@rollup/rollup-freebsd-x64': 4.34.6
      '@rollup/rollup-linux-arm-gnueabihf': 4.34.6
      '@rollup/rollup-linux-arm-musleabihf': 4.34.6
      '@rollup/rollup-linux-arm64-gnu': 4.34.6
      '@rollup/rollup-linux-arm64-musl': 4.34.6
      '@rollup/rollup-linux-loongarch64-gnu': 4.34.6
      '@rollup/rollup-linux-powerpc64le-gnu': 4.34.6
      '@rollup/rollup-linux-riscv64-gnu': 4.34.6
      '@rollup/rollup-linux-s390x-gnu': 4.34.6
      '@rollup/rollup-linux-x64-gnu': 4.34.6
      '@rollup/rollup-linux-x64-musl': 4.34.6
      '@rollup/rollup-win32-arm64-msvc': 4.34.6
      '@rollup/rollup-win32-ia32-msvc': 4.34.6
      '@rollup/rollup-win32-x64-msvc': 4.34.6
      fsevents: 2.3.3

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  safer-buffer@2.1.2:
    optional: true

  sass@1.84.0:
    dependencies:
      chokidar: 4.0.3
      immutable: 5.0.3
      source-map-js: 1.2.1
    optionalDependencies:
      '@parcel/watcher': 2.5.1

  sax@1.4.1:
    optional: true

  scule@1.3.0: {}

  semver@5.7.2:
    optional: true

  semver@6.3.1: {}

  semver@7.7.1: {}

  set-blocking@2.0.0: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  shepherd.js@11.1.1:
    dependencies:
      '@floating-ui/dom': 1.6.13
      deepmerge: 4.3.1

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.7
      object-inspect: 1.13.4

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.7
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  simple-swizzle@0.2.2:
    dependencies:
      is-arrayish: 0.3.2

  slash@3.0.0: {}

  slice-ansi@4.0.0:
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0

  source-map-js@1.2.1: {}

  source-map@0.6.1:
    optional: true

  sprintf-js@1.0.3: {}

  streamsaver@2.0.6: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-json-comments@3.1.1: {}

  strip-literal@2.1.1:
    dependencies:
      js-tokens: 9.0.1

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  svg-tags@1.0.0: {}

  svgo@3.3.2:
    dependencies:
      '@trysound/sax': 0.2.0
      commander: 7.2.0
      css-select: 5.1.0
      css-tree: 2.3.1
      css-what: 6.1.0
      csso: 5.0.5
      picocolors: 1.1.1

  table@6.9.0:
    dependencies:
      ajv: 8.17.1
      lodash.truncate: 4.4.2
      slice-ansi: 4.0.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  tailwind-merge@1.14.0: {}

  tar-mini@0.2.0: {}

  text-table@0.2.0: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  tslib@1.14.1: {}

  tslib@2.3.0: {}

  tslib@2.8.1: {}

  tsutils@3.21.0(typescript@5.7.3):
    dependencies:
      tslib: 1.14.1
      typescript: 5.7.3

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-fest@0.20.2: {}

  typescript@5.7.3: {}

  ufo@1.5.4: {}

  undici-types@6.20.0: {}

  unimport@3.14.6(rollup@4.34.6):
    dependencies:
      '@rollup/pluginutils': 5.1.4(rollup@4.34.6)
      acorn: 8.14.0
      escape-string-regexp: 5.0.0
      estree-walker: 3.0.3
      fast-glob: 3.3.3
      local-pkg: 1.0.0
      magic-string: 0.30.17
      mlly: 1.7.4
      pathe: 2.0.2
      picomatch: 4.0.2
      pkg-types: 1.3.1
      scule: 1.3.0
      strip-literal: 2.1.1
      unplugin: 1.16.1
    transitivePeerDependencies:
      - rollup

  unplugin-auto-import@0.17.8(rollup@4.34.6):
    dependencies:
      '@antfu/utils': 0.7.10
      '@rollup/pluginutils': 5.1.4(rollup@4.34.6)
      fast-glob: 3.3.3
      local-pkg: 0.5.1
      magic-string: 0.30.17
      minimatch: 9.0.5
      unimport: 3.14.6(rollup@4.34.6)
      unplugin: 1.16.1
    transitivePeerDependencies:
      - rollup

  unplugin-vue-components@0.26.0(@babel/parser@7.26.8)(rollup@4.34.6)(vue@3.5.13(typescript@5.7.3)):
    dependencies:
      '@antfu/utils': 0.7.10
      '@rollup/pluginutils': 5.1.4(rollup@4.34.6)
      chokidar: 3.6.0
      debug: 4.4.0
      fast-glob: 3.3.3
      local-pkg: 0.4.3
      magic-string: 0.30.17
      minimatch: 9.0.5
      resolve: 1.22.10
      unplugin: 1.16.1
      vue: 3.5.13(typescript@5.7.3)
    optionalDependencies:
      '@babel/parser': 7.26.8
    transitivePeerDependencies:
      - rollup
      - supports-color

  unplugin-vue-components@0.27.5(@babel/parser@7.26.8)(rollup@4.34.6)(vue@3.5.13(typescript@5.7.3)):
    dependencies:
      '@antfu/utils': 0.7.10
      '@rollup/pluginutils': 5.1.4(rollup@4.34.6)
      chokidar: 3.6.0
      debug: 4.4.0
      fast-glob: 3.3.3
      local-pkg: 0.5.1
      magic-string: 0.30.17
      minimatch: 9.0.5
      mlly: 1.7.4
      unplugin: 1.16.1
      vue: 3.5.13(typescript@5.7.3)
    optionalDependencies:
      '@babel/parser': 7.26.8
    transitivePeerDependencies:
      - rollup
      - supports-color

  unplugin@1.16.1:
    dependencies:
      acorn: 8.14.0
      webpack-virtual-modules: 0.6.2

  update-browserslist-db@1.1.2(browserslist@4.24.4):
    dependencies:
      browserslist: 4.24.4
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  v8-compile-cache@2.4.0: {}

  vite-plugin-compression2@1.3.3(rollup@4.34.6)(vite@5.4.14(@types/node@22.13.1)(less@4.2.2)(sass@1.84.0)):
    dependencies:
      '@rollup/pluginutils': 5.1.4(rollup@4.34.6)
      tar-mini: 0.2.0
      vite: 5.4.14(@types/node@22.13.1)(less@4.2.2)(sass@1.84.0)
    transitivePeerDependencies:
      - rollup

  vite-plugin-lazy-import@1.0.7:
    dependencies:
      '@rollup/pluginutils': 5.1.4(rollup@4.34.6)
      es-module-lexer: 1.6.0
      rollup: 4.34.6
      xe-utils: 3.7.0

  vite-svg-loader@5.1.0(vue@3.5.13(typescript@5.7.3)):
    dependencies:
      svgo: 3.3.2
      vue: 3.5.13(typescript@5.7.3)

  vite@5.4.14(@types/node@22.13.1)(less@4.2.2)(sass@1.84.0):
    dependencies:
      esbuild: 0.21.5
      postcss: 8.5.2
      rollup: 4.34.6
    optionalDependencies:
      '@types/node': 22.13.1
      fsevents: 2.3.3
      less: 4.2.2
      sass: 1.84.0

  vscode-uri@3.1.0: {}

  vue-demi@0.14.10(vue@3.5.13(typescript@5.7.3)):
    dependencies:
      vue: 3.5.13(typescript@5.7.3)

  vue-router@4.5.0(vue@3.5.13(typescript@5.7.3)):
    dependencies:
      '@vue/devtools-api': 6.6.4
      vue: 3.5.13(typescript@5.7.3)

  vue-tsc@2.2.0(typescript@5.7.3):
    dependencies:
      '@volar/typescript': 2.4.11
      '@vue/language-core': 2.2.0(typescript@5.7.3)
      typescript: 5.7.3

  vue@3.5.13(typescript@5.7.3):
    dependencies:
      '@vue/compiler-dom': 3.5.13
      '@vue/compiler-sfc': 3.5.13
      '@vue/runtime-dom': 3.5.13
      '@vue/server-renderer': 3.5.13(vue@3.5.13(typescript@5.7.3))
      '@vue/shared': 3.5.13
    optionalDependencies:
      typescript: 5.7.3

  vxe-pc-ui@4.3.50(vue@3.5.13(typescript@5.7.3)):
    dependencies:
      '@vxe-ui/core': 4.0.29(vue@3.5.13(typescript@5.7.3))
    transitivePeerDependencies:
      - vue

  vxe-table@4.9.19(vue@3.5.13(typescript@5.7.3)):
    dependencies:
      vxe-pc-ui: 4.3.50(vue@3.5.13(typescript@5.7.3))
    transitivePeerDependencies:
      - vue

  webpack-virtual-modules@0.6.2: {}

  which-module@2.0.1: {}

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  word-wrap@1.2.5: {}

  workerpool@9.2.0: {}

  wrap-ansi@6.2.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrappy@1.0.2: {}

  xe-utils@3.7.0: {}

  xss@1.0.14:
    dependencies:
      commander: 2.20.3
      cssfilter: 0.0.10

  y18n@4.0.3: {}

  yallist@3.1.1: {}

  yargs-parser@18.1.3:
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0

  yargs@15.4.1:
    dependencies:
      cliui: 6.0.0
      decamelize: 1.2.0
      find-up: 4.1.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      require-main-filename: 2.0.0
      set-blocking: 2.0.0
      string-width: 4.2.3
      which-module: 2.0.1
      y18n: 4.0.3
      yargs-parser: 18.1.3

  zrender@5.4.1:
    dependencies:
      tslib: 2.3.0
