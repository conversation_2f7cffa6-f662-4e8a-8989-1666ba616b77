<!--
* Description: tab标签
* Created by huqingchao
* Date: 2024/10/29 18:18
* Update: 2024/10/29 18:18
-->
<template>
  <div class="tags-views">
    <vxe-tabs
      v-model="activeName"
      type="round-card"
      :height="140"
      :options="saveList"
      :close-config="closeConfig"
      @tab-click="handleClick"
      @tab-close="tabCloseEvent">
    </vxe-tabs>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

// 当前激活tab
const activeName = ref('')
// tab数组
const saveList = ref([])
const names = computed(() => {
  return saveList.value.map(item => item.name)
})
watch(route, newVal => {
  activeName.value = newVal.name
  if (!names.value.includes(newVal.name)) {
    saveList.value.push({
      name: newVal.name,
      title: newVal.meta.title,
    })
  }
}, { immediate: true })

// 点击事件
const handleClick = tab => {
  router.push({ name: tab.name })
}
// 关闭前事件
const closeConfig = ref({
  enabled: true,
  beforeMethod({ name }) {
    return saveList.value.length !== 1;
  }
})
// 关闭事件
const tabCloseEvent = ({ name, nextName }) => {
  saveList.value = saveList.value.filter(item => item.name !== name)
  activeName.value = nextName
  router.push({ name: activeName.value })
}
</script>

<style lang="less" scoped>
.tags-views {
  height: var(--dx-ui-tab-height);
  overflow: hidden;
  font-size: var(--dx-ui-tab-font-size);
  :deep(.vxe-tabs--round-card){
    .vxe-tabs-header--item.is--active{
      background: none;
    }
    .vxe-tabs-header--item{
      border-radius: 0;
      border-top: none;
      margin: 0;
      &:not(:last-of-type){
        border-right: none;
      }
    }
  }
}
</style>
