// directives/collapseExtension.js
export const vCollapseExt = {
    mounted(el, binding, vnode) {
      // 等待组件实例挂载完成
      setTimeout(() => {
        const instance = vnode.component?.proxy
        
        if (instance && instance.$refs && instance.$refs.refElem) {
          // 添加扩展方法
          instance.toggle = (name) => {
            const { activeNames } = instance.reactData
            const index = activeNames.indexOf(name)
            if (index === -1) {
              activeNames.push(name)
            } else {
              activeNames.splice(index, 1)
            }
            instance.dispatchEvent('update:modelValue', activeNames)
          }
          
          instance.expand = (name) => {
            const { activeNames } = instance.reactData
            if (!activeNames.includes(name)) {
              activeNames.push(name)
              instance.dispatchEvent('update:modelValue', activeNames)
            }
          }
          
          instance.collapse = (name) => {
            const { activeNames } = instance.reactData
            const index = activeNames.indexOf(name)
            if (index !== -1) {
              activeNames.splice(index, 1)
              instance.dispatchEvent('update:modelValue', activeNames)
            }
          }
          
          instance.expandAll = () => {
            // 实现展开所有面板的逻辑
            // ...
          }
          
          instance.collapseAll = () => {
            instance.reactData.activeNames = []
            instance.dispatchEvent('update:modelValue', [])
          }
        }
      }, 0)
    }
  }