import { createRouter, createWebHashHistory } from 'vue-router';
import { useUserStore } from '@/stores/user.js'
import { staticRoutes } from './routes.js'
import { getUserAuth } from '@/api/methods/user.js'
import {Modal} from '@opentiny/vue'
const layout = () => import('@/layout/index.vue')
// 动态获取views文件下的index文件
const modules = import.meta.glob('../views/**/index.vue')

const router = createRouter({
    history: createWebHashHistory(),
    routes: staticRoutes
});
// 白名单
const whiteList = ['/login']
// 默认名称
const defaultTitle = '房屋定损保险系统'
// 前置路由
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta && to.meta.title) {
    document.title = `${to.meta.title} - ${defaultTitle}`
  } else {
    document.title = defaultTitle
  }
  const userStore = useUserStore()
  if (userStore.token) {
    if (userStore.userMenus.length) {
      next()
    } else {
      // 刷新页面时调用接口获取menus和routes
      await handleDynamicRoutes()
      // https://router.vuejs.org/zh/guide/advanced/dynamic-routing.html
      next(to.fullPath)
    }
  } else if (whiteList.some(m => to.path.startsWith(m))) {
    next()
  } else {
    next(`/login?redirect=${to.path}`)
  }
})

// 重置路由
export function resetRouter() {
  // 注意：所有动态路由路由必须带有 Name 属性，否则可能会不能完全重置干净
  try {
    const staticNames = staticRoutes.map(item => item.name)
    router.getRoutes().forEach((route) => {
      const { name } = route
      if (name && router.hasRoute(name) && !staticNames.includes(name)) {
        router.removeRoute(name)
      }
    })
  } catch {
    // 强制刷新浏览器也行，只是交互体验不是很好
    window.location.reload()
  }
}

// 将menus转换为路由
const transformMenusForRoutes = (menus) => {
  return menus.map(menu => {
    const route = {
      path: menu.path,
      name: menu.componentName,
      meta: {
        ...menu,
        title: menu.sname,
      }
    }
    if (route.meta.children) {
      delete route.meta.children
    }
    let key = menu.componentPath
    if (key) {
      key = key.includes('.vue') ? `../${menu.componentPath}` : `../${menu.componentPath}.vue`
      route.component = modules[key]
    }
    if (menu.children && menu.children.length) {
      route.redirect = menu.children[0].path
      route.children = transformMenusForRoutes(menu.children)
    }
    return route
  })
}

// 注册动态路由
export const registerRoutes = (routes) => {
  routes.forEach(route => {
    // 一级没有子数据时需要处理
    if (!route.children || !route.children.length) {
      // 动态添加路由至默认布局组件下
      router.addRoute('layout', route)
    } else {
      // 一级component需设置为layout
      route.component = layout
      // 动态添加路由
      router.addRoute(route)
    }
  })
}
// 异步调用菜单数据
export const handleDynamicRoutes = async () => {
  const userStore = useUserStore()
  let userId = useUserStore().userInfo.userId
  const { data } = await getUserAuth({userId:userId})
  // const menus = data.sysMenu
  const menus = data.menus
  if (!menus.length) {
    Modal.message({
      status: 'error',
      message: '当前用户无任何菜单权限！',
      duration: '2000'
    })
    userStore.quit()
    throw new Error('当前用户无任何菜单权限！')
  }
  const showMenus = handleMenus(menus)
  userStore.setUserMenus(showMenus)
  const routes = transformMenusForRoutes(menus)
  registerRoutes(routes)
}
// 处理hidden以及url, tiny-tree url为关键词，需重命名并删除
const handleMenus = (menus) => {
  const result = []
  menus.forEach(item => {
    if (!item.hidden) {
      const menu = {
        ...item,
        children: [],
        menuUrl: item.url,
        name: item.componentName,
        title: item.sname,
      }
      delete menu.url
      result.push(menu)
      if (item.children && item.children.length) {
        menu.children = handleMenus(item.children)
      }
    }
  })
  return result
}

export default router
