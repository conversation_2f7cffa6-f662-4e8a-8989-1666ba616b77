[#](about:blank#%E5%AE%89%E8%A3%85) 安装
======================================

Day.js 可以运行在浏览器和 Node.js 中。

本文档所有代码都可以在这两种环境中正常运行，所有单元测试也都在这两个环境下完成。

CI 系统测试覆盖的浏览器有：Chrome on Windows XP, IE 8, 9, and 10 on Windows 7, IE 11 on Windows 10, latest Firefox on Linux, and latest Safari on OSX 10.8 and 10.11。

打开您的浏览器控制台，即可输入测试示例代码。

[#](about:blank#node-js) Node.js
--------------------------------

要在您的 Node.js 项目中使用 Day.js，只需使用 [npm (opens new window)](https://docs.npmjs.com/cli/v9/commands/npm-install) 安装

```
> npm install dayjs

```

或 [cnpm (opens new window)](https://npmmirror.com/?spm=a2c6h.24755359.0.0.246b7a5bD84wNY) 安装

```
> cnpm install dayjs -S

```

或 [yarn (opens new window)](https://www.yarnpkg.cn/getting-started/usage) 安装

```
> yarn add dayjs

```

或 [pnpm (opens new window)](https://www.pnpm.cn/cli/add) 安装

```
> pnpm add dayjs

```

然后在项目代码中引入即可：

```
var dayjs = require('dayjs')
// import dayjs from 'dayjs' // ES 2015
dayjs().format()

```

查看这里了解更多关于加载 [多语言](https://dayjs.fenxianglu.cn/category/i18n/#%E5%9C%A8nodejs%E4%B8%AD%E5%8A%A0%E8%BD%BD%E8%AF%AD%E8%A8%80%E9%85%8D%E7%BD%AE) 和 [插件](https://dayjs.fenxianglu.cn/category/plugin/#%E5%9C%A8nodejs%E4%B8%AD%E5%8A%A0%E8%BD%BD%E6%8F%92%E4%BB%B6) 的信息。

[#](about:blank#%E6%B5%8F%E8%A7%88%E5%99%A8) 浏览器
------------------------------------------------

```
<script src="https://cdn.jsdelivr.net/npm/dayjs/dayjs.min.js"></script>
<script>
  dayjs().format()
</script>

```

CDN引入（网络不稳定的时候，可能需要翻墙）

注意

Day.js可以通过CDN提供商，如：[cdnjs.com (opens new window)](https://cdnjs.com/libraries/dayjs), [unpkg (opens new window)](https://unpkg.com/dayjs/)，[jsdelivr (opens new window)](https://www.jsdelivr.com/package/npm/dayjs)和[bootcdn.cn (opens new window)](https://www.bootcdn.cn/dayjs/)等引入

```
<script src="https://cdn.jsdelivr.net/npm/dayjs/dayjs.min.js"></script>
<script>dayjs().format()</script>

```

友情提示

F12 打开控制台，选择 网络(Network) -> js 也可以找到 Day.js 的CDN引入地址。

[#](about:blank#%E5%BE%AE%E4%BF%A1%E5%B0%8F%E7%A8%8B%E5%BA%8F) 微信小程序
--------------------------------------------------------------------

### [#](about:blank#%E6%96%B9%E5%BC%8F1) 方式1

下载 `dayjs.min.js` 放到小程序 `lib` 目录下（没有新建或用其他目录）

引入示例：

```
const dayjs = require('../../libs/dayjs.min.js');

```

### [#](about:blank#%E6%96%B9%E5%BC%8F2) 方式2

使用 `npm` 安装

```
> npm install dayjs --save

```

引入示例：

```
const dayjs = require("dayjs");

```

[#](about:blank#element-plus) Element-plus
------------------------------------------

[Element-plus (opens new window)](https://element-plus.gitee.io/zh-CN/) 组件库默认支持 dayjs 进行日期时间处理，所以可以直接导入使用，相关 [Date Picker (opens new window)](https://element-plus.gitee.io/zh-CN/component/date-picker.html) 组件介绍。

```
import { dayjs } from 'element-plus'

// 扩展插件
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
dayjs.extend(isSameOrBefore)
dayjs.extend(isSameOrAfter)

dayjs().isSameOrBefore(dayjs('2011-01-01'))

```

[#](about:blank#typescript) Typescript
--------------------------------------

在 NPM 包中已经包含 Day.js 的 TypeScript 类型定义文件。

通过 NPM 安装

```
> npm install dayjs --save

```

在 TypeScript 项目中导入并使用

```
import * as dayjs from 'dayjs'
dayjs().format()

```

如果您的 `tsconfig.json` 包含以下配置，您必须使用 `import dayjs from 'dayjs'` 的 default import 模式：

```
{ //tsconfig.json
  "compilerOptions": {
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
  }
}

```

如果您没有上述配置，default import 将无法正常工作。 您需要使用 `import * as dayjs from 'dayjs'`

```
import * as dayjs from 'dayjs'

```

### [#](about:blank#%E5%AF%BC%E5%85%A5%E6%9C%AC%E5%9C%B0%E5%8C%96%E8%AF%AD%E8%A8%80%E5%92%8C%E6%8F%92%E4%BB%B6) 导入本地化语言和插件

在使用本地化语言和插件，您首先需要导入它们。

```
import * as dayjs from 'dayjs'
import * as isLeapYear from 'dayjs/plugin/isLeapYear' // 导入插件
import 'dayjs/locale/zh-cn' // 导入本地化语言

dayjs.extend(isLeapYear) // 使用插件
dayjs.locale('zh-cn') // 使用本地化语言

```

[#](about:blank#%E5%AE%9E%E4%BE%8B) 实例
--------------------------------------

代替修改本地`Date.prototype`，Day.js对`Date`对象进行了封装，只需要调用`Dayjs()`即可

Day.js对象是不可变的，也就是说，以某种方式改变Day.js对象的所有API操作都将返回它的一个新实例。

[#](about:blank#%E5%BD%93%E5%89%8D%E6%97%B6%E9%97%B4) 当前时间
----------------------------------------------------------

直接调用 `dayjs()` 将返回一个包含当前日期和时间的 Day.js 对象。

```
var now = dayjs()

```

等同于 `dayjs(new Date())` 的调用。

当没有传入参数时，参数默认值是 undefined，所以调用 `dayjs(undefined)` 就相当于调用 `dayjs()`。

Day.js 将 `dayjs(null)` 视为无效的输入。

[#](about:blank#%E5%AD%97%E7%AC%A6%E4%B8%B2) 字符串
------------------------------------------------

解析传入的 [ISO 8601 (opens new window)](https://en.wikipedia.org/wiki/ISO_8601) 格式的字符串并返回一个 Day.js 对象实例。

```
dayjs('2018-04-04T16:00:00.000Z')

```

注意

为了保证结果一致，当解析除了 ISO 8601 格式以外的字符串时，您应该使用 String + Format。

[#](about:blank#%E5%AD%97%E7%AC%A6%E4%B8%B2-%E6%A0%BC%E5%BC%8F) 字符串+格式
----------------------------------------------------------------------

如果知道输入字符串的格式，您可以用它来解析日期。

注意

此功能依赖 [CustomParseFormat](https://dayjs.fenxianglu.cn/category/plugin.html#customparseformat) 插件

```
dayjs.extend(customParseFormat)
dayjs("12-25-1995", "MM-DD-YYYY")

```

如果想解析包含本地化语言的日期字符串，可以传入第三个参数。

```
require('dayjs/locale/zh-cn')
dayjs('2018 三月 15', 'YYYY MMMM DD', 'zh-cn')

```

最后一个参数可传入布尔值来启用严格解析模式。 严格解析要求格式和输入内容完全匹配，包括分隔符。

```
dayjs('1970-00-00', 'YYYY-MM-DD').isValid() // true
dayjs('1970-00-00', 'YYYY-MM-DD', true).isValid() // false
dayjs('1970-00-00', 'YYYY-MM-DD', 'es', true).isValid() // false

```

如果您不知道输入字符串的确切格式，但知道它可能是几种中的一种，可以使用数组传入多个格式。

```
dayjs("12-25-2001", ["YYYY", "YYYY-MM-DD"], 'es', true);

```

支持的解析占位符列表：

| 输入 | 示例 | 描述 |
| --- | --- | --- |
| YY | 18 | 两位数的年份 |
| YYYY | 2018 | 四位数的年份 |
| M | 1-12 | 月份，从 1 开始 |
| MM | 01-12 | 月份，两位数 |
| MMM | Jan-Dec | 缩写的月份名称 |
| MMMM | January-December | 完整的月份名称 |
| D | 1-31 | 月份里的一天 |
| DD | 01-31 | 月份里的一天，两位数 |
| H | 0-23 | 小时 |
| HH | 00-23 | 小时，两位数 |
| h | 1-12 | 小时, 12 小时制 |
| hh | 01-12 | 小时, 12 小时制, 两位数 |
| m | 0-59 | 分钟 |
| mm | 00-59 | 分钟，两位数 |
| s | 0-59 | 秒 |
| ss | 00-59 | 秒，两位数 |
| S | 0-9 | 毫秒，一位数 |
| SS | 00-99 | 毫秒，两位数 |
| SSS | 000-999 | 毫秒，三位数 |
| Z | \-05:00 | UTC 的偏移量 |
| ZZ | \-0500 | UTC 的偏移量，两位数 |
| A | AM / PM | 上午 下午 大写 |
| a | am / pm | 上午 下午 小写 |
| Do | 1st... 31st | 带序数词的月份里的一天 |
| X | 1410715640.579 | Unix 时间戳 |
| x | 1410715640579 | Unix 时间戳 |

[#](about:blank#unix-%E6%97%B6%E9%97%B4%E6%88%B3-%E6%AF%AB%E7%A7%92) Unix 时间戳 (毫秒)
----------------------------------------------------------------------------------

解析传入的一个 Unix 时间戳 (13 位数字，从1970年1月1日 UTC 午夜开始所经过的毫秒数) 创建一个 Day.js 对象。

```
dayjs(1318781876406)

```

注意

传递的参数必须是一个数字

[#](about:blank#unix-%E6%97%B6%E9%97%B4%E6%88%B3-%E7%A7%92) Unix 时间戳 (秒)
------------------------------------------------------------------------

解析传入的一个 Unix 时间戳 (10 位数字，从1970年1月1日 Utc 午夜开始所经过的秒数) 创建一个 Day.js 对象。

```
dayjs.unix(1318781876)

```

这个方法是用 `dayjs( timestamp * 1000)` 实现的，所以传入时间戳里的小数点后面的秒也会被解析。

```
dayjs.unix(1318781876.721)

```

[#](about:blank#date-%E5%AF%B9%E8%B1%A1) Date 对象
------------------------------------------------

使用原生 Javascript `Date` 对象创建一个 Day.js 对象。

```
var d = new Date(2018, 8, 18)
var day = dayjs(d)

```

这将克隆 `Date` 对象。 对传入的 `Date` 对象做进一步更改不会影响 Day.js 对象，反之亦然。

[#](about:blank#%E5%AF%B9%E8%B1%A1) 对象
--------------------------------------

您可以传入包含单位和数值的一个对象来创建 Dayjs 对象。

注意

此功能依赖 [ObjectSupport](https://dayjs.fenxianglu.cn/category/plugin.html#objectsupport) 插件，才能正常运行

```
dayjs.extend(objectSupport)
dayjs({ hour:15, minute:10 });
dayjs.utc({ y:2010, M:3, d:5, h:15, m:10, s:3, ms: 123});
dayjs({ year :2010, month :3, day :5, hour :15, minute :10, second :3, millisecond :123});
dayjs({ years:2010, months:3, date:5, hours:15, minutes:10, seconds:3, milliseconds:123});

```

`day` 和 `date` 都表示月份里的日期。

`dayjs({})` 返回当前时间。

注意

类似 `new Date(year, month, date)`，月份从 0 开始计算。

[#](about:blank#%E6%95%B0%E7%BB%84) 数组
--------------------------------------

您可以传入一个数组来创建一个 Dayjs 对象，数组和结构和 `new Date()` 十分类似。

注意

此功能依赖 [ArraySupport](https://dayjs.fenxianglu.cn/category/plugin.html#arraysupport) 插件，才能正常运行

```
dayjs.extend(arraySupport)
dayjs([2010, 1, 14, 15, 25, 50, 125]); // February 14th, 3:25:50.125 PM
dayjs.utc([2010, 1, 14, 15, 25, 50, 125]);
dayjs([2010]);        // January 1st
dayjs([2010, 6]);     // July 1st
dayjs([2010, 6, 10]); // July 10th

```

`dayjs({})` 返回当前时间。

注意

类似 `new Date(year, month, date)`, 月份从 0 开始计算。

[#](about:blank#utc) UTC
------------------------

> 世界调整时间（Universal Time Coordinated）

默认情况下，Day.js 会把时间解析成本地时间。

如果想使用 UTC 时间，您可以调用 `dayjs.utc()` 而不是 `dayjs()`。

在 UTC 模式下，所有显示方法将会显示 UTC 时间而非本地时间。

注意

此功能依赖 [UTC](https://dayjs.fenxianglu.cn/category/plugin.html#utc) 插件，才能正常运行

```
dayjs.extend(utc)

// 默认是当地时间
dayjs().format() //2019-03-06T08:00:00+08:00
// UTC 时间
dayjs.utc().format() // 2019-03-06T00:00:00Z
此外，在 UTC 模式下， 所有 getters 和 setters 将使用 Date#getUTC* 和 Date#setUTC* 方法而不是 Date#get* 和 Date#set* 方法。

dayjs.utc().seconds(30).valueOf()// => new Date().setUTCSeconds(30)
dayjs.utc().seconds()// => new Date().getUTCSeconds()

```

此外，在 UTC 模式下， 所有 getters 和 setters 将使用 `Date#getUTC*` 和 `Date#setUTC*` 方法而不是 `Date#get*` 和 `Date#set*` 方法。

```
dayjs.utc().seconds(30).valueOf()// => new Date().setUTCSeconds(30)
dayjs.utc().seconds()// => new Date().getUTCSeconds()

```

要在本地时间和 UTC 时间之间切换，您可以使用 `dayjs#utc` 或 `dayjs#local`。

[#](about:blank#dayjs-%E5%A4%8D%E5%88%B6) Dayjs 复制
--------------------------------------------------

所有的 Day.js 对象都是不可变的。 但如果有必要，使用 `dayjs#clone` 可以复制出一个当前对象。

```
var a = dayjs()
var b = a.clone()
// a 和 b 是两个独立的 Day.js 对象

```

在 `dayjs()` 里传入一个 Day.js 对象也会返回一个复制的对象。

```
var a = dayjs()
var b = dayjs(a)

```

[#](about:blank#%E9%AA%8C%E8%AF%81) 验证
--------------------------------------

返回 布尔值 表示 `Dayjs` 的日期是否通过校验。

```
dayjs().isValid()

```

### [#](about:blank#%E4%B8%8D%E4%B8%A5%E6%A0%BC%E7%9A%84%E6%A0%A1%E9%AA%8C) 不严格的校验

只检查传入的值能否被解析成一个时间日期

```
dayjs('2022-01-33').isValid();
// true, parsed to 2022-02-02
dayjs('some invalid string').isValid();
// false

```

### [#](about:blank#%E4%B8%A5%E6%A0%BC%E6%A0%A1%E9%AA%8C) 严格校验

检查传入的值能否被解析，且是否是一个有意义的日期。 最后两个参数 `format` 和 `strict` 必须提供。

注意

此功能依赖 [CustomParseFormat](https://dayjs.fenxianglu.cn/category/plugin.html#customparseformat) 插件，才能正常运行

```
dayjs('2022-02-31', 'YYYY-MM-DD', true).isValid();
// false

```

Last Updated: 10/10/2022, 6:48:58 PM



在设计上 Day.js 的 `getter` 和 `setter` 使用了相同的 API，也就是说，不传参数调用方法即为 `getter`，调用并传入参数为 `setter`。

这些 API 调用了对应原生 `Date` 对象的方法。

```
dayjs().second(30).valueOf() // => new Date().setSeconds(30)
dayjs().second() // => new Date().getSeconds()

```

如果您处于 [UTC](https://dayjs.fenxianglu.cn/category/parse.html#utc) 模式，将会调用对应的 UTC 方法。

```
dayjs.utc().seconds(30).valueOf()// => new Date().setUTCSeconds(30)
dayjs.utc().seconds()// => new Date().getUTCSeconds()

```

[#](about:blank#%E6%AF%AB%E7%A7%92) 毫秒
--------------------------------------

获取或设置毫秒。

传入0到999的数字。 如果超出这个范围，它会进位到秒。

```
dayjs().millisecond()
dayjs().millisecond(1)

```

[#](about:blank#%E7%A7%92) 秒
----------------------------

获取或设置秒。

传入0到59的数字。 如果超出这个范围，它会进位到分钟。

```
dayjs().second()
dayjs().second(1)

```

[#](about:blank#%E5%88%86%E9%92%9F) 分钟
--------------------------------------

获取或设置分钟。

传入0到59的数字。 如果超出这个范围，它会进位到小时。

```
dayjs().minute()
dayjs().minute(59)

```

[#](about:blank#%E5%B0%8F%E6%97%B6) 小时
--------------------------------------

获取或设置小时。

传入0到23的数字。 如果超出这个范围，它会进位到天数。

```
dayjs().hour()
dayjs().hour(12)

```

[#](about:blank#%E6%97%A5%E6%9C%9F) 日期
--------------------------------------

获取或设置月份里的日期。

接受1到31的数字。 如果超出这个范围，它会进位到月份。

```
dayjs().date()
dayjs().date(1)

```

注意

`dayjs#date` 是该月的日期。 `dayjs#day` 是星期几。

[#](about:blank#%E6%98%9F%E6%9C%9F) 星期
--------------------------------------

获取或设置星期几。

传入 number 从0(星期天)到6(星期六)。 如果超出这个范围，它会进位到其他周。

```
dayjs().day()
dayjs().day(0)

```

注意

`dayjs#date` 是该月的日期。 `dayjs#day` 是星期几。

[#](about:blank#%E6%9C%AC%E5%9C%B0%E5%8C%96%E6%98%9F%E6%9C%9F) 本地化星期
--------------------------------------------------------------------

根据本地化配置获取或设置星期几。

注意

此功能依赖 [Weekday](https://dayjs.fenxianglu.cn/category/plugin.html#weekday) 插件，才能正常运行

如果本地化配置了星期天为一周的第一天， `dayjs().weekday(0)` 将返回星期天。 如果星期一是一周的第一天， `dayjs().weekday(0)` 将返回星期一。

```
dayjs.extend(weekday)

// 当星期天是一周的第一天
dayjs().weekday(-7); // last Sunday
dayjs().weekday(7); // next Sunday

// 当星期一是一周的第一天
dayjs().weekday(-7) // last Monday
dayjs().weekday(7) // next Monday

// 当星期天是一周的第一天
dayjs().weekday(-5) // last Tuesday (5th day before Sunday)
dayjs().weekday(5) // next Friday (5th day after Sunday)

```

[#](about:blank#iso%E6%98%9F%E6%9C%9F) ISO星期
--------------------------------------------

获取或设置 [ISO 星期几 (opens new window)](https://en.wikipedia.org/wiki/ISO_week_date) ，其中 1 是星期一、7 是星期日。

注意

此功能依赖 [IsoWeek](https://dayjs.fenxianglu.cn/category/plugin.html#isoweek) 插件，才能正常运行

```
dayjs.extend(isoWeek)

dayjs().isoWeekday()
dayjs().isoWeekday(1); // Monday

```

[#](about:blank#%E5%B9%B4-%E6%97%A5%E6%9C%9F) 年-日期
--------------------------------------------------

获取或设置年份里第几天。

传入1到366的数字。

如果超出这个范围，它会进位到下一年。

注意

此功能依赖 [DayOfYear](https://dayjs.fenxianglu.cn/category/plugin.html#dayofyear) 插件

```
dayjs.extend(dayOfYear)

dayjs('2010-01-01').dayOfYear() // 1
dayjs('2010-01-01').dayOfYear(365) // 2010-12-31

```

[#](about:blank#%E5%B9%B4-%E5%91%A8) 年-周
----------------------------------------

获取或设置该年的第几周。

注意

此功能依赖 [WeekOfYear](https://dayjs.fenxianglu.cn/category/plugin.html#weekofyear) 插件

```
dayjs.extend(weekOfYear)

dayjs('2018-06-27').week() // 26
dayjs('2018-06-27').week(5) // 设置周

```

注意

`week()` 函数是特定于区域设置的，因此应该在之前导入区域设置。

```
import "dayjs/locale/zh-cn";
dayjs.locale("zh-cn");

dayjs("2022-8-8").week(1).format("YYYY-MM-DD"); // 2022-01-03
dayjs("2022-8-9").week(1).format("YYYY-MM-DD"); // 2022-01-04

```

[#](about:blank#%E5%B9%B4-%E5%91%A8-iso) 年-周(ISO)
-------------------------------------------------

获取或设置年份的 [ISO 星期 (opens new window)](https://en.wikipedia.org/wiki/ISO_week_date)。

注意

此功能依赖 [IsoWeek](https://dayjs.fenxianglu.cn/category/plugin.html#isoweek) 插件

```
dayjs.extend(isoWeek)

dayjs().isoWeek()
dayjs().isoWeek(2)

```

[#](about:blank#%E6%9C%88) 月
----------------------------

获取或设置月份。

传入0到11的 number。 如果超出这个范围，它会进位到年份。

```
dayjs().month()
dayjs().month(0)

```

注意

月份是从 0 开始计算的，即 1 月是 0。

[#](about:blank#%E5%AD%A3%E5%BA%A6) 季度
--------------------------------------

获取或设置季度。

注意

此功能依赖 [QuarterOfYear](https://dayjs.fenxianglu.cn/category/plugin.html#quarterofyear) 插件

```
dayjs.extend(quarterOfYear)

dayjs('2010-04-01').quarter() // 2
dayjs('2010-04-01').quarter(2)

```

[#](about:blank#%E5%B9%B4) 年
----------------------------

获取或设置年份。

```
dayjs().year()
dayjs().year(2000)

```

[#](about:blank#%E5%91%A8%E5%B9%B4) 周年
--------------------------------------

获取基于当前语言配置的按周计算的年份。

注意

此功能依赖 [WeekYear](https://dayjs.fenxianglu.cn/category/plugin.html#weekyear) 插件

```
dayjs.extend(weekYear)
dayjs.extend(weekOfYear)

dayjs().weekYear()

```

[#](about:blank#%E5%91%A8%E5%B9%B4-iso) 周年(ISO)
-----------------------------------------------

获取 [ISO 周年 (opens new window)](https://en.wikipedia.org/wiki/ISO_week_date)。

注意

此功能依赖 [IsoWeek](https://dayjs.fenxianglu.cn/category/plugin.html#isoweek) 插件

```
dayjs.extend(isoWeek)

dayjs().isoWeekYear()

```

[#](about:blank#%E5%B9%B4%E5%91%A8%E6%95%B0-iso) 年周数(ISO)
---------------------------------------------------------

获取当前年份的周数，根据 [ISO weeks (opens new window)](https://en.wikipedia.org/wiki/ISO_week_date) 的定义。

注意

此功能依赖 [IsoWeeksInYear](https://dayjs.fenxianglu.cn/category/plugin.html#isoweeksinyear) 插件

```
dayjs.extend(isoWeeksInYear)
dayjs.extend(isLeapYear)

dayjs('2004-01-01').isoWeeksInYear() // 53
dayjs('2005-01-01').isoWeeksInYear() // 52

```

[#](about:blank#get) Get
------------------------

从 Day.js 对象中获取相应信息的 getter。

可以理解为：

```
dayjs().get(unit) === dayjs()[unit]()

```

各个传入的单位对大小写不敏感，支持缩写和复数。 请注意，缩写是区分大小写的。

```
dayjs().get('year')
dayjs().get('month') // start 0
dayjs().get('date')
dayjs().get('hour')
dayjs().get('minute')
dayjs().get('second')
dayjs().get('millisecond')

```

支持的单位列表：

| 单位 | 缩写 | 描述 |
| --- | --- | --- |
| date | D | 日期 |
| day | d | 星期(星期日0，星期六6) |
| month | M | 月份(0-11) |
| year | y | 年 |
| hour | h | 小时 |
| minute | m | 分钟 |
| second | s | 秒 |
| millisecond | ms | 毫秒 |

[#](about:blank#set) Set
------------------------

通用的 setter，两个参数分别是要更新的单位和数值，调用后会返回一个修改后的新实例。

可以理解为：

```
dayjs().set(unit, value) === dayjs()[unit](value)

```

```
dayjs().set('date', 1)
dayjs().set('month', 3) // 四月
dayjs().set('second', 30)

```

也支持这样的链式调用：

```
dayjs().set('hour', 5).set('minute', 55).set('second', 15)

```

各个传入的单位对大小写不敏感，支持缩写和复数。

[支持的单位列表](https://dayjs.fenxianglu.cn/category/parse.html#%E5%AD%97%E7%AC%A6%E4%B8%B2-%E6%A0%BC%E5%BC%8F)

[#](about:blank#%E6%9C%80%E5%A4%A7%E5%80%BC) 最大值
------------------------------------------------

返回传入的 Day.js 实例中的最大的 (即最靠近未来的)。 它接受传入多个 Day.js实例或一个数组。

注意

此功能依赖 [MinMax](https://dayjs.fenxianglu.cn/category/plugin.html#minmax) 插件

```
dayjs.extend(minMax)

dayjs.max(dayjs(), dayjs('2018-01-01'), dayjs('2019-01-01'))
dayjs.max([dayjs(), dayjs('2018-01-01'), dayjs('2019-01-01')])

```

[#](about:blank#%E6%9C%80%E5%B0%8F%E5%80%BC) 最小值
------------------------------------------------

返回传入的 Day.js 实例中的最小的 (即最靠近过去的)。 它接受传入多个 Day.js实例或一个数组。

注意

此功能依赖 [MinMax](https://dayjs.fenxianglu.cn/category/plugin.html#minmax) 插件

```
dayjs.extend(minMax)

dayjs.min(dayjs(), dayjs('2018-01-01'), dayjs('2019-01-01'))
dayjs.min([dayjs(), dayjs('2018-01-01'), dayjs('2019-01-01')])

```

Last Updated: 10/10/2022, 6:48:58 PM

您可能需要一些方法来操作 Day.js 对象。

Day.js 支持像这样的链式调用：

```
dayjs('2019-01-25').add(1, 'day').subtract(1, 'year').year(2009).toString()

```

[#](about:blank#%E5%A2%9E%E5%8A%A0) 增加
--------------------------------------

返回增加一定时间的复制的 Day.js 对象。

```
dayjs().add(7, 'day')

```

各个传入的单位对大小写不敏感，支持缩写和复数。 请注意，缩写是区分大小写的。

支持的单位列表：

| 单位 | 缩写 | 描述 |
| --- | --- | --- |
| day | d | 日 |
| week | w | 周 |
| month | M | 月份(0-11) |
| quarter | Q | 季度，依赖 `QuarterOfYear` 插件 |
| year | y | 年 |
| hour | h | 小时 |
| minute | m | 分钟 |
| second | s | 秒 |
| millisecond | ms | 毫秒 |

或者，也可以给 Day.js 对象增加一个 [持续时间](https://dayjs.fenxianglu.cn/category/duration.html) 。

[#](about:blank#%E5%87%8F%E5%8E%BB) 减去
--------------------------------------

返回减去一定时间的复制的 Day.js 对象。

```
dayjs().subtract(7, 'year')

```

各个传入的单位对大小写不敏感，支持缩写和复数。

[#](about:blank#%E6%97%B6%E9%97%B4%E7%9A%84%E5%BC%80%E5%A7%8B) 时间的开始
--------------------------------------------------------------------

返回复制的 Day.js 对象，并设置到一个时间的开始。

```
dayjs().startOf('year')

```

各个传入的单位对大小写不敏感，支持缩写和复数。

支持的单位列表：

| 单位 | 缩写 | 描述 |
| --- | --- | --- |
| date | D | 当天 00:00 |
| day | d | 当天 00:00 |
| month | M | 本月1日上午 00:00 |
| quarter | Q | 本季度第一个月1日上午 00:00，依赖 `QuarterOfYear` 插件 |
| year | y | 今年一月1日上午 00:00 |
| week | w | 本周的第一天上午 00:00 |
| isoWeek |  | 本周的第一天上午 00:00 (根据 ISO 8601) ， ( 依赖 `IsoWeek` 插件 ) |
| hour | h | 当前时间，0 分、0 秒、0 毫秒 |
| minute | m | 当前时间，0 秒、0 毫秒 |
| second | s | 当前时间，0 毫秒 |

[#](about:blank#%E6%97%B6%E9%97%B4%E7%9A%84%E7%BB%93%E6%9D%9F) 时间的结束
--------------------------------------------------------------------

返回复制的 Day.js 对象，并设置到一个时间的末尾。

```
dayjs().endOf('month')

```

各个传入的单位对大小写不敏感，支持缩写和复数。

[#](about:blank#%E5%BD%93%E5%89%8D%E6%97%B6%E5%8C%BA) 当前时区
----------------------------------------------------------

返回一个在当前时区模式下的 Day.js 对象。

注意

此功能依赖 [UTC](https://dayjs.fenxianglu.cn/category/plugin.html#utc) 插件

```
dayjs.extend(utc)

var a = dayjs.utc()
a.format() // 2019-03-06T00:00:00Z
a.local().format() //2019-03-06T08:00:00+08:00

```

了解更多关于 [UTC 模式](https://dayjs.fenxianglu.cn/category/parse.html#utc) 的信息。

[#](about:blank#utc) UTC
------------------------

返回一个在 UTC 模式下的 Day.js 对象。

注意

此功能依赖 [UTC](https://dayjs.fenxianglu.cn/category/plugin.html#utc) 插件

```
dayjs.extend(utc)

var a = dayjs()
a.format() //2019-03-06T08:00:00+08:00
a.utc().format() // 2019-03-06T00:00:00Z

```

传入 true 将只改变 UTC 模式而不改变本地时间。

```
dayjs('2016-05-03 22:15:01').utc(true).format() 
// 2016-05-03T22:15:01Z

```

[#](about:blank#utc%E5%81%8F%E7%A7%BB%E9%87%8F) UTC偏移量
------------------------------------------------------

获取 UTC 偏移量 (分钟)。

```
dayjs().utcOffset()

```

也可以传入分钟来得到一个更改 UTC 偏移量的新实例。 请注意，一旦您设置了 UTC 偏移量，它将保持固定，不会自动改变 (即没有DST夏令时变更)。

注意

此功能依赖 [UTC](https://dayjs.fenxianglu.cn/category/plugin.html#utc) 插件

```
dayjs.extend(utc)

dayjs().utcOffset(120)

```

如果输入在-16到16之间，会将您的输入理解为小时数而非分钟。

```
// 以下两种写法是等效的
dayjs().utcOffset(8)  // 设置小时偏移量
dayjs().utcOffset(480)  // 设置分钟偏移量 (8 * 60)

```

第二个参数传入 true 可以只改变偏移量而保持本地时间不变。

```
dayjs.utc('2000-01-01T06:01:02Z').utcOffset(1, true).format() 
// 2000-01-01T06:01:02+01:00

```

Last Updated: 11/20/2022, 7:10:43 PM

当解析和操作完成后，您需要一些方式来展示 Day.js 对象。

[#](about:blank#%E6%A0%BC%E5%BC%8F%E5%8C%96) 格式化
------------------------------------------------

根据传入的占位符返回格式化后的日期。

将字符放在方括号中，即可原样返回而不被格式化替换 (例如， \[`MM`\])。

```
dayjs().format() 
// 默认返回的是 ISO8601 格式字符串 '2020-04-02T08:02:17-05:00'

dayjs('2019-01-25').format('[YYYYescape] YYYY-MM-DDTHH:mm:ssZ[Z]') 
// 'YYYYescape 2019-01-25T00:00:00-02:00Z'

dayjs('2019-01-25').format('DD/MM/YYYY') // '25/01/2019'

```

支持的格式化占位符列表：

| 标识 | 示例 | 描述 |
| --- | --- | --- |
| YY | 18 | 年，两位数 |
| YYYY | 2018 | 年，四位数 |
| M | 1-12 | 月，从1开始 |
| MM | 01-12 | 月，两位数 |
| MMM | Jan-Dec | 月，英文缩写 |
| MMMM | January-December | 月，英文全称 |
| D | 1-31 | 日 |
| DD | 01-31 | 日，两位数 |
| d | 0-6 | 一周中的一天，星期天是 0 |
| dd | Su-Sa | 最简写的星期几 |
| ddd | Sun-Sat | 简写的星期几 |
| dddd | Sunday-Saturday | 星期几，英文全称 |
| H | 0-23 | 小时 |
| HH | 00-23 | 小时，两位数 |
| h | 1-12 | 小时, 12 小时制 |
| hh | 01-12 | 小时, 12 小时制, 两位数 |
| m | 0-59 | 分钟 |
| mm | 00-59 | 分钟，两位数 |
| s | 0-59 | 秒 |
| ss | 00-59 | 秒，两位数 |
| S | 0-9 | 毫秒（十），一位数 |
| SS | 00-99 | 毫秒（百），两位数 |
| SSS | 000-999 | 毫秒，三位数 |
| Z | \-05:00 | UTC 的偏移量，±HH:mm |
| ZZ | \-0500 | UTC 的偏移量，±HHmm |
| A | AM / PM | 上/下午，大写 |
| a | am / pm | 上/下午，小写 |
| Do | 1st... 31st | 月份的日期与序号 |
| ... | ... | 其他格式 ( 依赖 [AdvancedFormat](https://dayjs.fenxianglu.cn/category/plugin.html#advancedformat) 插件 ) |

**本地化格式**

在不同的本地化配置下，有一些不同的本地化格式可以使用。

注意

此功能依赖 [LocalizedFormat](https://dayjs.fenxianglu.cn/category/plugin.html#localizedformat) 插件

```
dayjs.extend(LocalizedFormat)
dayjs().format('L LT')

```

支持的本地化格式列表：

| 占位符 | 英语语言 | 输出结果 |
| --- | --- | --- |
| LT | h:mm A | 8:02 PM |
| LTS | h:mm:ss A | 8:02:18 PM |
| L | MM/DD/YYYY | 08/16/2018 |
| LL | MMMM D, YYYY | August 16, 2018 |
| LLL | MMMM D, YYYY h:mm A | August 16, 2018 8:02 PM |
| LLLL | dddd, MMMM D, YYYY h:mm A | Thursday, August 16, 2018 8:02 PM |
| l | M/D/YYYY | 8/16/2018 |
| ll | MMM D, YYYY | Aug 16, 2018 |
| lll | MMM D, YYYY h:mm A | Aug 16, 2018 8:02 PM |
| llll | ddd, MMM D, YYYY h:mm A | Thu, Aug 16, 2018 8:02 PM |

[#](about:blank#%E7%9B%B8%E5%AF%B9%E5%BD%93%E5%89%8D%E6%97%B6%E9%97%B4-%E5%89%8D) 相对当前时间（前）
-------------------------------------------------------------------------------------------

返回现在到当前实例的相对时间。

注意

此功能依赖 [RelativeTime](https://dayjs.fenxianglu.cn/category/plugin.html#relativetime) 插件

```
dayjs.extend(relativeTime)

dayjs('1999-01-01').fromNow() // 22 年前

```

如果传入 true，则可以获得不带后缀的值。

```
dayjs.extend(relativeTime)

dayjs('1999-01-01').fromNow(true) // 22 年

```

时间范围划分标准：

表格里的值是由语言配置决定的，并且 [可以自定义输出内容](https://dayjs.fenxianglu.cn/category/custom.html#%E7%9B%B8%E5%AF%B9%E6%97%B6%E9%97%B4)。 时间会舍入到最接近的秒数。

| 范围 | 键值 | 输出结果 |
| --- | --- | --- |
| 0 to 44 seconds | s | 几秒前 |
| 45 to 89 seconds | m | 1 分钟前 |
| 90 seconds to 44 minutes | mm | 2 分钟前 ... 44 分钟前 |
| 45 to 89 minutes | h | 1 小时前 |
| 90 minutes to 21 hours | hh | 2 小时前 ... 21 小时前 |
| 22 to 35 hours | d | 1 天前 |
| 36 hours to 25 days | dd | 2 天前 ... 25 天前 |
| 26 to 45 days | M | 1 个月前 |
| 46 days to 10 months | MM | 2 个月前 ... 10 个月前 |
| 11 months to 17months | y | 1 年前 |
| 18 months+ | yy | 2 年前 ... 20 年前 |

[#](about:blank#%E7%9B%B8%E5%AF%B9%E6%8C%87%E5%AE%9A%E6%97%B6%E9%97%B4-%E5%89%8D) 相对指定时间（前）
-------------------------------------------------------------------------------------------

返回 X 到当前实例的相对时间。

注意

此功能依赖 [RelativeTime](https://dayjs.fenxianglu.cn/category/plugin.html#relativetime) 插件

```
dayjs.extend(relativeTime)

var a = dayjs('2000-01-01')

dayjs('1999-01-01').from(a) // 1 年前

```

如果传入 true，则可以获得不带后缀的值。

```
dayjs.extend(relativeTime)

var a = dayjs('2000-01-01')

dayjs('1999-01-01').from(a, true) // 1 年

```

[#](about:blank#%E7%9B%B8%E5%AF%B9%E5%BD%93%E5%89%8D%E6%97%B6%E9%97%B4-%E5%90%8E) 相对当前时间（后）
-------------------------------------------------------------------------------------------

返回当前实例到现在的相对时间。

注意

此功能依赖 [RelativeTime](https://dayjs.fenxianglu.cn/category/plugin.html#relativetime) 插件

```
dayjs.extend(relativeTime)

dayjs('1999-01-01').toNow() // 22 年后

```

如果传入 true，则可以获得不带后缀的值。

```
dayjs.extend(relativeTime)

dayjs('1999-01-01').toNow(true) // 22 年

```

[#](about:blank#%E7%9B%B8%E5%AF%B9%E6%8C%87%E5%AE%9A%E6%97%B6%E9%97%B4-%E5%90%8E) 相对指定时间（后）
-------------------------------------------------------------------------------------------

返回当前实例到 X 的相对时间。

注意

此功能依赖 [RelativeTime](https://dayjs.fenxianglu.cn/category/plugin.html#relativetime) 插件

```
dayjs.extend(relativeTime)

var a = dayjs('2000-01-01')

dayjs('1999-01-01').to(a) // 1 年后

```

如果传入 true，则可以获得不带后缀的值。

```
dayjs.extend(relativeTime)

var a = dayjs('2000-01-01')

dayjs('1999-01-01').to(a, true) // 1 年

```

[#](about:blank#%E6%97%A5%E5%8E%86%E6%97%B6%E9%97%B4) 日历时间
----------------------------------------------------------

日历时间显示了距离给定时间 (默认为现在) 的相对时间，但与 `dayjs#fromnow` 略有不同。

注意

此功能依赖 [Calendar](https://dayjs.fenxianglu.cn/category/plugin.html#calendar) 插件

```
dayjs.extend(calendar)

dayjs().calendar()
dayjs().calendar(dayjs('2008-01-01'))

```

| 键 | 值 |
| --- | --- |
| 上个星期 (lastWeek) | 上星期一 2:30 |
| 前一天 (lastDay) | 昨天 2:30 |
| 同一天 (sameDay) | 今天 2:30 |
| 下一天 (nextDay) | 明天 2:30 |
| 下个星期 (nextWeek) | 星期日 2:30 |
| 其他 (sameElse) | 7/10/2011 |

表格里的值是由语言配置决定的，并且 [可以自定义输出内容](https://dayjs.fenxianglu.cn/category/custom.html#%E6%97%A5%E5%8E%86)。

您也可以通过第二个参数传入指定日历输出格式。

将字符放在方括号中，即可原样返回而不被格式化替换 (例如， \[`Today`\])。

```
dayjs().calendar(null, {
  sameDay: '[Today at] h:mm A', // The same day ( Today at 2:30 AM )
  nextDay: '[Tomorrow]', // The next day ( Tomorrow at 2:30 AM )
  nextWeek: 'dddd', // The next week ( Sunday at 2:30 AM )
  lastDay: '[Yesterday]', // The day before ( Yesterday at 2:30 AM )
  lastWeek: '[Last] dddd', // Last week ( Last Monday at 2:30 AM )
  sameElse: 'DD/MM/YYYY' // Everything else ( 7/10/2011 )
})

```

[#](about:blank#%E5%B7%AE%E5%BC%82-diff) 差异（Diff）
-------------------------------------------------

返回指定单位下两个日期时间之间的差异。

要获得以毫秒为单位的差异，请使用 `dayjs#diff`。

```
const date1 = dayjs('2019-01-25')
const date2 = dayjs('2018-06-05')
date1.diff(date2) // 20214000000 默认单位是毫秒

```

要获取其他单位下的差异，则在第二个参数传入相应的单位。

```
const date1 = dayjs('2019-01-25')
date1.diff('2018-06-05', 'month') // 7

```

默认情况下 `dayjs#diff` 会将结果进位成整数。 如果要得到一个浮点数，将 `true` 作为第三个参数传入。

```
const date1 = dayjs('2019-01-25')
date1.diff('2018-06-05', 'month', true) // 7.645161290322581

```

时间戳（距今多少天）

```
dayjs().diff(dayjs(1718258944185), 'day')

```

支持的单位列表：

各个传入的单位对大小写不敏感，支持缩写和复数。 请注意，缩写是区分大小写的。

| 单位 | 缩写 | 描述 |
| --- | --- | --- |
| week | w | 周（Week of Year） |
| day | d | 日 |
| month | M | 月份 (一月 0， 十二月 11) |
| quarter | Q | 季度，依赖 `QuarterOfYear` 插件 |
| year | y | 年 |
| hour | h | 小时 |
| minute | m | 分钟 |
| second | s | 秒 |
| millisecond | ms | 毫秒 |

[#](about:blank#unix%E6%97%B6%E9%97%B4%E6%88%B3-%E6%AF%AB%E7%A7%92) Unix时间戳(毫秒)
-------------------------------------------------------------------------------

返回当前实例的 UNIX 时间戳，13位数字，毫秒

```
dayjs('2019-01-25').valueOf() // 1548381600000
+dayjs(1548381600000) // 1548381600000

```

您应该使用 `Unix Timestamp` 来获取 UNIX 时间戳(10位 秒)

[#](about:blank#unix%E6%97%B6%E9%97%B4%E6%88%B3) Unix时间戳
--------------------------------------------------------

返回当前实例的 UNIX 时间戳，10位数字，秒。

```
dayjs('2019-01-25').unix() // 1548381600

```

此值不包含毫秒信息，会进位到秒。

[#](about:blank#%E8%8E%B7%E5%8F%96%E6%9C%88%E5%A4%A9%E6%95%B0) 获取月天数
--------------------------------------------------------------------

获取当前月份包含的天数。

```
dayjs('2019-01-25').daysInMonth() // 31

```

[#](about:blank#%E8%BD%ACdate) 转Date
------------------------------------

调用 `dayjs#toDate` 从 Day.js 对象中获取原生的 Date 对象

```
dayjs('2019-01-25').toDate()

```

[#](about:blank#%E8%BD%AC%E6%95%B0%E7%BB%84) 转数组
------------------------------------------------

返回一个包含各个时间信息的 Array 。

注意

此功能依赖 [ToArray](https://dayjs.fenxianglu.cn/category/plugin.html#toarray) 插件

```
dayjs.extend(toArray)

dayjs('2019-01-25').toArray() // [ 2019, 0, 25, 0, 0, 0, 0 ]

```

[#](about:blank#%E8%BD%ACjson) 转JSON
------------------------------------

序列化为 ISO 8601 格式的字符串。

```
dayjs('2019-01-25').toJSON() // '2019-01-25T02:00:00.000Z'

```

[#](about:blank#%E8%BD%ACiso-8601%E5%AD%97%E7%AC%A6%E4%B8%B2) 转ISO 8601字符串
--------------------------------------------------------------------------

返回一个 ISO 8601 格式的字符串。

```
dayjs('2019-01-25').toISOString() // '2019-01-25T02:00:00.000Z'

```

[#](about:blank#%E8%BD%AC%E5%AF%B9%E8%B1%A1) 转对象
------------------------------------------------

返回包含时间信息的 Object。

注意

此功能依赖 [ToObject](https://dayjs.fenxianglu.cn/category/plugin.html#toobject) 插件

```
dayjs.extend(toObject)

dayjs('2019-01-25').toObject()
/* { years: 2019,
     months: 0,
     date: 25,
     hours: 0,
     minutes: 0,
     seconds: 0,
     milliseconds: 0 } */

```

[#](about:blank#%E8%BD%AC%E5%AD%97%E7%AC%A6%E4%B8%B2) 转字符串
----------------------------------------------------------

返回包含时间信息的 string 。

```
dayjs('2019-01-25').toString() // 'Fri, 25 Jan 2019 02:00:00 GMT'

```

Last Updated: 11/23/2023, 11:21:20 PM


[#](about:blank#%E6%98%AF%E5%90%A6%E4%B9%8B%E5%89%8D) 是否之前
----------------------------------------------------------

这表示 Day.js 对象是否在另一个提供的日期时间之前。

```
dayjs().isBefore(dayjs('2011-01-01')) // 默认毫秒

```

如果想使用除了毫秒以外的单位进行比较，则将单位作为第二个参数传入。

```
dayjs().isBefore('2011-01-01', 'year')

```

各个传入的单位对大小写不敏感，支持缩写和复数。

[#](about:blank#%E6%98%AF%E5%90%A6%E7%9B%B8%E5%90%8C) 是否相同
----------------------------------------------------------

这表示 Day.js 对象是否和另一个提供的日期时间相同。

```
dayjs().isSame(dayjs('2011-01-01')) // 默认毫秒

```

如果想使用除了毫秒以外的单位进行比较，则将单位作为第二个参数传入。

当使用第二个参数时，将会连同去比较更大的单位。 如传入 `month` 将会比较 `month` 和 `year`。 传入 `day` 将会比较 `day`、 `month`和 `year`。

```
dayjs().isSame('2011-01-01', 'year')

```

各个传入的单位对大小写不敏感，支持缩写和复数。

注意

`isSame()` 方法不传参数或参数为 `undefined` 都默认返回true

[#](about:blank#%E6%98%AF%E5%90%A6%E4%B9%8B%E5%90%8E) 是否之后
----------------------------------------------------------

这表示 Day.js 对象是否在另一个提供的日期时间之后。

```
dayjs().isAfter(dayjs('2011-01-01')) // 默认毫秒

// 其他写法
dayjs('2011-01-02').isAfter('2011-01-01') // true
dayjs('2011-01-02', 'YYYY-MM-DD').isAfter('2011-01-01') // true
dayjs(dayjs('2011-01-02')).isAfter(dayjs('2011-01-01')) // true
dayjs(dayjs('2011-01-02', 'YYYY-MM-DD')).isAfter(dayjs('2011-01-01', 'YYYY-MM-DD')) // true
dayjs(new Date('2011-01-02')).isAfter(new Date('2011-01-01')) // true

```

如果想使用除了毫秒以外的单位进行比较，则将单位作为第二个参数传入。

```
dayjs().isAfter('2011-01-01', 'year')

```

各个传入的单位对大小写不敏感，支持缩写和复数。

[#](about:blank#%E6%98%AF%E5%90%A6%E7%9B%B8%E5%90%8C%E6%88%96%E4%B9%8B%E5%89%8D) 是否相同或之前
----------------------------------------------------------------------------------------

这表示 Day.js 对象是否和另一个提供的日期时间相同或在其之前。

注意

此功能依赖 [IsSameOrBefore](https://dayjs.fenxianglu.cn/category/plugin.html#issameorbefore) 插件

```
dayjs.extend(isSameOrBefore)
dayjs().isSameOrBefore(dayjs('2011-01-01')) // 默认毫秒

```

如果想使用除了毫秒以外的单位进行比较，则将单位作为第二个参数传入。

```
dayjs().isSameOrBefore('2011-01-01', 'year')

```

各个传入的单位对大小写不敏感，支持缩写和复数。

[#](about:blank#%E6%98%AF%E5%90%A6%E7%9B%B8%E5%90%8C%E6%88%96%E4%B9%8B%E5%90%8E) 是否相同或之后
----------------------------------------------------------------------------------------

这表示 Day.js 对象是否和另一个提供的日期时间相同或在其之后。

注意

此功能依赖 [IsSameOrAfter](https://dayjs.fenxianglu.cn/category/plugin.html#issameorafter) 插件

```
dayjs.extend(isSameOrAfter)
dayjs().isSameOrAfter(dayjs('2011-01-01')) // 默认毫秒

```

如果想使用除了毫秒以外的单位进行比较，则将单位作为第二个参数传入。

```
dayjs().isSameOrAfter('2011-01-01', 'year')

```

各个传入的单位对大小写不敏感，支持缩写和复数。

[#](about:blank#%E6%98%AF%E5%90%A6%E4%B8%A4%E8%80%85%E4%B9%8B%E9%97%B4) 是否两者之间
------------------------------------------------------------------------------

这表示 Day.js 对象是否在其他两个的日期时间之间。

注意

此功能依赖 [IsBetween](https://dayjs.fenxianglu.cn/category/plugin.html#isbetween) 插件

```
dayjs.extend(isBetween)
dayjs('2010-10-20').isBetween('2010-10-19', dayjs('2010-10-25')) 
// 默认毫秒

```

如果想使用除了毫秒以外的单位进行比较，则将单位作为第三个参数传入。

```
dayjs().isBetween('2010-10-19', '2010-10-25', 'year')

```

各个传入的单位对大小写不敏感，支持缩写和复数。

[支持的单位列表](https://dayjs.fenxianglu.cn/category/manipulate.html#%E6%97%B6%E9%97%B4%E7%9A%84%E5%BC%80%E5%A7%8B)

第四个参数是设置包容性。 `[` 表示包含。 `(` 表示排除。

要使用包容性参数，必须同时传入两个指示符。

```
dayjs('2016-10-30').isBetween('2016-01-01', '2016-10-30', null, '[)')

```

[#](about:blank#%E6%98%AF%E5%90%A6%E6%98%AFday-js) 是否是Day.js
------------------------------------------------------------

这表示一个变量是否为 Day.js 对象。

```
dayjs.isDayjs(dayjs()) // true
dayjs.isDayjs(new Date()) // false

```

这和使用 instanceof 的结果是一样的：

```
dayjs() instanceof dayjs // true

```

[#](about:blank#%E6%98%AF%E5%90%A6%E9%97%B0%E5%B9%B4) 是否闰年
----------------------------------------------------------

查询 Day.js 对象的年份是否是闰年。

注意

此功能依赖 [IsLeapYear](https://dayjs.fenxianglu.cn/category/plugin.html#isleapyear) 插件

```
dayjs.extend(isLeapYear)

dayjs('2000-01-01').isLeapYear() // true

```

Last Updated: 9/6/2023, 10:18:48 AM

← [显示](https://dayjs.fenxianglu.cn/category/display.html) [国际化（i18n）](https://dayjs.fenxianglu.cn/category/i18n.html) →



本文转自 [https://dayjs.fenxianglu.cn/category/query.html](https://dayjs.fenxianglu.cn/category/query.html)，如有侵权，请联系删除。