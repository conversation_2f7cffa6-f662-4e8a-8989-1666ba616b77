1.分页示例:
<template>
  <div
    v-for="item in data"
    :key="item.id">
    <span>{{ item.name }}</span>
  </div>
  <button @click="handlePrevPage">上一页</button>
  <button @click="handleNextPage">下一页</button>
  <button @click="handleSetPageSize">设置每页数量</button>
  <span>共有{{ pageCount }}页</span>
  <span>共有{{ total }}条数据</span>
</template>

<script setup>
  import { queryStudents } from './api.js';
  import { usePagination } from '@alova/scene-vue';

  const {
    // 加载状态
    loading,

    // 列表数据
    data,

    // 是否为最后一页
    // 下拉加载时可通过此参数判断是否还需要加载
    isLastPage,

    // 当前页码，改变此页码将自动触发请求
    page,

    // 每页数据条数
    pageSize,

    // 分页页数
    pageCount,

    // 总数据量
    total
  } = usePagination(
    // Method实例获取函数，它将接收page和pageSize，并返回一个Method实例
    (page, pageSize) => queryStudents(page, pageSize),
    {
      // 请求前的初始数据（接口返回的数据格式）
      initialData: {
        total: 0,
        data: []
      },
      initialPage: 1, // 初始页码，默认为1
      initialPageSize: 10 // 初始每页数据条数，默认为10
    }
  );

  // 翻到上一页，page值更改后将自动发送请求
  const handlePrevPage = () => {
    page.value--;
  };

  // 翻到下一页，page值更改后将自动发送请求
  const handleNextPage = () => {
    page.value++;
  };

  // 更改每页数量，pageSize值更改后将自动发送请求
  const handleSetPageSize = () => {
    pageSize.value = 20;
  };
</script>

2.监听筛选条件
<template>
  <input v-model="studentName" />
  <select v-model="clsName">
    <option value="1">Class 1</option>
    <option value="2">Class 2</option>
    <option value="3">Class 3</option>
  </select>
  <!-- ... -->
</template>

<script setup>
  import { ref } from 'vue';
  import { queryStudents } from './api.js';
  import { usePagination } from '@alova/scene-vue';

  // 搜索条件状态
  const studentName = ref('');
  const clsName = ref('');
  const {
    // ...
  } = usePagination((page, pageSize) => queryStudents(page, pageSize, studentName.value, clsName.value), {
    // ...
    watchingStates: [studentName, clsName]
  });
</script>

usePagination((page, pageSize) => queryStudents(page, pageSize, studentName, clsName), {
  // ...
  debounce: 300 // 防抖参数，单位为毫秒数，也可以设置为数组对watchingStates单独设置防抖时间
});


import axios from 'axios' // api: https://github.com/axios/axios
import store from "@/store"		// vuex
import {AxiosResponse, AxiosRequestConfig, AxiosError} from "axios";		// 万恶的ts
import router from '@/router'		//router路由
// 全局的遮罩层控制
import {ElLoading, ElNotification, ElMessage} from 'element-plus'

// 允许跨域
axios.defaults.withCredentials = true
// 超时设置，不建议在这里设置超时
// axios.default.timeout = 10000
// 如果是生产模式 ，就是“/”，如果不是，就是'http://127.0.0.1:8000/'，根据实际情况来
const baseUrl = process.env.NODE_ENV === 'production' ? `/` : 'http://127.0.0.1:8000/',
// 请求拦截器
axios.interceptors.request.use((config: AxiosRequestConfig) => {
	// 添加令牌，
	// 启动遮罩层
}, (error: AxiosError) => {
	//  出现错误，取消遮罩层
}

// 响应拦截器，判断返回码，如果是200或者201，就将结果返回组件，如果不是200或者201，就提示错误
axios.interceptors.response.use(
    axios.interceptors.response.use(
        // 请求结束，关闭遮罩层
        return response
    },
    (error: AxiosError) => {
        // 请求出现错误，请求结束，关闭遮罩层
        // 错误处理
        return error
    }
);
// 封装get方法
export function $get(api: any, params: any) {
    return axios({
        method: "get",
        url: baseUrl + api,
        params: params,
    }).then(response => response.data)
}
// 封装post JSON请求
export function $json(api: any, data: any) {
    return axios({
        method: 'post',
        url: baseUrl + api,
        headers: {'Content-Type': 'application/json'},
        data: data,
    }).then(response => response.data).catch((err) => {
        console.log(err)
    })
}
// 封装post formdata请求
export function $form(api: any, data: any) {
    return axios({
        method: 'post',
        url: baseUrl + api,
        // baseURL: root,
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        data: data,
        transformRequest: [function (data) {
            let ret = ''
            for (const it in data) {
                ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
            }
            return ret
        }]
    }).then(response => response.data).catch((err) => {
        console.log(err)
    })
}

----------------------------store用法---------------------------------
<template>
  <div>
    <button @click="saveObjectData({ name: 'John Doe', age: 30 })">Save Object Data</button>
    <p>Saved Data: {{ savedData }}</p>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';

export default {
  computed: {
    ...mapState('persist', ['getData']),
    savedData() {
      // 如果存储的是JSON字符串，这里需要解析
      return this.getData ? JSON.parse(this.getData) : null;
    },
  },
  methods: {
    ...mapActions('persist', ['saveObjectData']),
  },
};
</script>

//防抖函数
function myFunction() {
    console.log('Function called');
}
const debouncedFunction = lodash.debounce(myFunction, 300);
// 调用debouncedFunction
debouncedFunction();