import { VxeUI } from 'vxe-pc-ui'
import XEUtils from 'xe-utils'

VxeUI.formats.add('FormatDateDefault', {
  tableCellFormatMethod ({ cellValue }) {
    return cellValue ? XEUtils.toDateString(cellValue, 'yyyy-MM-dd') : '-'
  }
})

VxeUI.formats.add('FormatDateTime', {
  tableCellFormatMethod ({ cellValue }) {
    return cellValue ? XEUtils.toDateString(cellValue, 'yyyy-MM-dd HH:mm:ss') : '-'
  }
})

// 定义了两个日期格式化方法：
// FormatDateDefault：将日期格式化为 "yyyy-MM-dd" 格式
// FormatDateTime：将日期格式化为 "yyyy-MM-dd HH:mm:ss" 格式
// 使用了 xe-utils 工具库进行日期转换
// 如果值为空，则显示 "-" 符号