import request from '@/api/index'
import { IDept, IDeptList } from '../entity/dept'
import { IDeptQuery } from '../entity/dept'
import { IUserList } from '../entity/user'

/**
 * 获取部门分页列表
 * @param params 查询参数
 * @returns Promise
 */
export const getDeptList = (params: IDeptQuery) => {
  return request.post<IDeptList>('/sysDept/getDeptTree', params)
}
/**
 * Demo专用
 * @param params Demo
 * @returns 
 */
export const demoDeptManager = (params: IDeptQuery) => {
  return request.post<IDeptList>('/demo/deptManager', params)
}
/**
 * Demo专用 
 * 获取部门人员列表
 * @param params 查询参数
 * @returns Promise
 */
export const demoDeptUserList = (params: IDeptQuery) => {
  return request.post<IUserList>('/demo/userInfos', params)
}




/**
 * 获取部门人员列表
 * @param params 查询参数
 * @returns Promise
 */
export const getDeptUserList = (params: IDeptQuery) => {
  return request.post<IUserList>('/sysDept/getUserDeptAndChildUserDept', params)
}

/**
 * 新增部门
 * @param params 新增参数
 * @returns Promise
 */
export const addDeptUser = (params: IDept) => {
  return request.post<IDept>('/sysDept/addDeptUser', params)
}



/* 
新增部门
*/
export const saveOrUpdateEntity = (params: IDept) => {
  return request.post<IDept>('/sysDept/saveOrUpdateEntity', params)
}

