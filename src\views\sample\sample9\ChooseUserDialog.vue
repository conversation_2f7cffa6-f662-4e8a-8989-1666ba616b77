<!--
* Description: 选择人员弹框
* Created by huqingchao
* Date: 2024/10/24 10:38
* Update: 2024/10/24 10:38
-->
<script setup>
import {reactive, ref, watch, nextTick} from "vue";
import {Modal} from "@opentiny/vue";
import {iconClose} from '@opentiny/vue-icon'
import {getToUserList} from '@/api/methods/role.js'
const TinyIconClose = iconClose()
const props = defineProps({
  roleId: [String, Number],
})
const visible = defineModel()
const emit = defineEmits(['handleConfirm'])
const searchName = ref('')
const tableRef = ref()

const gridOptions = reactive({
  showOverflow: true,
  border: true,
  height: '500px',
  columns: [
    { type: 'checkbox', width: 60 },
    { field: 'sname', title: '姓名', minWidth: 160, },
    { field: 'username', title: '用户名', minWidth: 160, },
    { field: 'contactInfo', title: '手机号', minWidth: 160, },
  ],
  data: []
})

const chooseRows = ref([])
const gridEvents = {
  checkboxChange({checked, row,}) {
    if (checked) {
      chooseRows.value.push(row)
    } else {
      const index = chooseRows.value.findIndex(item => item.id === row.id)
      chooseRows.value.splice(index, 1)
    }
  },
  checkboxAll({ checked, records }) {
    gridOptions.data.forEach(item => {
      const index = chooseRows.value.findIndex(item1 => item1.id === item.id)
      if (index > -1) {
        chooseRows.value.splice(index, 1)
      }
    })
    if (checked) {
      chooseRows.value = [
        ...records,
      ]
    }
  }
}

const handlePageData = () => {
  gridOptions.loading = true
  getToUserList({
    roleId: props.roleId,
    sname: searchName.value
  }).then(res => {
    gridOptions.data = res.data.list
    const chooseIds = chooseRows.value.map(item => item.id)
    const rows = gridOptions.data.filter(item => chooseIds.includes(item.id))
    nextTick(() => {
      tableRef.value.setCheckboxRow(rows, true)
    })
    gridOptions.loading = false
  })
}

const handleSearch = () => {
  handlePageData()
}

const handleDeleteChoose = (item, index) => {
  chooseRows.value.splice(index, 1)
  tableRef.value.setCheckboxRow(item, false)
}


const handleSave = () => {
  if (!chooseRows.value.length) {
    Modal.alert({ message: '请至少选择一项', status: 'warning' })
    return
  }
  emit('handleConfirm', chooseRows.value)
}

watch(visible, newVal => {
  if (newVal) {
    searchName.value = ''
    chooseRows.value = []
    handleSearch()
  }
})
</script>

<template>
  <tiny-dialog-box append-to-body v-model:visible="visible" title="选择人员" width="800px" class="base-dialog-box">
    <div class="inner-right">
      <div class="right-operate">
        <div class="search-wrapper">
          <tiny-input v-model="searchName" placeholder="请输入人员姓名" size="small" clearable></tiny-input>
          <tiny-button type="primary" size="small" @click="handleSearch">查询</tiny-button>
        </div>
      </div>
      <vxe-grid ref="tableRef" v-bind="gridOptions" v-on="gridEvents"></vxe-grid>
      <div class="choose-list" v-if="chooseRows.length">
        <div class="choose-list-title">已选：</div>
        <div class="choose-list-wrapper">
          <div class="choose-item" v-for="(item, index) in chooseRows" :key="item.id">
            <span class="choose-item-title" :title="item.sname">{{ item.sname }}</span>
            <TinyIconClose @click="handleDeleteChoose(item, index)"></TinyIconClose>
            <span>，</span>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <tiny-button @click="visible = false">取消</tiny-button>
      <tiny-button type="primary" @click="handleSave">确定</tiny-button>
    </template>
  </tiny-dialog-box>
</template>

<style scoped lang="less">
.right-operate{
  display: flex;
  justify-content: end;
  margin-bottom: 10px;
  .search-wrapper{
    display: flex;
  }
}

.select-info{
  margin-top: 20px;
  border: 1px solid #d5ecff;
  background: rgba(242, 249, 255, 0.54);
  border-radius: 5px;
  padding: 10px;
  font-size: 16px;
}
.choose-list{
  font-size: 14px;
  margin-top: 20px;
  &-title{
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  &-wrapper{
    background: #f2f9ff;
    display: flex;
    flex-wrap: wrap;
    max-height: 120px;
    overflow-y: auto;
    padding: 5px;
    min-height: 40px;
  }
  .choose-item{
    display: flex;
    align-items: center;
    padding: 5px;
    &-title{
      display: inline-block;
      max-width: 120px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 3px;
    }
    svg{
      margin-top: 2px;
      cursor: pointer;
    }
  }
}
</style>
