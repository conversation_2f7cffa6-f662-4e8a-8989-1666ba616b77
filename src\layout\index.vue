<!--
* Description: 布局index
* Created by huqingchao
* Date: 2024/10/29 18:18
* Update: 2024/10/29 18:18
-->
<template>
  <div class="layout">
    <vxe-layout-container vertical>
      <vxe-layout-header>
        <Header></Header>
      </vxe-layout-header>

      <vxe-layout-container>
        <vxe-layout-aside width="200px">
          <LeftMenu></LeftMenu>
        </vxe-layout-aside>

        <vxe-layout-container vertical>
          <vxe-layout-body>
            <TagsView></TagsView>
            <div class="main-container">
              <router-view></router-view>
            </div>
          </vxe-layout-body>
        </vxe-layout-container>
      </vxe-layout-container>
    </vxe-layout-container>
  </div>
</template>

<script setup>
import Header from './header.vue'
import LeftMenu from './leftMenu.vue'
import TagsView from './tagsView.vue'
</script>

<style lang="less" scoped>
.layout {
  height: 100%;
  .main-container{
    padding: 10px;
    height: calc(100% - var(--dx-ui-tab-height, 40px));
    min-width: 800px;
    overflow-y: auto;
    background-color: rgba(245, 246, 250, 1);
  }
}
</style>
