<!--
* Description: 用户管理
* Created by hu<PERSON>cha<PERSON>
* Date: 2024/10/29 18:16
* Update: 2024/10/29 18:16
-->
<script setup>
import {ref, reactive, onMounted, computed, nextTick, watch} from 'vue'
import useRouteTitle from "@/hooks/useRouteTitle.js";
import usePickerOptions from "@/hooks/usePickerOptions.js";
import {
  iconEyeopen,
  iconDel,
  iconEditorBackground,
  iconDoubleRight,
  iconDeltaRight,
  iconPlus
} from '@opentiny/vue-icon'
import {getPage, saveOrUpdate, deleteById, getInfoById, resetPasswords, sysUserExport} from '@/api/methods/user.js'
import {Modal} from "@opentiny/vue";
import {getList as getRoleList} from '@/api/methods/role.js'
import { useOperatePermission } from '@/hooks/useOperatePermission.js'
import { useCollapse } from '@/hooks/useCollapse.js'
import ruleMap from '@/utils/ruleMap.js'
import {exportData} from '@/utils/exportData.js'
const { btnPermission } = useOperatePermission()
const tableParentRef = ref(null)
const { isCollapse, handleCollapse, tableWrapperHeight } = useCollapse(tableParentRef)

const TinyIconEyeopen = iconEyeopen()
const TinyIconEditorBackground = iconEditorBackground()
const TinyIconDel = iconDel()
const TinyIconDoubleRight = iconDoubleRight()
const TinyIconDeltaRight = iconDeltaRight()
const TinyIconPlus = iconPlus()

const { title } = useRouteTitle()

const searchForm = reactive({
  username: '',
  sname: '',
  roleName	: '',
  status: null,
  updateUserName: '',
  dateRangeValue: [],
})

const handleSearch = () => {
  gridOptions.pagerConfig.currentPage = 1
  handlePageData()
}

const handleResetSearch = () => {
  searchForm.username = ''
  searchForm.sname = ''
  searchForm.roleName = ''
  searchForm.status = null
  searchForm.updateUserName = ''
  searchForm.dateRangeValue =  []
  handleSearch()
}

const handlePageData = () => {
  gridOptions.loading = true
  const params = {
    ...searchForm,
    roleName: getRoleName(searchForm.roleName),
    startDate: searchForm.dateRangeValue ? searchForm.dateRangeValue[0] : null,
    endDate: searchForm.dateRangeValue ? (searchForm.dateRangeValue[1] ? searchForm.dateRangeValue[1].replace('00:00:00', '23:59:59') : undefined) : null,
    pageNumber: gridOptions.pagerConfig.currentPage,
    pageSize: gridOptions.pagerConfig.pageSize
  }
  console.log(JSON.stringify(searchForm))
  delete params.dateRangeValue
  getPage(params).then(res => {
    console.log("1111111111111111111111")
    // if (res && res.data && res.data.list) {
    //   const data = res.data
    //   gridOptions.data = data.list
    //   gridOptions.pagerConfig.total = data.total
    // }else{
    //   console.error('Unexpected response structure:', res)
    // }
    gridOptions.loading = false // 加载完成，设置 loading 为 false
    const data = res.data
    gridOptions.data = data.records
    gridOptions.pagerConfig.total = data.totalRow
    gridOptions.loading = false

  }).catch(error => {
    console.error('Error fetching page data:', error)
    gridOptions.loading = false // 加载失败，设置 loading 为 false
  })
}

const handleExport = () => {
  sysUserExport({
    username: searchForm.username,
    sname: searchForm.sname,
    status: searchForm.status,
    roleName: getRoleName(searchForm.roleName),
    startDate: searchForm.dateRangeValue ? searchForm.dateRangeValue[0] : null,
    endDate: searchForm.dateRangeValue ? (searchForm.dateRangeValue[1] ? searchForm.dateRangeValue[1].replace('00:00:00', '23:59:59') : undefined) : null,
  }).then(res => {
    exportData(res, '用户管理列表.xlsx')
  })
}

const gridRef = ref(null)

const gridOptions = reactive({
  showOverflow: true,
  border: true,
  loading: false,
  height: '100%',
  pagerConfig: {
    total: 0,
    currentPage: 1,
    pageSize: 10,
  },
  columns: [
    { type: 'checkbox', width: 70, fixed: 'left', align: 'center' },
    { type: 'seq', width: 70, fixed: 'left', align: 'center' },
    { title: '操作', width: 250, slots: { default: 'operate_default' } },
    // { field: 'status', title: '状态', width: 100, slots: { default: 'flag_default'} },
    { field: 'username', title: '用户名', minWidth: 120 },
    { field: 'sname', title: '姓名', minWidth: 120 },
    { field: 'roleName', title: '角色', minWidth: 120 },
    { field: 'phone', title: '联系方式', minWidth: 120 },
    { field: 'createUserName', title: '操作人', minWidth: 140 },
    { field: 'createDate', title: '操作时间', minWidth: 140},
  ],
  data: []
})
const gridEvents = {
  pageChange ({ pageSize, currentPage }) {
    gridOptions.pagerConfig.currentPage = currentPage
    gridOptions.pagerConfig.pageSize = pageSize
    handlePageData()
  }
}

const state = ref('list')
const boxVisibility = ref(false)
const operateType = ref('add')
const currentRow = ref(null)
const guid = ref()

const { datePickerOptions } = usePickerOptions()

const dialogTitle = computed(() => {
  let title = '新建用户'
  if (operateType.value === 'edit') {
    title = '编辑'
  } else if (operateType.value === 'view') {
    title = '查看'
  }
  return title
})

const handleAdd = () => {
  currentRow.value = null
  boxVisibility.value = true
  operateType.value = 'add'
}

const handleResetPassword = () => {
  const userIdList = gridRef.value.getCheckboxRecords().map(item => item.userId)
  if (!userIdList.length) {
    Modal.message({
      status: 'warning',
      message: '至少选择一个用户！',
    })
    return
  }
  Modal.confirm('确定要重置密码？').then(res => {
    if (res === 'confirm') {
      resetPasswords({
        userIdList
      }).then(res => {
        handleSearch()
        Modal.message({
          status: 'success',
          message: '重置密码成功',
        })
      })
    }
  })
}

const handleOperate = (row, type) => {
  currentRow.value = row
  operateType.value = type
  boxVisibility.value = true
  getInfoById({
    userId: currentRow.value.userId,
  }).then(res => {
    const data = res.data
    baseFormData.sname = data.sname
    baseFormData.username = data.username
    baseFormData.phone = data.phone
    baseFormData.roleIdList = data.roleIdList
    baseFormData.status = data.status
    baseFormData.remark = data.remark
  })
}

const baseFormData = reactive({
  sname: '',
  username: '',
  status: 1,
  phone: '',
  roleIdList: [],
  remark: '',
  userId: ''
})

const formRules = ref({
  contactInfo: [
    { validator: ruleMap['onlyNumber']['validator'], trigger: 'blur' },
  ]
})

const formRef = ref(null)
const handleSave = () => {
  formRef.value.validate(valid => {
    if (valid) {
      const params = {
        ...baseFormData,
      }
      if (operateType.value === 'edit') {
        params.userId = currentRow.value.userId
      }
      saveOrUpdate(params).then(res => {
        boxVisibility.value = false
        if (operateType.value === 'edit') {
          console.log('edit')
          handlePageData()
        } else {
          console.log('save')
          handleResetSearch()
        }
      })
    }
  })
}

const handleDelete = row => {
  console.log(JSON.stringify(row))
  if (row.status) {
    return
  }
  Modal.confirm('您确定要删除吗？').then(res => {
    if (res === 'confirm') {
      deleteById({
        userId: row.userId,
      }).then(res => {
        Modal.message({
          status: 'success',
          message: '删除成功',
        })
        handleSearch()
      })
    }
  })
}

const goBack = () => {
  state.value = 'list'
  handlePageData()
}
const getRoleName = id => {
  let name = ''
  if (id) {
    const item = roleOptions.value.find(item => item.id === id)
    name = item.sname
  }
  return name
}
const roleOptions = ref([])
const statusOptions = ref([
  {
    value: 1,
    text: '启用',
  },
  {
    value: 0,
    text: '禁用',
  }
])

const getRoleOptions = () => {
  getRoleList({
    sname: '',
    status: 1,
  }).then(res => {
    roleOptions.value = res.data
  })
}
onMounted(() => {
  handlePageData()
  getRoleOptions()
})
</script>

<template>
  <div class="user">
    <div class="base-search">
      <div class="base-title"> {{title}} </div>
      <tiny-form :model="searchForm" size="small" @keyup.enter="handleSearch" label-width="120px">
        <tiny-layout :cols="24">
          <tiny-row>
            <tiny-col :span="6">
              <tiny-form-item label="用户名" prop="username">
                <tiny-input v-model="searchForm.username" clearable placeholder="请输入"></tiny-input>
              </tiny-form-item>
            </tiny-col>
            <tiny-col :span="6">
              <tiny-form-item label="姓名" prop="sname">
                <tiny-input v-model="searchForm.sname" clearable placeholder="请输入"></tiny-input>
              </tiny-form-item>
            </tiny-col>
            <tiny-col :span="6">
              <tiny-form-item label="角色" prop="roleName">
                <tiny-select v-model="searchForm.roleName" placeholder="请选择" filterable clearable>
                  <tiny-option v-for="item in roleOptions" :key="item.roleId" :label="item.sname" :value="item.roleId"></tiny-option>
                </tiny-select>
              </tiny-form-item>
            </tiny-col>
            <tiny-col :span="2">
              <div :class="['base-search-collapse', { 'is--collapse': isCollapse }]" @click="handleCollapse">
                <span>{{isCollapse ? '展开' : '收起' }}</span>
                <TinyIconDoubleRight></TinyIconDoubleRight>
              </div>
            </tiny-col>
            <tiny-col :span="4">
              <div class="base-search-operate">
                <tiny-button size="small" @click="handleResetSearch">重置</tiny-button>
                <tiny-button size="small" type="info" @click="handleSearch">查询</tiny-button>
              </div>
            </tiny-col>
          </tiny-row>
          <tiny-row v-show="!isCollapse">
            <tiny-col :span="6">
              <tiny-form-item label="状态" prop="status">
                <tiny-select v-model="searchForm.status" placeholder="请选择" filterable clearable>
                  <tiny-option v-for="item in statusOptions" :key="item.value" :label="item.text" :value="item.value"></tiny-option>
                </tiny-select>
              </tiny-form-item>
            </tiny-col>
            <tiny-col :span="6">
              <tiny-form-item label="操作人" prop="updateUserName">
                <tiny-input v-model="searchForm.updateUserName" clearable placeholder="请输入"></tiny-input>
              </tiny-form-item>
            </tiny-col>
            <tiny-col :span="6">
              <tiny-form-item label="操作时间" prop="dateRangeValue">
                <tiny-date-picker v-model="searchForm.dateRangeValue" type="daterange" :picker-options="datePickerOptions" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss" clearable></tiny-date-picker>
              </tiny-form-item>
            </tiny-col>
          </tiny-row>
        </tiny-layout>
      </tiny-form>
    </div>
    <div class="base-content">
      <div class="base-content-operate">
        <tiny-button type="info" @click="handleAdd" v-if="btnPermission.add">新建用户</tiny-button>
        <tiny-button type="info" @click="handleResetPassword" v-if="btnPermission.reset">重置密码</tiny-button>
        <tiny-button type="info" plain @click="handleExport" v-if="btnPermission.add">导出</tiny-button>
      </div>
      <div ref="tableParentRef" :style="{ height: tableWrapperHeight, overflow: 'hidden' }">
        <vxe-grid ref="gridRef" v-bind="gridOptions" v-on="gridEvents">
          <template #flag_default="{ row }">
            <tiny-tag size="medium" type="success" value="已启用" v-if="row.status"></tiny-tag>
            <tiny-tag size="medium" type="danger" value="禁用" v-else></tiny-tag>
          </template>
          <template #operate_default="{ row }">
            <div class="base-table-operate__icon1">
              <div v-if="btnPermission.view" class="icon-wrap" @click="handleOperate(row, 'view')"><TinyIconEyeopen></TinyIconEyeopen>查看</div>
              <div v-if="btnPermission.edit" class="icon-wrap" @click="handleOperate(row, 'edit')"><TinyIconEditorBackground></TinyIconEditorBackground>编辑</div>
              <div v-if="btnPermission.delete" :class="['icon-wrap', row.status ? 'is-disabled' : '']" @click="handleDelete(row)"><TinyIconDel></TinyIconDel>删除</div>
            </div>
          </template>
        </vxe-grid>
      </div>
    </div>
    <tiny-dialog-box v-model:visible="boxVisibility" :title="dialogTitle" width="800px" class="base-dialog-box">
      <tiny-form ref="formRef" :model="baseFormData" :rules="formRules" label-width="150px" class="base-form-border" :disabled="operateType === 'view'">
        <tiny-form-item label="姓名" prop="sname">
          <tiny-input v-model="baseFormData.sname" placeholder="请输入" clearable :maxlength="10"></tiny-input>
        </tiny-form-item>
        <tiny-form-item label="用户名" prop="username" required>
          <tiny-input v-model="baseFormData.username" placeholder="请输入" clearable></tiny-input>
        </tiny-form-item>
        <tiny-form-item label="联系方式" prop="phone">
          <tiny-input v-model="baseFormData.phone" placeholder="请输入" clearable></tiny-input>
        </tiny-form-item>
        <tiny-form-item label="角色" prop="roleIdList">
          <tiny-select v-model="baseFormData.roleIdList" placeholder="请选择" filterable clearable multiple collapse-tags>
            <tiny-option v-for="item in roleOptions" :key="item.roleId" :label="item.sname" :value="item.roleId"></tiny-option>
          </tiny-select>
        </tiny-form-item>
        <tiny-form-item label="状态" prop="status" required>
          <vxe-switch v-model="baseFormData.status" open-label="启用" close-label="禁用" :open-value="1" :close-value="0"></vxe-switch>
        </tiny-form-item>
        <tiny-form-item label="备注" prop="remark">
          <tiny-input v-model="baseFormData.remark" type="textarea" clearable placeholder="请输入" :maxlength="200"></tiny-input>
        </tiny-form-item>
      </tiny-form>
      <template #footer v-if="operateType !== 'view'">
        <div style="display: flex; justify-content: center;">
          <tiny-button @click="boxVisibility = false" >取消</tiny-button>
          <tiny-button type="info" @click="handleSave" >保存</tiny-button>
        </div>
      </template>
    </tiny-dialog-box>
  </div>
</template>

<style scoped lang="less">
.base-dialog-box.tiny-dialog-box__header{
  font-size: 10px;
}
</style>
