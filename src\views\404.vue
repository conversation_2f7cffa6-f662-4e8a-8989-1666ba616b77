<!--
* Description: 404页面
* Created by <PERSON><PERSON>qingcha<PERSON>
* Date: 2024/10/29 18:19
* Update: 2024/10/29 18:19
-->
<template>
	<div class="error-page">
    <div>404找不到页面</div>
    <div class="error-page-operate">
      <tiny-button @click="goHome">返回首页</tiny-button>
      <tiny-button  @click="goBack">返回上一页</tiny-button>
    </div>
	</div>
</template>

<script setup>
import { useRouter } from 'vue-router'
const router = useRouter()
const goHome = () => {
  router.push({name: 'home'})
}
const goBack = () => {
  router.go(-1)
}
</script>

<style lang="less" scoped>
.error-page{
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  font-size: 24px;
  &-operate{
    margin-top: 10px;
  }
}
</style>
