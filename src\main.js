import {createApp} from 'vue'
import App from './App.vue'
//状态管理
import {createPinia} from 'pinia'
//vxe-utils工具库
import XEUtils from 'xe-utils';
// 将 XEUtils 添加到 globalThis 上
globalThis.XEUtils = XEUtils;

import piniaPersist from './utils/piniaPersist';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';

const pinia = createPinia();
piniaPersist(pinia); // 使用自定义持久化插件
pinia.use(piniaPluginPersistedstate); // 使用默认持久化插件

// 全局引入lodash
import _ from 'lodash';
globalThis.lodash = _;

//全局引入vxe-table组件
import {
  VxeTable,
  VxeColumn,
  VxeColgroup,
  VxeGrid,
  VxeToolbar
} from 'vxe-table'

import {
  VxeUI,
  VxeLoading,
  VxeButton,
  VxeButtonGroup,
  VxeCheckbox,
  VxeCheckboxGroup,
  VxeFormItem,
  VxeForm,
  VxeIcon,
  VxeInput,
  VxeModal,
  VxePager,
  VxePulldown,
  VxeRadio,
  VxeRadioButton,
  VxeRadioGroup,
  VxeSelect,
  VxeSwitch,
  VxeTextarea,
  VxeTooltip,
  VxeNumberInput,
  VxeImagePreview,
  VxeDatePicker,
  VxeTree,
  VxeTreeSelect,
  VxeUpload,
  VxeTabs,
  VxeTabPane,
  VxeLayoutAside,
  VxeLayoutBody,
  VxeLayoutContainer,
  VxeLayoutFooter,
  VxeLayoutHeader,
  VxeMenu,
  VxeCollapse,
  VxeCollapsePane,
  VxeOption,
  VxeText
} from 'vxe-pc-ui'

globalThis.VxeUI = VxeUI;
// ...

// 导入主题变量，也可以重写主题变量
import 'vxe-table/styles/cssvar.scss'
import 'vxe-pc-ui/styles/cssvar.scss'

// 导入默认的语言
import zhCN from 'vxe-pc-ui/lib/language/zh-CN'

VxeUI.setI18n('zh-CN', zhCN)
VxeUI.setLanguage('zh-CN')

function LazyVxeTable(app) {
  app.use(VxeTable)
  app.use(VxeColumn)
  app.use(VxeColgroup)
  app.use(VxeGrid)
  app.use(VxeToolbar)
}

function LazyVxeUI(app) {
  app.use(VxeButton)
  app.use(VxeLoading)
  app.use(VxeButtonGroup)
  app.use(VxeCheckbox)
  app.use(VxeCheckboxGroup)
  app.use(VxeFormItem)
  app.use(VxeForm)
  app.use(VxeIcon)
  app.use(VxeInput)
  app.use(VxeModal)
  app.use(VxePager)
  app.use(VxePulldown)
  app.use(VxeRadio)
  app.use(VxeRadioButton)
  app.use(VxeRadioGroup)
  app.use(VxeSelect)
  app.use(VxeSwitch)
  app.use(VxeTextarea)
  app.use(VxeTooltip)
  app.use(VxeNumberInput)
  app.use(VxeImagePreview)
  app.use(VxeDatePicker)
  app.use(VxeTree)
  app.use(VxeTreeSelect)
  app.use(VxeUpload)
  app.use(VxeTabs)
  app.use(VxeTabPane)
  app.use(VxeLayoutAside)
  app.use(VxeLayoutBody)
  app.use(VxeLayoutContainer)
  app.use(VxeLayoutFooter)
  app.use(VxeLayoutHeader)
  app.use(VxeMenu)
  app.use(VxeCollapse)
  app.use(VxeCollapsePane)
  app.use(VxeOption)
  app.use(VxeText)
}
// 自定义vxe-table格式化数据
import '@/utils/vxeFormat.js'

// 引入路由
import router from './router/index';
import 'dx-ui-var'
// 添加全局样式，需放在路由后
import '@/styles/index.less'
import '@/assets/iconfont/iconfont.css'

//引入全局的axios
import axios from "./api/index.js";

globalThis.axios = axios;

// 引入全局插件
import './plugins'

// 添加 vxe-ui 插件
const app = createApp(App);
app.use(LazyVxeTable).use(LazyVxeUI).use(pinia).use(router);

import {useMainStore} from './utils/store';

const mainStore = useMainStore();
globalThis.mainStore = mainStore;

import dayjs from 'dayjs';
globalThis.dayjs = dayjs;

app.mount('#app');
//加载全局配置
VxeUI.setConfig({
  zIndex: 9999,
  alert: {},
  anchor: {},
  anchorLink: {},
  breadcrumb: {
    separator: '/'
  },
  breadcrumbItem: {},
  button: {},
  buttonGroup: {},
  checkbox: {},
  checkboxGroup: {},
  col: {},
  colgroup: {},
  collapse: {},
  collapsePane: {},
  column: {},
  drawer: {
    // size: null,
    position: 'left',
    showHeader: true,
    lockView: true,
    mask: true,
    showTitleOverflow: true,
    showClose: true,
    padding: true
  },
  form: {
    // preventSubmit: false,
    // size: null,
    // colon: false,
    validConfig: {
      showMessage: true,
      autoPos: true
    },
    tooltipConfig: {
      enterable: true
    },
    titleAsterisk: true
  },
  formDesign: {
    height: 400,
    showPc: true
  },
  formGather: {},
  formItem: {},
  formView: {},
  grid: {},
  icon: {},
  image: {},
  imagePreview: {},
  input: {
    // size: null,
    // transfer: false
    // parseFormat: 'yyyy-MM-dd HH:mm:ss.SSS',
    // labelFormat: '',
    // valueFormat: '',
    startDate: new Date(1900, 0, 1),
    endDate: new Date(2100, 0, 1),
    startDay: 1,
    selectDay: 1,
    digits: 2,
    controls: true
  },
  layoutAside: {},
  layoutBody: {},
  layoutContainer: {},
  layoutFooter: {},
  layoutHeader: {},
  link: {
    underline: true
  },
  listDesign: {
    height: 400,
    showPc: true
  },
  list: {
    // size: null,
    scrollY: {
      enabled: true,
      gt: 100
      // oSize: 0
    }
  },
  loading: {},
  modal: {
    // size: null,
    top: 15,
    showHeader: true,
    minWidth: 340,
    minHeight: 140,
    lockView: true,
    mask: true,
    duration: 3000,
    marginSize: 0,
    dblclickZoom: true,
    showTitleOverflow: true,
    showClose: true,
    padding: true,
    draggable: true,
    showConfirmButton: null,
    // storage: false,
    storageKey: 'VXE_MODAL_POSITION'
  },
  optgroup: {},
  option: {},
  pager: {
    // size: null,
    // autoHidden: false,
    // perfect: true,
    // pageSize: 10,
    // pagerCount: 7,
    // layouts: ['PrevJump', 'PrevPage', 'Jump', 'PageCount', 'NextPage', 'NextJump', 'Sizes', 'Total']
    pageSizes: [10, 20, 30, 50, 100],
    background: true,
    layouts: ['Total', 'Sizes', 'PrevPage', 'JumpNumber', 'NextPage', 'FullJump'],
    align: 'center'
  },
  pulldown: {},
  radio: {
    strict: true
  },
  radioButton: {
    strict: true
  },
  radioGroup: {
    strict: true
  },
  row: {},
  select: {
    multiCharOverflow: 8
  },
  switch: {},
  tabPane: {},
  table: {
    columnConfig: {
      resizable: true
    },
    rowConfig: {
      isHover: true,
      isCurrent: true,
    },
  },
  tabs: {},
  textarea: {},
  toolbar: {},
  tips: {},
  tooltip: {
    // size: null,
    trigger: 'hover',
    theme: 'dark',
    enterDelay: 500,
    leaveDelay: 300
  },
  tree: {
    size: 'mini' 
  },
  treeSelect: {
    popupConfig:{
      width: 300,
    }
  },
  upload: {
    mode: 'all',
    imageTypes: ['jpg', 'jpeg', 'png', 'gif']
  }
})

//全局得图标配置
VxeUI.setIcon({
  // loading
  LOADING: 'vxe-icon-spinner roll vxe-loading--default-icon',

  // button
  BUTTON_DROPDOWN: 'vxe-icon-arrow-down',
  BUTTON_LOADING: 'vxe-icon-spinner roll',

  // menu
  MENU_ITEM_EXPAND_OPEN: 'vxe-icon-arrow-down rotate180',
  MENU_ITEM_EXPAND_CLOSE: 'vxe-icon-arrow-down',

  // select
  SELECT_LOADED: 'vxe-icon-spinner roll',
  SELECT_OPEN: 'vxe-icon-caret-down rotate180',
  SELECT_CLOSE: 'vxe-icon-caret-down',

  // pager
  PAGER_HOME: 'vxe-icon-home-page',
  PAGER_END: 'vxe-icon-end-page',
  PAGER_JUMP_PREV: 'vxe-icon-arrow-double-left',
  PAGER_JUMP_NEXT: 'vxe-icon-arrow-double-right',
  PAGER_PREV_PAGE: 'vxe-icon-arrow-left',
  PAGER_NEXT_PAGE: 'vxe-icon-arrow-right',
  PAGER_JUMP_MORE: 'vxe-icon-ellipsis-h',

  // radio
  RADIO_CHECKED: 'vxe-icon-radio-checked-fill',
  RADIO_UNCHECKED: 'vxe-icon-radio-unchecked',

  // checkbox
  CHECKBOX_INDETERMINATE: 'vxe-icon-checkbox-indeterminate-fill',
  CHECKBOX_CHECKED: 'vxe-icon-checkbox-checked-fill',
  CHECKBOX_UNCHECKED: 'vxe-icon-checkbox-unchecked',

  // input
  INPUT_CLEAR: 'vxe-icon-error-circle-fill',
  INPUT_PWD: 'vxe-icon-eye-fill',
  INPUT_SHOW_PWD: 'vxe-icon-eye-fill-close',
  INPUT_PREV_NUM: 'vxe-icon-caret-up',
  INPUT_NEXT_NUM: 'vxe-icon-caret-down',
  INPUT_DATE: 'vxe-icon-calendar',
  INPUT_SEARCH: 'vxe-icon-search',

  // modal
  MODAL_ZOOM_IN: 'vxe-icon-square',
  MODAL_ZOOM_OUT: 'vxe-icon-maximize',
  MODAL_CLOSE: 'vxe-icon-close',
  MODAL_INFO: 'vxe-icon-info-circle-fill',
  MODAL_SUCCESS: 'vxe-icon-success-circle-fill',
  MODAL_WARNING: 'vxe-icon-warning-circle-fill',
  MODAL_ERROR: 'vxe-icon-error-circle-fill',
  MODAL_QUESTION: 'vxe-icon-question-circle-fill',
  MODAL_LOADING: 'vxe-icon-spinner roll',

  // form
  FORM_PREFIX: 'vxe-icon-question-circle-fill',
  FORM_SUFFIX: 'vxe-icon-question-circle-fill',
  FORM_FOLDING: 'vxe-icon-arrow-up rotate180',
  FORM_UNFOLDING: 'vxe-icon-arrow-up',

  // form-design
  FORM_DESIGN_STYLE_SETTING: 'vxe-icon-layout',
  FORM_DESIGN_PROPS_PC: 'vxe-icon-pc',
  FORM_DESIGN_PROPS_MOBILE: 'vxe-icon-mobile',
  FORM_DESIGN_WIDGET_ADD: 'vxe-icon-square-plus-fill',
  FORM_DESIGN_WIDGET_COPY: 'vxe-icon-copy',
  FORM_DESIGN_WIDGET_DELETE: 'vxe-icon-delete',
  FORM_DESIGN_WIDGET_OPTION_DELETE: 'vxe-icon-delete',
  FORM_DESIGN_WIDGET_OPTION_EXPAND_OPEN: 'vxe-icon-square-plus',
  FORM_DESIGN_WIDGET_OPTION_EXPAND_CLOSE: 'vxe-icon-square-minus',

  // list-design
  LIST_DESIGN_FIELD_SETTING: 'vxe-icon-custom-column',
  LIST_DESIGN_LIST_SETTING: 'vxe-icon-menu',

  // upload
  UPLOAD_FILE_ERROR: 'vxe-icon-warning-circle-fill',
  UPLOAD_FILE_ADD: 'vxe-icon-upload',
  UPLOAD_FILE_DELETE: 'vxe-icon-delete',
  UPLOAD_IMAGE_RE_UPLOAD: 'vxe-icon-repeat',
  UPLOAD_IMAGE_ADD: 'vxe-icon-add',
  UPLOAD_IMAGE_DELETE: 'vxe-icon-close',
  UPLOAD_LOADING: 'vxe-icon-spinner roll vxe-loading--default-icon',
  UPLOAD_FILE_TYPE_DEFAULT: 'vxe-icon-file',
  UPLOAD_FILE_TYPE_XLSX: 'vxe-icon-file-excel',
  UPLOAD_FILE_TYPE_XLS: 'vxe-icon-file-excel',
  UPLOAD_FILE_TYPE_PDF: 'vxe-icon-file-pdf',
  UPLOAD_FILE_TYPE_PNG: 'vxe-icon-file-image',
  UPLOAD_FILE_TYPE_GIF: 'vxe-icon-file-image',
  UPLOAD_FILE_TYPE_JPG: 'vxe-icon-file-image',
  UPLOAD_FILE_TYPE_JPEG: 'vxe-icon-file-image',
  UPLOAD_FILE_TYPE_MD: 'vxe-icon-file-markdown',
  UPLOAD_FILE_TYPE_PPD: 'vxe-icon-file-ppt',
  UPLOAD_FILE_TYPE_DOCX: 'vxe-icon-file-word',
  UPLOAD_FILE_TYPE_DOC: 'vxe-icon-file-word',
  UPLOAD_FILE_TYPE_ZIP: 'vxe-icon-file-zip',
  UPLOAD_FILE_TYPE_TXT: 'vxe-icon-file-txt',

  // image-preview
  IMAGE_PREVIEW_CLOSE: 'vxe-icon-close',
  IMAGE_PREVIEW_PREVIOUS: 'vxe-icon-arrow-left',
  IMAGE_PREVIEW_NEXT: 'vxe-icon-arrow-right',
  IMAGE_PREVIEW_PCT_FULL: 'vxe-icon-pct-full',
  IMAGE_PREVIEW_PCT_1_1: 'vxe-icon-pct-1-1',
  IMAGE_PREVIEW_ZOOM_OUT: 'vxe-icon-search-zoom-out',
  IMAGE_PREVIEW_ZOOM_IN: 'vxe-icon-search-zoom-in',
  IMAGE_PREVIEW_ROTATE_LEFT: 'vxe-icon-rotate-left',
  IMAGE_PREVIEW_ROTATE_RIGHT: 'vxe-icon-rotate-right',
  IMAGE_PREVIEW_PRINT: 'vxe-icon-print',

  // alert
  ALERT_CLOSE: 'vxe-icon-close',
  ALERT_INFO: 'vxe-icon-info-circle-fill',
  ALERT_SUCCESS: 'vxe-icon-success-circle-fill',
  ALERT_WARNING: 'vxe-icon-warning-circle-fill',
  ALERT_ERROR: 'vxe-icon-error-circle-fill'
})
// 刷新系统时
document.documentElement.setAttribute('data-dx-ui-size', localStorage.getItem('dxSize') || 'medium')

import TinyThemeTool from '@opentiny/vue-theme/theme-tool'

const themeTool = new TinyThemeTool()

themeTool.changeTheme({
  name: 'my-app-custom-styles',
  css: `
    .tiny-button {
      --tv-Button-min-width-small: var(--dx-ui-button-width, 100px);
      --tv-Button-min-width: var(--dx-ui-button-width, 100px);
    }
  `
})
