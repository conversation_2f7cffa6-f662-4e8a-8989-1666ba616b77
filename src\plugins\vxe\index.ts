import VxeUI from 'vxe-pc-ui'

import VxeUIPluginMenu from '@vxe-ui/plugin-menu'
import VxeUIPluginExportXLSX from '@vxe-ui/plugin-export-xlsx'
import VxeUIPluginRenderWangEditor from '@vxe-ui/plugin-render-wangeditor'
import '@vxe-ui/plugin-render-wangeditor/dist/style.css'

// import './config'
// // 格式化
import './format'
// // 渲染器
import './render'

VxeUI.use(VxeUIPluginMenu)
VxeUI.use(VxeUIPluginExportXLSX)
VxeUI.use(VxeUIPluginRenderWangEditor)

// VxeUI 的主入口文件
// 引入并注册了几个重要的 VxeUI 插件：
// VxeUIPluginMenu：菜单插件
// VxeUIPluginExportXLSX：Excel导出插件
// VxeUIPluginRenderWangEditor：富文本编辑器插件
// 导入配置文件、格式化文件和渲染器文件
// 使用 VxeUI.use() 方法注册插件
