import {createAxle} from '@varlet/axle'

const axle = createAxle()
import {Modal} from '@opentiny/vue'
import { useUserStore } from '@/stores/user.js'
import router from '@/router/index.js'

// axle 内置的 axios ，用法和 axios 一模一样，并且和 axle 共享配置。
const {axios} = axle

axios.defaults.baseURL = import.meta.env.PROD ? import.meta.env.VITE_REQUEST_URL : '/api'
axios.defaults.headers.common['Token'] = localStorage.getItem('token') || ''
// axios.defaults.timeout = 30000

// 添加请求前拦截器
axios.interceptors.request.use(
  (config) => {
    // 请求前处理
    return config
  },
  (error) => {
    // 请求错误处理
    return Promise.reject(error)
  }
)

// 添加请求后返回拦截器
axios.interceptors.response.use(
  (response) => {
    // 任何位于 2xx 范围内的状态码都会导致该函数触发
    // 对响应数据做一些事情
    // 处理导出文件流
    if (response.data instanceof Blob) {
      return response.data
    }
    if (response.data.code == 0) {
      return response.data
    } else {
      Modal.message({
        status: 'error',
        message: response.data.msg,
        duration: '1000'
      })
      console.log(response)
      // token过期跳转登录页
      if (response.data.code == 401 || response.data.msg?.includes('token 无效')) {
        const userStore = useUserStore();
        userStore.quit()
        router.push('/login')
      }
      return Promise.reject(response.data)
    }
  },
  (error) => {
    // 任何超出 2xx 范围的状态代码都会导致此函数触发
    // 对响应错误做一些事情
    Modal.message({
      status: 'error',
      message: error.message || '请求失败',
      duration: '1000'
    })
    return Promise.reject(error)
  }
)

export default axios;
