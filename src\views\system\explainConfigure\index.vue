<!--
* Description: 404页面
* Created by hu<PERSON>cha<PERSON>
* Date: 2024/10/29 18:19
* Update: 2024/10/29 18:19
-->
<template>
	<div class="error-page">
    <dev>
      <img src="@/assets/images/developing.png"></img>
    </dev>
    <div class="title">
        <!-- <TinyIconPushpin style="fill: blue;font-size: 30px;margin: 10px;"></TinyIconPushpin> -->
         
        功能正在开发中</div>
	</div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { IconPushpin } from '@opentiny/vue-icon'
const TinyIconPushpin = IconPushpin()
const router = useRouter()
const goHome = () => {
  router.push({name: 'home'})
}
const goBack = () => {
  router.go(-1)
}
</script>

<style lang="less" scoped>
.error-page{
  min-height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  &-operate{
    margin-top: 10px;
  }
  .title{
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
  }
}
</style>
