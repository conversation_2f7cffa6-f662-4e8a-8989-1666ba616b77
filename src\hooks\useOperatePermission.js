/**
 * Description: 操作按钮权限，包括按钮和tab
 * Created by huqingchao
 * Date: 2024/10/29 14:16
 * Update: 2024/10/29 14:16
 */
import { useRoute } from 'vue-router'
import { reactive, ref } from 'vue'
export const useOperatePermission = () => {
  const route = useRoute()
  // 按钮对象
  const btnPermission = reactive({})
  // tab对象
  const tabPermission = reactive({})
  // 当前选中tab
  const activeTabName = ref('')

  if (route.meta) {
    const { btnList, tabList } = route.meta
    console.log("111111111111-------"+JSON.stringify(route.meta))

    btnList?.forEach(item => {
      btnPermission[item.alias] = item.sname
    })
    tabList?.forEach(item => {
      if (!activeTabName.value) {
        activeTabName.value = item.alias
      }
      tabPermission[item.alias] = {
        sname: item.sname,
      }
      if (item.btnList && item.btnList.length) {
        item.btnList.forEach(btn => {
          tabPermission[item.alias][btn.alias] = btn.sname
        })
      }
    })
  }
  console.log("111111111111-------"+JSON.stringify(btnPermission))
    console.log("111111111111-------"+JSON.stringify(tabPermission))
  return {
    btnPermission,
    tabPermission,
    activeTabName
  }
}
