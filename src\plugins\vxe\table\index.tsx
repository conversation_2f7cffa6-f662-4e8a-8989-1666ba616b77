import { VxeUI ,VxeImage, VxeButton, VxeTag} from 'vxe-pc-ui'

// 辅助函数
const handleView = (row: any) => {
  console.log('查看行数据:', row)
}

const handleEdit = (row: any) => {
  console.log('编辑行数据:', row)
}

const handleDelete = (row: any) => {
  VxeUI.modal.confirm('确定要删除该记录吗？').then(() => {
  console.log('删除行数据:', row)
  })
}

/**
 * 表格操作按钮渲染器
 */
VxeUI.renderer.add('TableActionBtn', {
  // 默认显示模板
  renderTableDefault (renderOpts, params) {
    const { row } = params
    return [
      <VxeButton mode="text" status="primary" onClick={() => handleView(row)}>查看</VxeButton>,
      <VxeButton mode="text" status="success" onClick={() => handleEdit(row)}>编辑</VxeButton>,
      <VxeButton mode="text" status="error" onClick={() => handleDelete(row)}>删除</VxeButton>,
    ]
  }
})

/**
 * 表格状态渲染器
 */
VxeUI.renderer.add('TableStatus', {
  // 默认显示模板
  renderTableDefault (renderOpts, params) {
    const { row } = params
    return [
      <VxeTag status={row.status === 'Man' ? 'success' : 'error'}>
        {row.status}
      </VxeTag>
    ]
  }
})

/**
 * 表格图片渲染器
 */
VxeUI.renderer.add('TableImage', {
  // 默认显示模板
  renderTableDefault (renderOpts, params) {
    const { row, column } = params
    const url = row[column.field]
    return url ? <img src={url} style="width: 50px; height: 50px; object-fit: cover;" /> : '-'
  }
})

// 创建单元格图片预览
VxeUI.renderer.add('MyTableCellImg', {
  // 默认显示模板
  renderTableDefault (renderOpts, params) {
    const { row } = params
    return <VxeImage src={row.url} height="60"></VxeImage>
  }
})