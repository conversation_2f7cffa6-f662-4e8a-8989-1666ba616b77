import type { IDict, DictResponse } from '@/api/entity/dict'
import request from '../index.js'
import requests from '@/api/index.ts'
import { IResultData } from '../entity/index.js'

export const getDictPage = function (data) {
  return request({
    url: '/sysDict/getDictTreePage',
    method: 'post',
    data
  })
}

export const saveOrUpdateDict = function (data) {
  return request({
    url: '/sysDict/saveOrUpdate',
    method: 'post',
    data
  })
}

export const removeDict = function (data) {
  return request({
    url: '/sysDict/removeDict',
    method: 'post',
    data
  })
}

export const getDict = function (data) {
  return request({
    url: '/sysDict/getDict',
    method: 'post',
    data
  })
}

export const dictDatas = () => {
  return requests.post<DictResponse>('/sysDict/dictDatas',{})
}