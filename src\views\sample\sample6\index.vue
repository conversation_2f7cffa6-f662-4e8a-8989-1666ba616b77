<template>
    <BmGridLayout :layout="layout" :col-num="12" :is-draggable="false" :is-resizable="false" :vertical-compact="false"
        :margin="[0, 0]" :use-css-transforms="true" :auto-size="true" fullscreen @size-change="handleSizeChange">
        <BmGridItem v-bind="layout[0]">
            <bm-grid :grid-options="gridOptions1"></bm-grid>
        </BmGridItem>
        <BmGridItem v-bind="layout[1]">
            <bm-form :form-options="formOptions" ref="formRef"></bm-form>
        </BmGridItem>
        <BmGridItem v-bind="layout[2]">
            <bm-grid :grid-options="gridOptions" ref="bmGridRef">
                <!-- 支持原生插槽
                <template #status>
                    <vxe-tag status="info">未开始</vxe-tag>
                    <vxe-tag status="success">已完成</vxe-tag>
                    <vxe-tag status="primary">待处理</vxe-tag>
                    <vxe-tag status="error">已驳回</vxe-tag>
                </template> -->
            </bm-grid>
        </BmGridItem>
    </BmGridLayout>
    <FormDialog v-bind="formDialogProps" />
    <FormDialog v-bind="leftFormDialogProps" />
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import BmGridLayout from '@/components/gridlayout/BmGridLayout.vue'
import BmGridItem from '@/components/gridlayout/BmGridItem.vue'
import BmGrid from '@/components/BmGrid.vue'
import { ExtendedGridProps } from '@/types/grid'
import { VxeUI, VxeTag } from 'vxe-pc-ui'
import { getDeptList, getDeptUserList, addDeptUser ,saveOrUpdateEntity} from '@/api/methods/dept'

import {postSaveDataForDept } from '@/api/methods/user'
import FormDialog, { type Props } from '@/components/FormDialog.vue'
import BmForm from '@/components/BmForm.vue'
import { ExtendedFormProps } from '@/types/form'
import axios from 'axios'
import { useUserStore } from '@/stores/user'
//当前选中的部门
let currentSelectDep
//当前选中的部门用户
let currentSelectDepUser
const layout = reactive([
    // 菜单区域
    { x: 0, y: 0, w: 2.99, h: 1, i: '0' },
    // 查询区域
    { x: 3, y: 0, w: 9, h: 0.198, i: '1' },
    // 表格区域
    { x: 3, y: 0.2, w: 9, h: 0.8, i: '2' },
])

let layoutSize = reactive({})

// 监听尺寸变化
const handleSizeChange = (size) => {
    console.log(size)
    layoutSize = size
}

//添加查询表单实例
const formRef = ref()

//添加表格实例
const bmGridRef = ref()

//左侧新增部门对话框配置
const  leftFormDialogProps = reactive<Props>({
    visible: false,
    title: '新增部门',
    mode: 'add',
    width: '40%',
    height: '50%',
    formProps: {
        titleWidth: 100,//标题宽度
        titleAlign: 'right',//标题对齐方式
        titleColon: true,//是否显示冒号
        items: [//表单项配置数组
            { field: 'deptName', title: '组织架构', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入组织架构名称' } } },//字段名,标题,跨度,渲染配置
            { field: 'alias', title: '组织架构', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入组织架构简称' } } },//字段名,标题,跨度,渲染配置
            { field: 'deptNo', title: '组织编号', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入组织编号' } } },//字段名,标题,跨度,渲染配置
            { field: 'creditCode', title: '统一社会信用码', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入统一社会信用码' } } },//字段名,标题,跨度,渲染配置
            { field: 'sort', title: '排序', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入排序' } } },//字段名,标题,跨度,渲染配置
            { field: 'remark', title: '描述', span: 24, itemRender: { 
                name: 'VxeSelect', props: { placeholder: '请输入描述' },
                // options: [
                //     { label: '1', value: '1' },
                //     { label: '2', value: '2' },
                //     { label: '3', value: '3' },
                // ],
                dict: '10001',//字典的值 一定要去控制台查看键值对 是否存在
            } },//字段名,标题,跨度,渲染配置
            { field: 'imgList1', title: '图片', span: 12, 
                itemRender: { 
                    name: 'VxeUpload', 
                    props: { 
                        placeholder: '请上传图片',
                        showUploadButton: true,
                        showPreviewButton: true,
                        showDownloadButton: true,
                        showProgress: true,
                        uploadMethod({ file, options }) {
                            alert('上传')
                            console.log(file)
                            console.log(options)
                        },
                        downloadMethod({ file, options }) {
                            alert('下载')
                        }
                    },
                    events: {
                        
                    }
                } 
            },//字段名,标题,跨度,渲染配置
        ],


        data: {


        },//表单数据
        formType: 'add',//表单类型
        applyData: async (data) => {
            // 提交数据
            // let res = await addDeptUser(data);
            // console.log(res)

        },
        getData: () => {
            // 获取数据
            // return getDeptUserList({ deptId: currentDeptId.value });
            return currentSelectDep
        },
        rules: {
            name: [
                { required: true, validator: 'ValidName' }
            ],
            nickname: [
                { required: true, validator: 'ValidNikeName' }
            ]
        },
    },
    buttons: {
        position: 'right',
        itemRenders: [
            {
                name: '取消',
                props: {
                    content: '取消',
                    type: 'default',
                    width: '100px'
                },
                events: {
                    click: (formData, formRef) => {
                        console.log('取消按钮点击', formRef)
                        // 可以访问表单数据和表单实例
                        leftFormDialogProps.visible = false
                    }
                }
            },
            {
                name: '确定',
                props: {
                    content: '确定',
                    type: 'primary',
                    width: '100px'
                },
                events: {
                    click: async (formData, formRef) => {
                        console.log('left确定按钮点击', formData)
                        // 可以进行表单验证等操作
                        leftFormDialogProps.visible = false
                        let rootId=currentSelectDep.rootId
                        console.log(JSON.stringify(currentSelectDep))
                        let pid
                        // if(currentSelectDep.pid!=0){
                        //      pid=currentSelectDep.deptId
                        // }
                        let level
                        if(leftFormDialogProps.mode=='add'){
                            level= ++currentSelectDep.level
                            pid=currentSelectDep.deptId
                        }else{
                            level= currentSelectDep.level
                            pid=currentSelectDep.pid
                        }



                        let createUserid=useUserStore().userInfo.userId
                        console.log("111---"+createUserid)
                        formData={
                            ...formData,rootId,pid,createUserid,level
                        }
                        const { _X_ROW_KEY:_, isFlagName,statusName,children,aliasName,deptNoName,creditCodeName,remarkName,deptNameName, ...filteredFormData } = formData;

                       let res = await saveOrUpdateEntity(filteredFormData)

                       if(leftFormDialogProps.mode=='add'){
                        window.location.reload();
                       }

                    }
                }
            },
            {
                name: '重置',
                props: {
                    content: '重置',
                    type: 'default',
                    width: '100px'
                },
                events: {
                    click: (formData, formRef) => {
                        formRef.resetForm()
                    }
                }
            }
        ]
    },
    onCancel: () => {
        leftFormDialogProps.visible = false
    },
    onSubmit: (data) => {
        // 处理提交逻辑

    }
})

// 右侧下半部分表单
//这里因为需要动态显示和隐藏,所以需要使用let声明
let toolbarTools = reactive<ExtendedFormProps>({
    // 表单项配置数组
    items: [
        {
            field: 'cardType',
            // title: '证件类型',
            span: 14,
            itemRender: {
                name: 'VxeRadioGroup',
                options: [
                    { label: '身份证', value: '1' },
                    { label: '护照', value: '2' }
                ],
                props: {
                    placeholder: '请选择证件类型',
                },
                events: {
                    change(value, formData, formRef) {
                        // console.log(value, formData)
                        console.log(formRef)
                        // formRef.submitForm()
                    }
                },
            },
            // visible: false
        },
        {
            field: 'username',
            // title: '证件类型',
            span: 4,
            itemRender: {
                name: 'VxeInput',
                props: {
                    placeholder: '请选择证件类型',
                },
                events: {
                    change(value, formData, formRef) {
                        // console.log(value, formData)
                    }
                }
            }
        },
        //这里为表单得字段按钮.
        {
            field: 'workCode',
            span: 6,
            itemRender: {
                name: 'VxeButton',
                props: {
                    type: 'primary',
                    content: '测试按钮'
                },
                events: {
                    // 参数1:表单数据,参数2:表单实例,参数3:组件实例
                    click(value, formRef) {
                        formRef.resetForm()

                    }
                }
            },
            // 这个函数仅在第一次加载的时候 生效,后面无论怎么修改都不生效
            visibleEvent: (data) => {
                // 获取表单内的数据
                console.log("visibleDataEvent", data)
                return true;
            }
        },
        {
            field: 'btn',
            span: 1,
            itemRender: {
                name: 'VxeButton',
                props: {
                    type: 'primary',
                    content: '新增'
                },
                events: {
                    // 参数1:表单数据,参数2:表单实例,参数3:组件实例
                    click(value, formData, formRef) {
                        if(currentSelectDep){
                            formDialogProps.mode='add'
                            formDialogProps.title='新增用户'
                            formDialogProps.visible=true
                        }else{
                            VxeUI.modal.message({
                                content: '请选择一个部门进行新增',
                                status: 'success'
                            })
                        }
                    }
                }
            },
            // 这个函数仅在第一次加载的时候 生效,后面无论怎么修改都不生效
            visibleEvent: (data) => {
                // 获取表单内的数据
                console.log("visibleDataEvent", data)
                return true;
            }
        },
    ],
    // 表单类型 这里决定了表格上面得工具栏是否能自动获取值
    formType: 'edit',
    // 获取数据的配置
    getData: () => {
        //这里支持 返回接口数据
        return {
            cardType: '1',
            workCode: '2',
            username: '张三',
        }
    },
    // 提交数据的函数
    applyData: async (data) => {
        // 这里可以实现提交数据的逻辑
        console.log('提交的数据:', data)
    },
    // 其他vxe-form的配置项
    titleWidth: 'auto',
    titleAlign: 'right',
    titleColon: true,
    titleOverflow: true,
    // 表单布局
    layout: 'inline',
    // 表单项间距
    itemGap: 10,
    // 是否显示冒号
    colon: true,
    // 是否显示必填星号
    showRequiredMark: true,
    // 校验规则
    rules: {
        sname: [
            { required: true, message: '请输入用户名' }
        ],
        phone: [
            { required: true, message: '请输入手机号' }
        ]
    },
    // 是否只读
    readonly: false,
})

//右侧上半部分表单配置,这里因为需要动态显示和隐藏,所以需要使用let声明
let formOptions = reactive<ExtendedFormProps>({
    // 表单项配置数组
    items: [
        {
            field: 'sname',
            title: '用户名',
            span: 6,
            itemRender: {
                name: 'VxeInput',
                props: {
                    placeholder: '请输入用户名'
                }
            },
            //拓展组件,支持组件宽度和事件以及显示效果
            itemRenders: [
                {
                    name: 'VxeButton',
                    props: {
                        width: '50%',
                        type: 'primary',
                        content: '重置按钮-1'
                    },
                    events: {
                        click(formData, formRef) {
                            console.log(formData)
                        }
                    }
                },
                {
                    name: 'VxeDatePicker',
                    props: {
                        width: '30%'
                    },
                    events: {
                        change(value, params, formRef) {
                            console.log(value, params)
                        }
                    }
                }
            ],
            visible: true,
            //原生组件,但是无法调整宽度和取值赋值麻烦
            // children: [
            //     {
            //         field: 'deptId1',
            //         span: 6,
            //         itemRender: {
            //             name: 'VxeDatePicker',
            //             events: {
            //                 change(value, params, formRef) {
            //                     console.log(value, params)
            //                 }
            //             }
            //         }
            //     }
            // ]
        },
        {
            field: 'phone',
            title: '手机号',
            span: 6,
            itemRender: {
                name: 'VxeInput',
                props: {
                    placeholder: '请输入手机号'
                }
            }
        },
        {
            field: 'cardType',
            title: '证件类型',
            span: 6,
            itemRender: {
                name: 'VxeSelect',
                options: [
                    { label: '身份证', value: '1' },
                    { label: '护照', value: '2' }
                ],
                // selectApi: 'http://172.16.0.172:8083/sys/temp/list',
                props: {
                    placeholder: '请选择证件类型'
                },
                events: {
                    change(value, params, formRef) {
                        console.log(value, params)
                    }
                }
            }
        },
        {
            field: 'cardNo',
            title: '证件号码',
            span: 6,
            itemRender: {
                name: 'VxeInput',
                props: {
                    placeholder: '请输入证件号码'
                }
            }
        },
        {
            field: 'deptId1',
            title: '部门',
            span: 6,
            itemRender: {
                name: 'VxeDatePicker',
            }
        },
        //这里为表单得字段按钮.
        {
            field: 'workCode',
            span: 6,
            itemRender: {
                name: 'VxeButton',
                props: {
                    type: 'primary',
                    content: '测试按钮-2'
                },
                events: {
                    // 参数1:表单数据,参数2:表单实例,参数3:组件实例
                    click(value, formRef) {
                        formRef.resetForm()
                    }
                }
            }
        },

    ],
    // 工具栏配置
    tools: {
        position: 'right',
        children: [
            {
                name: 'VxeButton',
                props: {
                    type: 'primary',
                    content: '搜索'
                },
                events: {
                    // 参数1:表单数据,参数2:表单实例
                    async click(formData, formRef) {
                        //  formRef.resetForm()
                        // console.log('表单已重置', formData)
                        alert('执行查询')

                        //根据deptId查询人员信息
                        let res = await getDeptUserList({ deptId: formData.deptId, queryChild: "false", currentPage:1,pageSize:2 })
                        // console.log(bmGridRef.value)
                        bmGridRef.value.getTableInstance().loadData(res.data.records)
                    }
                }
            },
            {
                name: 'VxeButton',
                props: {
                    type: 'primary',
                    content: '折叠'
                },
                events: {
                    // 参数1:表单数据,参数2:表单实例
                    click(formData, formRef) {
                        formRef.collapseForm()
                    }
                }
            },
            {
                name: 'VxeDatePicker',
                props: {
                    width: '120px'  // 可以使用px或%
                },
                events: {
                    change(formData, formRef) {
                        console.log('重置', formData)
                    }
                }
            },
            {
                name: 'VxeInput',
                props: {
                    type: 'primary',
                    content: '重置',
                    width: '120px'  // 可以使用px或%
                },
                events: {
                    change(formData, formRef) {
                        console.log('重置', formData)
                    }
                }
            }
        ]
    },
    // 表单类型
    formType: 'add',
    // 获取数据的配置
    getData: () => {
        //这里支持 返回接口数据
        return {
            sname: '11',
            phone: '1234567890',
            cardType: '1',
            cardNo: '1234567890',
            workCode: '1234567890'
        }
    },
    // 提交数据的函数
    applyData: async (data) => {
        // 这里可以实现提交数据的逻辑
        console.log('提交的数据:', data)
    },
    // 监听form表单得面板尺寸
    panelSize: (formSize) => {
        //变成折叠状态
        if (formSize.collapseStatus) {
            layout[1].h = layout[1].h + formSize.changeCoefficient?.height
            layout[2].y = layout[2].y + formSize.changeCoefficient?.height
            layout[2].h = layout[2].h - formSize.changeCoefficient?.height
        }
        //变成展开状态
        else {
            layout[1].h = formSize.originalCoefficient?.height
            // 这里得 0.002 相当于创造一个分割线
            layout[2].y = layout[1].h + 0.002
            layout[2].h = 1 - layout[2].y
        }
        console.log('面板尺寸:', formSize)
    },
    // 表单尺寸,把系数传导过来
    formSize: {
        // 原始尺寸 从layout中获取来获取
        originalCoefficient: { width: layout[1].w, height: layout[1].h },
        // 从页面元素提取器中查看
        itemHeight: 56.4
    },

    // 其他vxe-form的配置项
    titleWidth: 'auto',
    titleAlign: 'right',
    titleColon: true,
    titleOverflow: true,
    // 表单布局
    layout: 'inline',
    // 表单项间距
    itemGap: 10,
    // 是否显示冒号
    colon: true,
    // 是否显示必填星号
    showRequiredMark: true,
    // 校验规则
    rules: {
        sname: [
            { required: true, message: '请输入用户名' }
        ],
        phone: [
            { required: true, message: '请输入手机号' }
        ]
    },
    // 是否只读
    readonly: false,
})


const gridOptions = reactive<ExtendedGridProps>({
    border: true,//是否显示边框
    headerAlign: 'center',//表头对齐方式
    showOverflow: true,//是否显示溢出
    height: 'auto',//高度
    scrollY: { enabled: false },//是否开启滚动
    id: 'userId',//表格唯一标识
    keepSource: true,//是否保持原始数据
    customConfig: {
        storage: true//是否开启本地存储
    },
    rowConfig: {
        isHover: true,//是否开启行高亮
        keyField: '_id'//行唯一标识
    },
    sortConfig: {
        remote: true,//是否开启远程排序
        multiple: true//是否开启多列排序
    },
    editConfig: {
        mode: 'row',//编辑模式
        showStatus: true,//是否显示编辑状态
        enabled: false,//是否开启编辑
        beforeEditMethod({ row, column }) {

            return true
        }
    },
    checkboxConfig: {
        // labelField: 'name',
        highlight: true
    },
    // 工具栏配置,新增组件
    tools: toolbarTools,
    pagerConfig: {
        total: 10
    },
    columns: [
        { type: 'checkbox', width: 30, align: 'center' },
        { type: 'seq', width: 30, title: '序号', align: 'center' },
        { field: 'sname', title: '姓名', width: 100, visible: true },//字段名,标题,宽度,是否显示
        { field: 'phone', title: '手机号', minWidth: 80, sortable: true },//字段名,标题,最小宽度,是否排序
        { field: 'cardTypeName', title: '证件类型', minWidth: 80 },//字段名,标题,最小宽度
        { field: 'cardNo', title: '证件号码', minWidth: 100 },//字段名,标题,最小宽度
        { field: 'workCode', title: '员工编号', minWidth: 100 },//字段名,标题,最小宽度
        {
            field: 'action', title: '操作', fixed: 'right', width: 120,
            cellRenders: [
                {
                    name: 'VxeButton',
                    props: {
                        type: 'primary',
                        width: '50%',
                        content: '编辑'
                    },
                    events: {
                        click(cellParams, params) {
                            formDialogProps.mode='edit'
                            formDialogProps.title='编辑用户'
                            formDialogProps.visible=true
                            currentSelectDepUser=cellParams
                        }
                    }
                },
                {
                    name: 'VxeButton',
                    props: {
                        type: 'primary',
                        width: '50%',
                        content: '删除'
                    },
                    events: {
                        click(cellParams, params) {

                        }
                    }
                }
            ]
        },
        {
            field: 'imgList1', title: '状态', width: 500,
            cellRender: {
                name: 'VxeUpload',
                props: {
                    width: '80%',
                    multiple: true,//是否多选
                    showButtonText: false,//是否显示按钮文字
                    showProgress: true,//是否显示进度
                    autoHiddenButton: true,//超过最大数量后隐藏按钮
                    showDownloadButton: true,//是否显示下载按钮
                    dragSort: true,//是否开启拖拽排序
                    progressText: '{percent}%',//进度文字
                    fileTypes: ['pdf', 'xlsx'],//文件类型
                    limitCount: 1,//最大数量
                    nameField: 'name',//文件名
                    urlField: 'url',//文件地址
                    sizeField: 'size',//文件大小
                    moreConfig: {
                        maxCount: 1,
                        layout: 'horizontal',
                        showMoreButton: true
                    },
                    showPreview: true,//是否显示预览
                    uploadMethod({ file, updateProgress }) {
                        const formData = new FormData()
                        formData.append('file', file)
                        return axios.post('https://www.nenggongshe.com/file/group1/upload', formData, {
                            onUploadProgress(progressEvent) {
                                const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 0))
                                // 更新进度
                                updateProgress(percentCompleted)
                            }
                        }).then((res) => {
                            return {
                                ...res.data
                            }
                        })
                    },
                    previewMethod({ file, options }) {
                        alert('预览')
                        console.log(file)
                        console.log(options)
                    },
                    downloadMethod({ file, options }) {
                        alert('下载')
                        console.log(file)
                        console.log(options)
                    },

                },
                events: {
                    // 上传按钮事件
                    add(options, event) {
                        alert('添加文件')
                        console.log(options)
                        console.log(event.option)
                    },
                    // 上传成功事件
                    uploadSuccess(options, data, event) {
                        alert('上传成功')
                        console.log(data)
                    },
                    uploadError(options, data, event) {
                        alert('上传失败')
                        console.log(data)
                    },
                    // 下载按钮事件
                    download(options, event) {
                        //执行下载
                        alert('执行下载')
                        console.log(options)
                        console.log(event.option)
                    },
                    // 删除按钮事件
                    remove(options, event) {
                        //执行删除
                        alert('执行删除')
                        console.log(options)
                        console.log(event.option)
                    }
                }
            },
            // cellRenders: [
            //     {
            //         name: 'VxeUpload',
            //         props: {
            //             width: '80%',
            //             multiple: true,//是否多选
            //             showButtonText: false,//是否显示按钮文字
            //             showProgress: true,//是否显示进度
            //             autoHiddenButton: true,//超过最大数量后隐藏按钮
            //             showDownloadButton: true,//是否显示下载按钮
            //             dragSort: true,//是否开启拖拽排序
            //             progressText: '{percent}%',//进度文字
            //             fileTypes: ['pdf', 'xlsx'],//文件类型
            //             limitCount: 6,//最大数量
            //             nameField: 'name',//文件名
            //             urlField: 'url',//文件地址
            //             sizeField: 'size',//文件大小
            //             moreConfig: {
            //                 maxCount: 3,
            //                 layout: 'horizontal',
            //                 showMoreButton: true
            //             },
            //             uploadMethod({ file, updateProgress }) {
            //                 const formData = new FormData()
            //                 formData.append('file', file)
            //                 return axios.post('/api/pub/upload/single', formData, {
            //                     onUploadProgress(progressEvent) {
            //                         const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 0))
            //                         // 更新进度
            //                         updateProgress(percentCompleted)
            //                     }
            //                 }).then((res) => {
            //                     return {
            //                         ...res.data
            //                     }
            //                 })
            //             }
            //         }
            //     },
            //     {
            //         name: 'VxeText',
            //         props: {
            //             content: '0%',
            //             width: '20%'
            //         }
            //     }
            // ]
        },
        // { field: 'status', title: '状态', width: 300,dict: '20001'},
        { field: 'status', title: '状态', width: 300,
             cellRender:{
                name:'VxeSwitch',
                // name:'VxeInput',
                props:{
                    content:'内容',
                    width:'100%'
                },
                events:{
                    // click(value,params){
                    //     alert('切换')
                    // },
                    change(value,params){
                        alert('切换')
                    }
                }
            }
            // },
            //cellRenders:[
            //    {
            //        name:'VxeSwitch',
            //        props:{
            //            content:'内容',
            //            width:'100%'
            //        },
            //        events:{
            //            click(value,params){
            //                alert('切换')
            //            }
            //        }
            //    }
            //]
        },
    ],
    proxyConfig: {
        sort: true,//是否开启排序
        form: true,//是否开启表单
        response: {
            result: 'records',//返回数据
            total: 'totalRow'
        },
        ajax: {
            async query({ page, form, sorts }) {
                // 通过ref获取表单数据
                const formData = formRef.value?.getFormData()
                
                const params = {
                    deptId:"1849342376485720064",
                    queryChild:false,
                    pageNumber:page.currentPage,
                    ...page,
                    ...formData // 使用获取到的表单数据
                }
                let res = await getDeptUserList(params)
                return res.data
            }
        }
    }
})
//左半部分表单配置
const gridOptions1 = reactive<ExtendedGridProps>({
    headerAlign: 'center',//表头对齐方式
    border: false,//是否显示边框
    scrollY: { enabled: false },//是否开启滚动
    treeConfig: {
        rowField: 'deptId',//行唯一标识
        childrenField: 'children'//子节点字段名
    },
    columns: [
        {
            field: 'deptName', title: '部门列表', width: '70%', treeNode: true,//字段名,标题,宽度,是否树节点
            cellRender: {
                name: 'VxeText',//组件名称
                events: {
                    async click(cellParams, params) {
                        // currentSelectDep=cellParams.row
                        // //根据deptId查询人员信息
                        // let res = await getDeptUserList({ deptId: cellParams.row.deptId, queryChild: "false" })
                        // console.log(res.data.records)
                        // gridOptions.data = res.data.records

                        //修改formOption的下拉框
                        formOptions.items[2].itemRender.options=[
                            {   
                                label:'1',
                                value:'1'
                            },
                            {
                                label:'2',
                                value:'2'
                            }
                        ]
                    }
                }
            }
        },
        {
            field: 'action',//字段名
            title: '操作',//标题
            width: '30%',//宽度
            cellRenders: [//单元格渲染
                {
                    name: 'VxeIcon',
                    props: {
                        size: 'medium',//组件属性
                        name: 'add',//组件属性
                        width: '20%',//组件属性
                        status: 'primary'//组件属性
                    },
                    events: {
                        click(cellParams, params) {
                           leftFormDialogProps.title="新增部门"
                            leftFormDialogProps.mode='add'
                            currentSelectDep=cellParams
                            console.log(JSON.stringify(currentSelectDep))
                            leftFormDialogProps.visible=true
                        }
                    }
                },
                {
                    name: 'VxeIcon',
                    props: {
                        size: 'medium',
                        name: 'edit',
                        width: '20%',
                        status: 'success'
                    },
                    events: {
                        click(cellParams, params) {
                            leftFormDialogProps.title="编辑部门"
                            leftFormDialogProps.mode='edit'
                            currentSelectDep=cellParams
                            console.log(JSON.stringify(currentSelectDep))
                            leftFormDialogProps.visible=true
                        }
                    }
                },
                {
                    name: 'VxeIcon',
                    props: {
                        size: 'medium',
                        name: 'delete',
                        width: '20%',
                        status: 'warning'
                    },
                    events: {
                      async  click(cellParams, params) {
                            currentSelectDep=cellParams
                            currentSelectDep.isFlag='10001002'
                            let del={deptId:currentSelectDep.deptId,isFlag:currentSelectDep.isFlag}
                            let res = await saveOrUpdateEntity(del)
                            window.location.reload();
                            VxeUI.modal.message({
                                content: '删除成功',
                                status: 'success'
                            })
                        }
                    }
                }
            ]
        }
    ],
    height: "auto",
    proxyConfig: {
        sort: true,//是否开启排序
        form: true,//是否开启表单
        response: {
            result: 'data',//返回数据
        },
        ajax: {
            async query({ page, form, sorts }) {
                const params = {
                    ...page,
                    ...form
                }
                let res = await getDeptList(params)
                return res.data.records
            }
        }
    }
})
//新增部门用户表单
const formDialogProps = reactive<Props>({
    visible: false,
    title: '新增用户',
    mode: 'add',
    width: '40%',
    height: '50%',
    formProps: {
        titleWidth: 100,//标题宽度
        titleAlign: 'right',//标题对齐方式
        titleColon: true,//是否显示冒号
        items: [//表单项配置数组
            { field: 'sname', title: '姓名', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入姓名' } } },//字段名,标题,跨度,渲染配置
            { field: 'phone', title: '手机号', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入手机号' } } },//字段名,标题,跨度,渲染配置
            {
                field: 'cardType',
                title: '证件类型',
                span: 12,
                itemRender: {
                    name: 'VxeSelect',//组件名称
                    options: [//选项配置数组
                        { label: '身份证', value: '1' },
                        { label: '护照', value: '2' }
                    ]
                }
            },
            { field: 'cardNo', title: '证件号码', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入证件号码' } } },//字段名,标题,跨度,渲染配置
            { field: 'workCode', title: '员工编号', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入���工编号' } } }//字段名,标题,跨度,渲染配置
        ],
        data: {},//表单数据
        formType: 'add',//表单类型
        applyData: async (data) => {
            // 提交数据
            let res = await addDeptUser(data);
            console.log(res)
        },
        getData: () => {
            return currentSelectDepUser
            // 获取数据
            //return getDeptUserList({ deptId: currentDeptId.value });
        },
        rules: {
            name: [
                { required: true, validator: 'ValidName' }
            ],
            nickname: [
                { required: true, validator: 'ValidNikeName' }
            ]
        },
    },
    buttons: {
        position: 'right',
        itemRenders: [
            {
                name: '取消',
                props: {
                    content: '取消',
                    type: 'default',
                    width: '100px'
                },
                events: {
                    click: async (formData, formRef) => {
                        console.log('取消按钮点击', formData)
                        // 可以访问表单数据和表单实例
                        formDialogProps.visible = false

                    }
                }
            },
            {
                name: '确定',
                props: {
                    content: '确定',
                    type: 'primary',
                    width: '100px'
                },
                events: {
                    click: async (formData, formRef) => {
                        console.log('确定按钮点击--', formData)
                        // 可以进行表单验证等操作
                        formDialogProps.visible = false
                        let deptId =currentSelectDep.deptId
                        console.log('确定按钮点击--', deptId)
                        let createUserid=useUserStore().userInfo.userId
                        formData={...formData,deptId,createUserid}
                        console.log(JSON.stringify(formData))
                        postSaveDataForDept(formData)

                    }
                }
            },
            {
                name: '重置',
                props: {
                    content: '重置',
                    type: 'default',
                    width: '100px'
                },
                events: {
                    click: (formData, formRef) => {
                        // console.log('重置按钮点击', formData)
                        formRef.resetForm()
                    }
                }
            }
        ]
    },
    onCancel: () => {
        formDialogProps.visible = false
    },
    onSubmit: (data) => {
        // 处理提交逻辑
    }
})




</script>
<style scoped></style>
