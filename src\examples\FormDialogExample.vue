<template>
  <div class="example-container">
    <h2>FormDialog 组件示例</h2>
    <div class="buttons">
      <vxe-button @click="showFormDialog" type="primary">打开表单对话框</vxe-button>
      <vxe-button @click="setDialogSize('80%', '70%')" type="info">调整对话框大小</vxe-button>
    </div>

    <!-- 使用新的 FormDialog 组件 -->
    <form-dialog 
      v-model:formOptions="formDialogOptions"
      @confirm="handleFormConfirm"
      @cancel="handleFormCancel"
      ref="formDialogRef"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { VxeButton, VxeUI } from 'vxe-pc-ui'
import FormDialog from '@/components/FormDialog.vue'
import { FormDialogOptions } from '@/types/form-dialog'

// 表单对话框引用
const formDialogRef = ref()

// 表单对话框配置
const formDialogOptions = reactive<FormDialogOptions>({
  visible: false,
  title: '用户信息',
  width: '60%',
  height: '50%',
  loading: false,
  mode: 'add',
  showConfirmButton: true,
  showCancelButton: true,
  confirmButtonText: '保存',
  cancelButtonText: '取消',
  formProps: {
    titleWidth: 100,
    titleAlign: 'right',
    titleColon: true,
    items: [
      { 
        field: 'name', 
        title: '姓名', 
        span: 12, 
        itemRender: { 
          name: 'VxeInput', 
          props: { 
            placeholder: '请输入姓名' 
          } 
        } 
      },
      { 
        field: 'age', 
        title: '年龄', 
        span: 12, 
        itemRender: { 
          name: 'VxeInput', 
          props: { 
            type: 'number',
            placeholder: '请输入年龄' 
          } 
        } 
      },
      { 
        field: 'gender', 
        title: '性别', 
        span: 12, 
        itemRender: { 
          name: 'VxeSelect', 
          options: [
            { label: '男', value: 'male' },
            { label: '女', value: 'female' }
          ]
        } 
      },
      { 
        field: 'email', 
        title: '邮箱', 
        span: 12, 
        itemRender: { 
          name: 'VxeInput', 
          props: { 
            placeholder: '请输入邮箱' 
          } 
        } 
      },
      { 
        field: 'address', 
        title: '地址', 
        span: 24, 
        itemRender: { 
          name: 'VxeInput', 
          props: { 
            placeholder: '请输入地址' 
          } 
        } 
      },
      { 
        field: 'remark', 
        title: '备注', 
        span: 24, 
        itemRender: { 
          name: 'VxeTextarea', 
          props: { 
            placeholder: '请输入备注' 
          } 
        } 
      }
    ],
    getData: () => {
      // 模拟获取数据
      return {
        name: '',
        age: '',
        gender: '',
        email: '',
        address: '',
        remark: ''
      }
    },
    applyData: async (data) => {
      // 模拟提交数据
      console.log('提交的数据:', data)
      // 模拟异步操作
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(data)
        }, 1000)
      })
    }
  },
  buttons: {
    position: 'right',
    itemRenders: [
      {
        name: '重置',
        props: {
          type: 'default',
          content: '重置'
        },
        events: {
          click: (formData, formRef) => {
            formRef.formRef.reset()
          }
        }
      }
    ]
  }
})

// 显示表单对话框
const showFormDialog = () => {
  formDialogOptions.visible = true
}

// 设置对话框大小
const setDialogSize = (width, height) => {
  if (formDialogRef.value) {
    formDialogRef.value.setSize(width, height)
  }
}

// 处理表单确认
const handleFormConfirm = (data) => {
  console.log('表单确认:', data)
  VxeUI.modal.message({ content: '保存成功', status: 'success' })
}

// 处理表单取消
const handleFormCancel = () => {
  console.log('表单取消')
}
</script>

<style scoped>
.example-container {
  padding: 20px;
}

.buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}
</style>
