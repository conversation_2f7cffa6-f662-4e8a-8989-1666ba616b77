/**
* Description: 时间选择器hooks
* Created by huqingchao
* Date: 2024/10/15 10:28
* Update: 2024/10/15 10:28
*/
import {ref} from 'vue'
const usePickerOptions = () => {
  const datePickerOptions = ref({
    shortcuts: [
      {
        text: '最近一周',
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
          picker.$emit('pick', [start, end])
        }
      },
      {
        text: '最近一个月',
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
          picker.$emit('pick', [start, end])
        }
      },
      {
        text: '最近三个月',
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
          picker.$emit('pick', [start, end])
        }
      }
    ]
  })
  return {
    datePickerOptions
  }
}
export default usePickerOptions
