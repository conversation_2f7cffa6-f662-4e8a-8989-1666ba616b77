import {defineConfig, loadEnv} from 'vite'
import {compression} from 'vite-plugin-compression2'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'

import {lazyImport, VxeResolver} from 'vite-plugin-lazy-import'
import autoImportPlugin from '@opentiny/unplugin-tiny-vue'
import viteSvgPlugin from 'vite-svg-loader'


import {resolve} from 'path'

function pathResolve(dir) {
  return resolve(process.cwd(), '.', dir)
}

// 定义版本信息
const versionInfo = 'v1.0.0'; // 您可以根据需要更改版本信息


// https://vitejs.dev/config/
export default defineConfig(({command, mode}) => {
  // Load env file based on `mode` in the current working directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), '')
  return {
    base: env.VITE_PUBLIC_PATH,
    define: {
      'process.env': { ...process.env,TINY_MODE:'pc' }
    },
    assetsInclude: ['**/*.xlsx'],
    resolve: {
      alias: {
        '@': pathResolve('./src'),
      },
    },
    server: {
      host: '0.0.0.0',
      proxy : {
        '/uploadApi': {
          target: env.VITE_UPLOAD_URL,
          changeOrigin: true,
          rewrite: path => path.replace(/^\/uploadApi/, ''),
        },
        '/api': {
          target: env.VITE_REQUEST_URL,
          changeOrigin: true,
          rewrite: path => path.replace(/^\/api/, ''),
        },
      },
    },
    plugins: [
      vue(),
      vueJsx(),
      lazyImport({
        resolvers: [
          VxeResolver({
            libraryName: 'vxe-table'
          }),
          VxeResolver({
            libraryName: 'vxe-pc-ui'
          })
        ]
      }),
      autoImportPlugin('vite'),
      // 就是使用这个插件实现的文件压缩
      compression({
        threshold: 2000, // 设置只有超过 2k 的文件才执行压缩
        deleteOriginalAssets: false, // 设置是否删除原文件
        skipIfLargerOrEqual: true, // 如果压缩后的文件大小与原文件大小一致或者更大时，不进行压缩
        // 其他的属性暂不需要配置，使用默认即可
      }),
      viteSvgPlugin()
    ],

    //  主要看的就是这个 build 的部分
    build: {
      outDir: env.VITE_PUBLIC_PATH.replace('/', '') || 'dist',
      minify: "terser", // 这个是设置打包后的文件不压缩，方便查看
      assetsInlineLimit: 4096, //小于此阈值的导入或引用资源将内联为 base64 编码，以避免额外的 http 请求
      emptyOutDir: true, //Vite 会在构建时清空该目录
      terserOptions: {
        compress: {
          keep_infinity: true, // 防止 Infinity 被压缩成 1/0，这可能会导致 Chrome 上的性能问题
          drop_console: true, // 生产环境去除 console
          drop_debugger: true, // 生产环境去除 debugger
        },
        format: {
          comments: true, // 删除注释
        },
      },
      rollupOptions: {
        output: { // 打包输出的配置
          manualChunks: (id) => {
            // 这个ID，就是所有文件的绝对路径
            if (id.includes("node_modules")) {
              // 因为 node_modules 中的依赖通常是不会改变的
              // 所以直接单独打包出去
              // 这个return 的值就是打包的名称
              return "vendor";
            }
          }

          // // 配置rollup输出选项
          // // Static resource classification and packaging//静态资源分类打包
          // chunkFileNames: `assets/js/[name]-${visonInfo}-[hash].js`, //代码块文件名
          // entryFileNames: `assets/js/[name]-${visonInfo}-[hash].js`, //入口文件名
          // assetFileNames: `assets/[ext]/[name]-${visonInfo}-[hash].[ext]`, // 资源文件名
          // manualChunks(id) {
          //     if (id.includes('node_modules')) {
          //         const parts = id.toString().split('node_modules/');
          //         if (parts.length > 2) {
          //             return parts[2].split('/')[0].toString();
          //         }
          //     }
          // },

        }
      }
    }

  }
})
